import { describe, it, expect, vi } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useWindowState } from '../useWindowState'

// Mock Tauri window API
vi.mock('@tauri-apps/api/window', () => ({
  appWindow: {
    isMaximized: vi.fn().mockResolvedValue(false),
    isMinimized: vi.fn().mockResolvedValue(false),
    isVisible: vi.fn().mockResolvedValue(true),
    scaleFactor: vi.fn().mockResolvedValue(1),
    maximize: vi.fn().mockResolvedValue(undefined),
    unmaximize: vi.fn().mockResolvedValue(undefined),
    minimize: vi.fn().mockResolvedValue(undefined),
    close: vi.fn().mockResolvedValue(undefined),
  },
}))

vi.mock('@tauri-apps/api/event', () => ({
  listen: vi.fn().mockResolvedValue(vi.fn()),
}))

describe('useWindowState', () => {
  it('should initialize with default state', async () => {
    const { result } = renderHook(() => useWindowState())

    expect(result.current.windowState).toEqual({
      isMaximized: false,
      isMinimized: false,
      isVisible: true,
      scaleFactor: 1,
    })
  })
})
