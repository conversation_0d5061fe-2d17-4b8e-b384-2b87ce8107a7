import { describe, it, expect, vi } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useUpdater } from '../useUpdater'

// Mock Tauri updater plugin
vi.mock('@tauri-apps/plugin-updater', () => ({
  check: vi.fn().mockResolvedValue(null),
}))

vi.mock('@tauri-apps/plugin-process', () => ({
  relaunch: vi.fn().mockResolvedValue(undefined),
}))

describe('useUpdater', () => {
  it('should initialize with default state', async () => {
    // Mock check to return null (no update available)
    const { result } = renderHook(() => useUpdater())

    // Wait for the initial check to complete
    await new Promise((resolve) => setTimeout(resolve, 100))

    expect(result.current.updateAvailable).toBe(false)
    expect(result.current.updateInfo).toBeNull()
    expect(result.current.isChecking).toBe(false)
    expect(result.current.isDownloading).toBe(false)
  })
})
