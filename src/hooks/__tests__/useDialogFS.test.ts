import { describe, it, expect, vi } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useDialogFS } from '../useDialogFS'

// Mock Tauri dialog and fs plugins
vi.mock('@tauri-apps/plugin-dialog', () => ({
  open: vi.fn().mockResolvedValue(null),
  save: vi.fn().mockResolvedValue(null),
}))

vi.mock('@tauri-apps/plugin-fs', () => ({
  readTextFile: vi.fn().mockResolvedValue(''),
  writeTextFile: vi.fn().mockResolvedValue(undefined),
}))

describe('useDialogFS', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useDialogFS())

    expect(result.current.isOpening).toBe(false)
    expect(result.current.isSaving).toBe(false)
    expect(result.current.error).toBeNull()
  })
})
