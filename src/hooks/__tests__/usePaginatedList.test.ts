import { describe, it, expect } from 'vitest'
import { renderHook } from '@testing-library/react'
import { usePaginatedList } from '../usePaginatedList'
import { usePaginatedList } from '../usePaginatedList'

describe('usePaginatedList', () => {
  it('should paginate data correctly', () => {
    const data = Array.from({ length: 20 }, (_, i) => ({ id: i, name: `Item ${i}` }))
    const { result } = renderHook(() => usePaginatedList(data, 10))

    expect(result.current.data).toHaveLength(10)
    expect(result.current.totalPages).toBe(2)
  })
})
