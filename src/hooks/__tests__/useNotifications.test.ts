import { describe, it, expect, vi } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useNotifications } from '../useNotifications'

// Mock Tauri notification plugin
vi.mock('@tauri-apps/plugin-notification', () => ({
  requestPermission: vi.fn().mockResolvedValue('default'),
  sendNotification: vi.fn().mockResolvedValue(undefined),
  isPermissionGranted: vi.fn().mockResolvedValue(true),
}))

describe('useNotifications', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useNotifications())

    expect(result.current.permission).toBe('default')
    expect(result.current.isRequesting).toBe(false)
    expect(result.current.error).toBeNull()
  })
})
