import { describe, it, expect, vi } from 'vitest'
import { renderHook } from '@testing-library/react'
import { useClipboard } from '../useClipboard'

// Mock Tauri clipboard plugin
vi.mock('@tauri-apps/plugin-clipboard-manager', () => ({
  writeText: vi.fn().mockResolvedValue(undefined),
  readText: vi.fn().mockResolvedValue(''),
}))

describe('useClipboard', () => {
  it('should initialize with default state', () => {
    const { result } = renderHook(() => useClipboard())

    expect(result.current.isCopying).toBe(false)
    expect(result.current.isReading).toBe(false)
    expect(result.current.error).toBeNull()
  })
})
