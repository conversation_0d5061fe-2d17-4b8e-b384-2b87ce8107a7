import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'
import type { User, LoginCredentials, AuthResponse } from '@/shared/types'

// In production, this should be in environment variables
const JWT_SECRET = 'your-super-secret-jwt-key-change-in-production'
const JWT_EXPIRES_IN = '24h'
const SALT_ROUNDS = 12

export interface JWTPayload {
  userId: string
  email: string
  role: string
  iat?: number
  exp?: number
}

export class SecurityService {
  /**
   * Hash a password using bcrypt
   */
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, SALT_ROUNDS)
  }

  /**
   * Compare a password with its hash
   */
  static async comparePassword(password: string, hash: string): Promise<boolean> {
    return bcrypt.compare(password, hash)
  }

  /**
   * Generate a JWT token
   */
  static generateToken(user: User): string {
    const payload: JWTPayload = {
      userId: user.id,
      email: user.email,
      role: user.role,
    }

    return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
  }

  /**
   * Verify and decode a JWT token
   */
  static verifyToken(token: string): JWTPayload | null {
    try {
      const decoded = jwt.verify(token, JWT_SECRET) as JWTPayload
      return decoded
    } catch (error) {
      console.error('Token verification failed:', error)
      return null
    }
  }

  /**
   * Check if a token is expired
   */
  static isTokenExpired(token: string): boolean {
    try {
      const decoded = jwt.decode(token) as JWTPayload
      if (!decoded || !decoded.exp) return true

      const now = Math.floor(Date.now() / 1000)
      return decoded.exp < now
    } catch {
      return true
    }
  }

  /**
   * Validate password strength
   */
  static validatePasswordStrength(password: string): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }

    if (password.length > 128) {
      errors.push('Password must be less than 128 characters long')
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter')
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter')
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number')
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('Password must contain at least one special character')
    }

    // Check for common patterns
    const commonPatterns = [/password/i, /123456/, /qwerty/i, /admin/i, /letmein/i]

    for (const pattern of commonPatterns) {
      if (pattern.test(password)) {
        errors.push('Password cannot contain common patterns')
        break
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
    }
  }

  /**
   * Generate a secure random token for password reset
   */
  static generateResetToken(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    let token = ''
    for (let i = 0; i < 32; i++) {
      token += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return token
  }

  /**
   * Sanitize user input to prevent XSS
   */
  static sanitizeInput(input: string): string {
    return input.replace(/</g, '').replace(/>/g, '').trim().slice(0, 1000) // Limit length
  }

  /**
   * Rate limiting implementation (in-memory, for production use Redis)
   */
  private static rateLimitMap = new Map<string, { count: number; resetTime: number }>()

  static isRateLimited(identifier: string, maxAttempts = 5, windowMs = 15 * 60 * 1000): boolean {
    const now = Date.now()
    const record = this.rateLimitMap.get(identifier)

    if (!record || now > record.resetTime) {
      this.rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs })
      return false
    }

    if (record.count >= maxAttempts) {
      return true
    }

    record.count++
    return false
  }

  /**
   * Get remaining rate limit attempts
   */
  static getRateLimitInfo(
    identifier: string,
    windowMs = 15 * 60 * 1000
  ): {
    remaining: number
    resetTime: number
  } {
    const record = this.rateLimitMap.get(identifier)
    const now = Date.now()

    if (!record || now > record.resetTime) {
      return { remaining: 5, resetTime: now + windowMs }
    }

    return {
      remaining: Math.max(0, 5 - record.count),
      resetTime: record.resetTime,
    }
  }

  /**
   * Clear rate limit for a user
   */
  static clearRateLimit(identifier: string): void {
    this.rateLimitMap.delete(identifier)
  }
}
