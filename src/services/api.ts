import { TauriAPI } from './tauriAPI'
import type { ApiError, Paginated } from '../shared/types/api'
import type { DashboardStats, Trainee, Assessment, TrainingProgram } from '../shared/types'

// Define Result type that matches our ApiError
export type Result<T> = { ok: true; data: T } | { ok: false; error: ApiError }

export async function toResult<T>(promiseFactory: () => Promise<T>): Promise<Result<T>> {
  try {
    const data = await promiseFactory()
    return { ok: true, data }
  } catch (e) {
    const message = e instanceof Error ? e.message : String(e)
    const error: ApiError = { success: false, error: message, code: 'API_ERROR' }
    return { ok: false, error }
  }
}

// Dashboard
export const getDashboardStatsResult = () =>
  toResult<DashboardStats>(() => TauriAPI.getDashboardStats())

// Trainees
export const getTraineesResult = () => toResult<Trainee[]>(() => TauriAPI.getTrainees())
export const saveTraineeResult = (trainee: Trainee) =>
  toResult<void>(() => TauriAPI.saveTrainee(trainee))

// Assessments
export const getAssessmentsResult = (traineeId?: string) =>
  toResult<Assessment[]>(() => TauriAPI.getAssessments(traineeId))
export const saveAssessmentResult = (assessment: Assessment) =>
  toResult<void>(() => TauriAPI.saveAssessment(assessment))

// Training Programs
export const getTrainingProgramsResult = () =>
  toResult<TrainingProgram[]>(() => TauriAPI.getTrainingPrograms())
export const saveTrainingProgramResult = (program: TrainingProgram) =>
  toResult<void>(() => TauriAPI.saveTrainingProgram(program))

// Utility: Example of paginated client-side helper (can be used with large arrays until server pagination exists)
export function paginate<T>(items: T[], page = 1, pageSize = 10): Paginated<T> {
  const start = (page - 1) * pageSize
  const end = start + pageSize
  return {
    data: items.slice(start, end),
    pagination: {
      page,
      pageSize,
      total: items.length,
      totalPages: Math.ceil(items.length / pageSize),
    },
  }
}
