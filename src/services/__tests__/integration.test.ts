import { describe, it, expect, vi } from 'vitest'
import { TauriAPI } from '../tauriAPI'

// Mock the Tauri invoke function
vi.mock('@tauri-apps/api/core', () => ({
  invoke: vi.fn(),
}))

describe('TauriAPI Integration', () => {
  it('should have all the required methods', () => {
    expect(typeof TauriAPI.getDashboardStats).toBe('function')
    expect(typeof TauriAPI.getTrainees).toBe('function')
    expect(typeof TauriAPI.saveTrainee).toBe('function')
    expect(typeof TauriAPI.getAssessments).toBe('function')
    expect(typeof TauriAPI.saveAssessment).toBe('function')
    expect(typeof TauriAPI.getUsers).toBe('function')
    expect(typeof TauriAPI.saveUser).toBe('function')
    expect(typeof TauriAPI.getTrainingPrograms).toBe('function')
    expect(typeof TauriAPI.saveTrainingProgram).toBe('function')
    expect(typeof TauriAPI.login).toBe('function')
    expect(typeof TauriAPI.logout).toBe('function')
    expect(typeof TauriAPI.getCurrentUser).toBe('function')
    expect(typeof TauriAPI.getSupportTeams).toBe('function')
    expect(typeof TauriAPI.getSupportTeam).toBe('function')
    expect(typeof TauriAPI.createSupportTeam).toBe('function')
    expect(typeof TauriAPI.updateSupportTeam).toBe('function')
    expect(typeof TauriAPI.deleteSupportTeam).toBe('function')
    expect(typeof TauriAPI.getReports).toBe('function')
    expect(typeof TauriAPI.generateReport).toBe('function')
    expect(typeof TauriAPI.deleteReport).toBe('function')
    expect(typeof TauriAPI.getTraineeAnalytics).toBe('function')
    expect(typeof TauriAPI.getAssessmentAnalytics).toBe('function')
    expect(typeof TauriAPI.isTauriAvailable).toBe('function')
  })
})
