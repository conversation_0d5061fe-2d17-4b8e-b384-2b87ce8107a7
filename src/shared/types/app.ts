// App-level shared types aligned with existing store and permissions

export interface DashboardStats {
  users: number
  trainees: number
  assessments: number
  programs: number
  totalTrainees: number
  activeTrainees: number
  completedTrainees: number
  pendingAssessments: number
  overdueAssessments: number
  averageProgress: number
  programsByType: Record<string, number>
  assessmentsByQuarter: Record<string, number>
  lastUpdated?: string
}
