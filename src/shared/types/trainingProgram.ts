import type { EntityId, ISODateString } from './common'

/**
 * Training session interface for individual training sessions
 */
export interface TrainingSession {
  id: EntityId
  title: string
  description?: string
  startDate: ISODateString
  endDate: ISODateString
  instructor?: string
  location?: string
  capacity?: number
  enrolledCount?: number
  resources?: string[]
  status?: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
  type?: 'lecture' | 'lab' | 'workshop' | 'assessment' | 'review'
}

/**
 * Training milestone interface for important deadlines and achievements
 */
export interface TrainingMilestone {
  id: EntityId
  title: string
  description?: string
  dueDate: ISODateString
  type?: 'assessment' | 'project' | 'certification' | 'review' | 'deadline'
  status?: 'pending' | 'completed' | 'overdue'
  weight?: number // For progress calculation
  dependencies?: EntityId[] // Other milestone IDs that must be completed first
}

/**
 * Training schedule interface containing sessions and timeline
 */
export interface TrainingSchedule {
  id: EntityId
  programId: EntityId
  startDate: ISODateString
  endDate: ISODateString
  sessions: TrainingSession[]
  milestones: TrainingMilestone[]
  timezone?: string
  recurringPattern?: {
    frequency: 'daily' | 'weekly' | 'monthly'
    interval: number
    daysOfWeek?: number[] // 0-6 (Sunday-Saturday)
    endDate?: ISODateString
  }
}

/**
 * Enhanced TrainingProgram interface with schedule and milestones
 */
export interface TrainingProgram {
  id: EntityId
  name: string
  durationMonths: number
  description?: string
  createdAt?: ISODateString
  updatedAt?: ISODateString
  traineesCount?: number
  assessmentsCount?: number

  // Enhanced training program properties
  status?: 'draft' | 'active' | 'completed' | 'suspended'
  instructor?: string
  department?: string
  budget?: number
  prerequisites?: string[]
  learningObjectives?: string[]

  // Schedule and milestones
  schedule?: TrainingSchedule
  progress?: {
    completedSessions: number
    totalSessions: number
    completedMilestones: number
    totalMilestones: number
    overallPercentage: number
  }

  // Resource allocation
  resources?: {
    rooms?: string[]
    equipment?: string[]
    materials?: string[]
  }

  // Enrollment and capacity
  maxCapacity?: number
  currentEnrollment?: number
  waitlistCount?: number
}

/**
 * Training program enrollment interface
 */
export interface TrainingProgramEnrollment {
  id: EntityId
  programId: EntityId
  traineeId: EntityId
  enrolledAt: ISODateString
  status?: 'enrolled' | 'completed' | 'dropped' | 'suspended'
  progress?: number
  completedSessions?: EntityId[]
  completedMilestones?: EntityId[]
  notes?: string
}

/**
 * Training program filter options
 */
export interface TrainingProgramFilter {
  status?: TrainingProgram['status'][]
  instructor?: string
  department?: string
  dateRange?: {
    start: Date
    end: Date
  }
  hasCapacity?: boolean
  durationRange?: {
    min: number
    max: number
  }
}
