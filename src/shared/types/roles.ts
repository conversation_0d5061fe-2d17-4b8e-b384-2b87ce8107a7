import type { EntityId } from './common'

export type PermissionKey =
  | 'users.read'
  | 'users.write'
  | 'users.manage'
  | 'trainees.read'
  | 'trainees.write'
  | 'trainingPrograms.read'
  | 'trainingPrograms.write'
  | 'assessments.read'
  | 'assessments.write'
  | 'reports.read'
  | 'reports.export'
  | 'supportTeams.read'
  | 'supportTeams.write'
  | 'dashboard.view'

export interface Role {
  id: EntityId
  name: 'Admin' | string
  permissions: PermissionKey[]
  description?: string
}

export type UserRole = 'admin' | 'ld_officer' | 'manager' | 'direct_manager' | 'mentor' | 'trainee'

export interface User {
  id: EntityId
  name: string
  email: string
  role: UserRole
  permissions?: PermissionKey[]
  status?: string
  lastLogin?: string
  createdAt?: string
  updatedAt?: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface AuthResponse {
  token: string
  user: User
  expiresAt: string
}
