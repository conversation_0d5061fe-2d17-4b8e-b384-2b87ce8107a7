import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import { ConfirmationDialog } from '../ConfirmationDialog'

describe('ConfirmationDialog', () => {
  it('should render with title and description', () => {
    const onOpenChange = vi.fn()
    const onConfirm = vi.fn()

    render(
      <ConfirmationDialog
        open={true}
        onOpenChange={onOpenChange}
        title="Test Title"
        description="Test Description"
        onConfirm={onConfirm}
      />
    )

    expect(screen.getByText('Test Title')).toBeInTheDocument()
    expect(screen.getByText('Test Description')).toBeInTheDocument()
  })
})
