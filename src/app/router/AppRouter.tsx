import { lazy, Suspense } from 'react'
import { createBrowserRouter, Navigate, RouterProvider } from 'react-router-dom'
import { ThemeProvider } from '@/shared/components/ThemeProvider'
import App from '@/App'
import { ErrorBoundary } from '@/shared/components/ErrorBoundary'
import RouteError from '@/shared/components/RouteError'
import NotFound from '@/shared/components/NotFound'

const TraineeManagement = lazy(() => import('@/features/trainees/components/TraineeManagement'))
const TrainingProgramManagement = lazy(
  () => import('@/features/training-programs/components/TrainingProgramManagement')
)
const AssessmentManagement = lazy(
  () => import('@/features/assessments/components/AssessmentManagement')
)
const ReportManagement = lazy(() => import('@/features/reports/components/ReportManagement'))
const SupportTeamManagement = lazy(
  () => import('@/features/support-teams/components/SupportTeamManagement')
)

function Loader() {
  return <div className="text-muted-foreground text-sm">Loading...</div>
}

const router = createBrowserRouter([
  {
    path: '/',
    element: (
      <ErrorBoundary>
        <App />
      </ErrorBoundary>
    ),
    errorElement: <RouteError />,
    children: [
      {
        index: true,
        element: <Navigate to="/dashboard" replace />,
      },

      {
        path: 'trainees',
        element: (
          <Suspense fallback={<Loader />}>
            <TraineeManagement />
          </Suspense>
        ),
      },
      {
        path: 'training-programs',
        element: (
          <Suspense fallback={<Loader />}>
            <TrainingProgramManagement />
          </Suspense>
        ),
      },
      {
        path: 'assessments',
        element: (
          <Suspense fallback={<Loader />}>
            <AssessmentManagement />
          </Suspense>
        ),
      },
      {
        path: 'reports',
        element: (
          <Suspense fallback={<Loader />}>
            <ReportManagement />
          </Suspense>
        ),
      },
      {
        path: 'support-teams',
        element: (
          <Suspense fallback={<Loader />}>
            <SupportTeamManagement />
          </Suspense>
        ),
      },
      { path: '*', element: <NotFound /> },
    ],
  },
])

export function AppRouter() {
  return (
    <ThemeProvider>
      <RouterProvider router={router} />
    </ThemeProvider>
  )
}

export default AppRouter
