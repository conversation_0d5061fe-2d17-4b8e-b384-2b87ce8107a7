import { useState, useEffect, useRef } from 'react'
import { ErrorBoundary } from '@/shared/components/ErrorBoundary'
import { ReportManagement } from './features/reports/components/ReportManagement'
import { SupportTeamManagement } from './features/support-teams/components/SupportTeamManagement'

import FullCalendarScheduler from './features/calendar/components/FullCalendarScheduler'
import './features/calendar/styles/calendar.css'
import { TrainingBudgetManagement } from './features/training-budgets/components/TrainingBudgetManagement'
import { UserManagement } from './features/users/components/UserManagement'
import { useAuth } from './contexts/AuthContext'
import {
  BarChart3,
  UserPlus,
  BookOpen,
  ClipboardCheck,
  FileText,
  Calendar,
  Settings,
  PanelLeft,
  DollarSign,
  Users,
  LogOut,
  User,
  UserCheck,
} from 'lucide-react'
import { Button } from '@/shared/components/ui/button'
import { Separator } from '@/shared/components/ui/separator'
import { useIsMobile } from '@/shared/hooks/use-mobile'

import './App.css'
/**
 * Remove any third-party “license key” banners/notices injected into the DOM.
 * Some libraries render a floating div with text like “Your licence key …”.
 * We defensively remove such elements on load and when new nodes are added.
 */
function useRemoveLicenseKeyDiv() {
  useEffect(() => {
    const matchesLicenseText = (text: string | null | undefined) => {
      if (!text) return false
      const t = text.toLowerCase()
      return (
        t.includes('your licence key') ||
        t.includes('your license key') ||
        (t.includes('license') && t.includes('key')) ||
        (t.includes('licence') && t.includes('key'))
      )
    }

    const tryRemove = () => {
      const allDivs = document.querySelectorAll('div')
      allDivs.forEach((el) => {
        if (matchesLicenseText(el.textContent)) {
          // Remove the element entirely; if removal fails, hide it
          try {
            el.remove()
          } catch {
            ;(el as HTMLElement).style.display = 'none'
          }
        }
      })
    }

    // Initial cleanup
    tryRemove()

    // Observe future DOM changes (e.g., notices injected after mount)
    const observer = new MutationObserver(() => tryRemove())
    observer.observe(document.body, { childList: true, subtree: true })

    return () => observer.disconnect()
  }, [])
}

type ActiveTab =
  | 'dashboard'
  | 'users'
  | 'trainees'
  | 'training-programs'
  | 'assessments'
  | 'reports'
  | 'calendar'
  | 'training-budgets'
  | 'support-teams'
  | 'profile'

const navItems: {
  id: ActiveTab
  label: string
  icon: React.ComponentType<{ className?: string }>
}[] = [
  { id: 'dashboard', label: 'Learning & Development', icon: BarChart3 },
  { id: 'users', label: 'Users', icon: Users },
  { id: 'trainees', label: 'Trainees', icon: UserPlus },
  { id: 'training-programs', label: 'Programs', icon: BookOpen },
  { id: 'assessments', label: 'Assessments', icon: ClipboardCheck },
  { id: 'reports', label: 'Reports', icon: FileText },
  { id: 'calendar', label: 'Calendar', icon: Calendar },
  { id: 'training-budgets', label: 'Training Budgets', icon: DollarSign },
  { id: 'support-teams', label: 'Support Teams', icon: UserCheck },
]

function App() {
  // Remove any injected "your licence key" divs
  useRemoveLicenseKeyDiv()
  const { user, logout } = useAuth()
  const [activeTab, setActiveTab] = useState<ActiveTab>('dashboard')
  const [sidebarOpen, setSidebarOpen] = useState(true)
  const [isInitializing, setIsInitializing] = useState(true)
  const [initProgress, setInitProgress] = useState(0)
  const [initMessage, setInitMessage] = useState('Initializing application...')
  const isMobile = useIsMobile()
  const isInitializingRef = useRef(false)

  const handleLogout = async () => {
    await logout()
  }

  // Actual Tauri initialization
  useEffect(() => {
    // Prevent multiple initializations using ref
    if (isInitializingRef.current) return
    isInitializingRef.current = true

    // Set initial progress immediately
    setInitProgress(0)
    setInitMessage('Initializing application...')

    const initializeApp = async () => {
      try {
        console.log('Starting app initialization...')
        // Small delay to ensure UI renders
        await new Promise((resolve) => setTimeout(resolve, 10))

        setInitMessage('Initializing...')
        setInitProgress(20)

        // Skip Tauri initialization for now to avoid getting stuck
        setInitProgress(50)
        setInitMessage('Loading components...')

        setInitProgress(80)
        setInitMessage('Finalizing setup...')

        setInitProgress(100)
        setInitMessage('Ready!')
        console.log('Initialization complete')

        // Small delay before removing splash
        setTimeout(() => {
          console.log('About to set isInitializing to false')
          setIsInitializing(false)
          console.log('Set isInitializing to false')
        }, 300)
      } catch (error) {
        console.error('Failed to initialize application:', error)
        setInitMessage('Initialization failed. Please restart the application.')
        console.log('Error details:', error)
        // Even on error, we should show the UI after some time
        setTimeout(() => setIsInitializing(false), 3000)
      }
    }

    initializeApp()

    // Force timeout after 5 seconds to prevent infinite loading
    const forceTimeout = setTimeout(() => {
      console.log('Forcing app to show after timeout')
      setIsInitializing(false)
    }, 5000)

    return () => clearTimeout(forceTimeout)
  }, []) // Empty dependency array ensures this runs only once // Empty dependency array ensures this runs only once

  // Set initial sidebar state based on mobile
  useEffect(() => {
    setSidebarOpen(!isMobile)
  }, [isMobile])

  const renderActiveContent = () => {
    switch (activeTab) {
      case 'dashboard':
      case 'trainees':
      case 'training-programs':
      case 'assessments':
      case 'profile':
        return <div>Dashboard</div>
      case 'users':
        return <UserManagement />
      case 'reports':
        return <ReportManagement />
      case 'calendar':
        return <FullCalendarScheduler />
      case 'training-budgets':
        return <TrainingBudgetManagement />
      case 'support-teams':
        return <SupportTeamManagement />
      default:
        return <div>Dashboard</div>
    }
  }

  if (isInitializing) {
    return (
      <div className="bg-background flex h-screen w-full items-center justify-center">
        <div className="w-full max-w-md space-y-6 p-8">
          <div className="flex flex-col items-center space-y-4">
            <div className="bg-primary text-primary-foreground flex aspect-square size-16 items-center justify-center rounded-lg">
              <BarChart3 className="size-8" />
            </div>
            <div className="text-center">
              <h1 className="text-2xl font-bold">GT-EGA</h1>
              <p className="text-muted-foreground">Training Management System</p>
            </div>
          </div>

          <div className="space-y-2">
            <div className="bg-secondary h-2 w-full rounded-full">
              <div
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${initProgress}%` }}
              ></div>
            </div>
            <p className="text-muted-foreground text-center text-sm">{initMessage}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div className="flex h-screen w-full overflow-hidden">
        {/* Sidebar */}
        <div
          className={`${sidebarOpen ? 'w-64' : 'w-0'} bg-background overflow-hidden border-r transition-all duration-300 ease-in-out`}
        >
          <div className="flex h-full w-64 flex-col">
            <div className="border-b p-4">
              <div className="flex items-center gap-2">
                <div className="bg-primary text-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                  <BarChart3 className="size-4" />
                </div>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-semibold">GT-EGA</span>
                  <span className="truncate text-xs">Training Management</span>
                </div>
              </div>
            </div>
            <div className="flex-1 overflow-y-auto py-4">
              <nav className="space-y-1 px-3" role="navigation" aria-label="Main navigation">
                {navItems.map((item) => {
                  const Icon = item.icon
                  const active = activeTab === item.id
                  return (
                    <button
                      key={item.id}
                      onClick={() => {
                        setActiveTab(item.id)
                        if (isMobile) {
                          setSidebarOpen(false)
                        }
                      }}
                      className={`flex w-full items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors ${
                        active
                          ? 'bg-primary text-primary-foreground'
                          : 'hover:bg-accent hover:text-accent-foreground'
                      }`}
                      aria-current={active ? 'page' : undefined}
                      role="menuitem"
                    >
                      <Icon className="h-4 w-4" />
                      <span>{item.label}</span>
                    </button>
                  )
                })}
              </nav>
            </div>
            <div className="border-t p-4">
              <div className="text-muted-foreground flex items-center gap-2 px-2 py-1 text-xs">
                <span>GT-EGA © 2024</span>
                <span>Learning & Development Management System</span>
                <span>Version 1.0.0</span>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex flex-1 flex-col overflow-hidden">
          <header className="bg-background/95 supports-[backdrop-filter]:bg-background/60 sticky top-0 z-50 flex h-14 items-center gap-2 border-b px-4 backdrop-blur">
            <div className="flex flex-1 items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={() => setSidebarOpen(!sidebarOpen)}
                aria-label={sidebarOpen ? 'Collapse sidebar' : 'Expand sidebar'}
                aria-expanded={sidebarOpen}
              >
                <PanelLeft className="h-4 w-4" />
              </Button>
              <Separator orientation="vertical" className="mr-2 h-4" />
              <h1 className="text-lg font-semibold">GT-EGA</h1>
            </div>
            <div className="flex items-center gap-2">
              {/* User Info */}
              <div className="mr-2 hidden items-center gap-2 md:flex">
                <User className="h-4 w-4 text-gray-500" />
                <div className="flex flex-col">
                  <span className="text-sm font-medium">{user?.name}</span>
                  <span className="text-muted-foreground text-xs">
                    {user?.role.replace('_', ' ')}
                  </span>
                </div>
              </div>
              <Separator orientation="vertical" className="hidden h-4 md:block" />
              <Button variant="ghost" size="icon" className="h-8 w-8" aria-label="Settings">
                <Settings className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8"
                onClick={handleLogout}
                aria-label="Logout"
                title="Logout"
              >
                <LogOut className="h-4 w-4" />
              </Button>
            </div>
          </header>
          <main className="flex-1 overflow-y-auto">
            <div className="container mx-auto p-6">{renderActiveContent()}</div>
          </main>
        </div>
      </div>
    </ErrorBoundary>
  )
}

export default App
