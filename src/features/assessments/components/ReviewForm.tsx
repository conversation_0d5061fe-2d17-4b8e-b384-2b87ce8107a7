import { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { Badge } from '@/shared/components/ui/badge'
import { useAssessments } from '../hooks/useAssessments'
import { Save, X, Star, MessageSquare, TrendingUp, TrendingDown } from 'lucide-react'
import type { Assessment, SubmitReviewForm } from '@/shared/types'

interface ReviewFormProps {
  assessment: Assessment
  onSuccess?: () => void
  onCancel?: () => void
}

export function ReviewForm({ assessment, onSuccess, onCancel }: ReviewFormProps) {
  const { submitReview } = useAssessments()

  const [formData, setFormData] = useState({
    technicalScore: '',
    softSkillsScore: '',
    onJobScore: '',
    comments: '',
    strengths: '',
    improvements: '',
  })

  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  // Always allow review submission since we removed auth
  const existingReview = assessment.reviews?.[0] // Just take the first review if it exists
  const hasReviewed = false

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    const technicalScore = parseInt(formData.technicalScore)
    const softSkillsScore = parseInt(formData.softSkillsScore)
    const onJobScore = parseInt(formData.onJobScore)

    if (isNaN(technicalScore) || technicalScore < 0 || technicalScore > 100) {
      newErrors.technicalScore = 'Technical score must be between 0 and 100'
    }

    if (isNaN(softSkillsScore) || softSkillsScore < 0 || softSkillsScore > 100) {
      newErrors.softSkillsScore = 'Soft skills score must be between 0 and 100'
    }

    if (isNaN(onJobScore) || onJobScore < 0 || onJobScore > 100) {
      newErrors.onJobScore = 'On-job score must be between 0 and 100'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      const reviewData: SubmitReviewForm = {
        assessmentId: assessment.id,
        reviewerId: 'reviewer-1', // Default reviewer ID since we removed auth
        reviewerRole: 'ld_officer',
        technicalScore: parseInt(formData.technicalScore),
        softSkillsScore: parseInt(formData.softSkillsScore),
        onJobScore: parseInt(formData.onJobScore),
        comments: formData.comments.trim() || undefined,
        strengths: formData.strengths.trim()
          ? formData.strengths.split('\n').filter((s) => s.trim())
          : undefined,
        improvements: formData.improvements.trim()
          ? formData.improvements.split('\n').filter((s) => s.trim())
          : undefined,
      }

      await submitReview(assessment.id, 'reviewer-1', 'ld_officer', reviewData)

      setFormData({
        technicalScore: '',
        softSkillsScore: '',
        onJobScore: '',
        comments: '',
        strengths: '',
        improvements: '',
      })

      onSuccess?.()
    } catch (error) {
      console.error('Failed to submit review:', error)
      setErrors({ submit: 'Failed to submit review. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }))
    }
  }

  const getRoleDisplayName = (role: string) => {
    switch (role) {
      case 'direct_manager':
        return 'Direct Manager'
      case 'mentor':
        return 'Mentor'
      case 'manager':
        return 'Manager'
      case 'ld_officer':
        return 'L&D Officer'
      default:
        return role
    }
  }

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600'
    if (score >= 80) return 'text-blue-600'
    if (score >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (hasReviewed) {
    return (
      <Card className="mx-auto w-full max-w-2xl">
        <CardHeader>
          <CardTitle>Review Already Submitted</CardTitle>
          <CardDescription>
            You have already submitted your review for this assessment.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="rounded-lg border border-green-200 bg-green-50 p-4">
              <div className="mb-2 flex items-center gap-2">
                <Star className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">Your Review</span>
                <Badge variant="secondary">
                  {existingReview ? getRoleDisplayName(existingReview.reviewerRole) : 'L&D Officer'}
                </Badge>
              </div>

              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium">Technical:</span>
                  <span
                    className={`ml-2 font-semibold ${existingReview ? getScoreColor(existingReview.technicalScore || 0) : 'text-gray-600'}`}
                  >
                    {existingReview?.technicalScore || 0}%
                  </span>
                </div>
                <div>
                  <span className="font-medium">Soft Skills:</span>
                  <span
                    className={`ml-2 font-semibold ${existingReview ? getScoreColor(existingReview.softSkillsScore || 0) : 'text-gray-600'}`}
                  >
                    {existingReview?.softSkillsScore || 0}%
                  </span>
                </div>
                <div>
                  <span className="font-medium">On-Job:</span>
                  <span
                    className={`ml-2 font-semibold ${existingReview ? getScoreColor(existingReview.onJobScore || 0) : 'text-gray-600'}`}
                  >
                    {existingReview?.onJobScore || 0}%
                  </span>
                </div>
              </div>

              {existingReview?.comments && (
                <div className="mt-3">
                  <span className="font-medium">Comments:</span>
                  <p className="mt-1 text-gray-600">{existingReview.comments}</p>
                </div>
              )}

              {existingReview?.submittedAt && (
                <div className="mt-3 text-sm text-gray-500">
                  Submitted on {new Date(existingReview.submittedAt).toLocaleDateString()}
                </div>
              )}
            </div>

            <div className="flex justify-end">
              <Button variant="outline" onClick={onCancel}>
                Close
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="mx-auto w-full max-w-2xl">
      <CardHeader>
        <CardTitle>Submit Review</CardTitle>
        <CardDescription>
          Review assessment for <strong>{assessment.trainee?.userName}</strong> -{' '}
          {assessment.trainee?.college}
          <br />
          <Badge variant="outline" className="mt-2">
            {getRoleDisplayName('ld_officer')}
          </Badge>
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Score Inputs */}
          <div className="space-y-4">
            <h3 className="flex items-center gap-2 text-lg font-medium">
              <Star className="h-5 w-5" />
              Performance Scores (0-100)
            </h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="technicalScore">Technical Skills</Label>
                <Input
                  id="technicalScore"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.technicalScore}
                  onChange={(e) => handleInputChange('technicalScore', e.target.value)}
                  placeholder="0-100"
                  disabled={loading}
                />
                {errors.technicalScore && (
                  <p className="text-sm text-red-600">{errors.technicalScore}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="softSkillsScore">Soft Skills</Label>
                <Input
                  id="softSkillsScore"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.softSkillsScore}
                  onChange={(e) => handleInputChange('softSkillsScore', e.target.value)}
                  placeholder="0-100"
                  disabled={loading}
                />
                {errors.softSkillsScore && (
                  <p className="text-sm text-red-600">{errors.softSkillsScore}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="onJobScore">On-Job Performance</Label>
                <Input
                  id="onJobScore"
                  type="number"
                  min="0"
                  max="100"
                  value={formData.onJobScore}
                  onChange={(e) => handleInputChange('onJobScore', e.target.value)}
                  placeholder="0-100"
                  disabled={loading}
                />
                {errors.onJobScore && <p className="text-sm text-red-600">{errors.onJobScore}</p>}
              </div>
            </div>
          </div>

          {/* Comments */}
          <div className="space-y-2">
            <Label htmlFor="comments">
              <MessageSquare className="mr-2 inline h-4 w-4" />
              Overall Comments
            </Label>
            <textarea
              id="comments"
              value={formData.comments}
              onChange={(e) => handleInputChange('comments', e.target.value)}
              rows={4}
              className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
              placeholder="Provide overall feedback about the trainee's performance..."
              disabled={loading}
            />
          </div>

          {/* Strengths */}
          <div className="space-y-2">
            <Label htmlFor="strengths">
              <TrendingUp className="mr-2 inline h-4 w-4" />
              Strengths (one per line)
            </Label>
            <textarea
              id="strengths"
              value={formData.strengths}
              onChange={(e) => handleInputChange('strengths', e.target.value)}
              rows={3}
              className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
              placeholder="List the trainee's key strengths..."
              disabled={loading}
            />
          </div>

          {/* Areas for Improvement */}
          <div className="space-y-2">
            <Label htmlFor="improvements">
              <TrendingDown className="mr-2 inline h-4 w-4" />
              Areas for Improvement (one per line)
            </Label>
            <textarea
              id="improvements"
              value={formData.improvements}
              onChange={(e) => handleInputChange('improvements', e.target.value)}
              rows={3}
              className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
              placeholder="List areas where the trainee can improve..."
              disabled={loading}
            />
          </div>

          {errors.submit && (
            <div className="rounded-md border border-red-200 bg-red-50 p-3">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="mr-2 h-4 w-4" />
              {loading ? 'Submitting...' : 'Submit Review'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
