import { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { Badge } from '@/shared/components/ui/badge'
import { useAssessments } from '../hooks/useAssessments'
import { useTrainees } from '@/features/trainees/hooks/useTrainees'
import { Calendar, Users, CheckCircle, Clock } from 'lucide-react'

export function QuarterlyScheduler() {
  const { scheduleQuarterlyAssessments, assessments } = useAssessments()
  const { trainees } = useTrainees()

  const [selectedQuarter, setSelectedQuarter] = useState(Math.ceil((new Date().getMonth() + 1) / 3))
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [scheduledDate, setScheduledDate] = useState(new Date().toISOString().split('T')[0])
  const [dueDate, setDueDate] = useState(
    new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
  )
  const [loading, setLoading] = useState(false)
  const [result, setResult] = useState<{
    success: boolean
    message: string
    count?: number
  } | null>(null)

  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 5 }, (_, i) => currentYear + i)
  const quarters = [1, 2, 3, 4]

  const getQuarterDateRange = (quarter: number, year: number) => {
    const startMonth = (quarter - 1) * 3
    const endMonth = startMonth + 2

    const startDate = new Date(year, startMonth, 1)
    const endDate = new Date(year, endMonth + 1, 0) // Last day of the quarter

    return {
      start: startDate.toLocaleDateString(),
      end: endDate.toLocaleDateString(),
    }
  }

  const getActiveTrainees = () => {
    // For now, consider all trainees as active
    // In a real app, you might filter by enrollment status, completion date, etc.
    return trainees
  }

  const getExistingAssessmentsForQuarter = (quarter: number, year: number) => {
    return assessments.filter((a) => a.quarter === quarter && a.year === year)
  }

  const getTraineesWithoutAssessment = (quarter: number, year: number) => {
    const existingAssessments = getExistingAssessmentsForQuarter(quarter, year)
    const assessedTraineeIds = existingAssessments.map((a) => a.traineeId)
    return trainees.filter((t) => !assessedTraineeIds.includes(t.id))
  }

  const handleScheduleAssessments = async () => {
    setLoading(true)
    setResult(null)

    try {
      const newAssessments = await scheduleQuarterlyAssessments(selectedQuarter, selectedYear)
      setResult({
        success: true,
        message: `Successfully scheduled ${newAssessments.length} assessments for Q${selectedQuarter} ${selectedYear}`,
        count: newAssessments.length,
      })
    } catch (error) {
      console.error('Failed to schedule assessments:', error)
      setResult({
        success: false,
        message: 'Failed to schedule assessments. Please try again.',
      })
    } finally {
      setLoading(false)
    }
  }

  const activeTrainees = getActiveTrainees()
  const existingAssessments = getExistingAssessmentsForQuarter(selectedQuarter, selectedYear)
  const traineesWithoutAssessment = getTraineesWithoutAssessment(selectedQuarter, selectedYear)
  const dateRange = getQuarterDateRange(selectedQuarter, selectedYear)

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Quarterly Assessment Scheduler
          </CardTitle>
          <CardDescription>
            Schedule assessments for all active trainees for a specific quarter
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
            {/* Configuration */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Schedule Configuration</h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quarter">Quarter</Label>
                  <select
                    id="quarter"
                    value={selectedQuarter}
                    onChange={(e) => setSelectedQuarter(parseInt(e.target.value))}
                    className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                    disabled={loading}
                  >
                    {quarters.map((quarter) => (
                      <option key={quarter} value={quarter}>
                        Q{quarter} ({dateRange.start} - {dateRange.end})
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="year">Year</Label>
                  <select
                    id="year"
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                    className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                    disabled={loading}
                  >
                    {years.map((year) => (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="scheduledDate">Scheduled Date</Label>
                <Input
                  id="scheduledDate"
                  type="date"
                  value={scheduledDate}
                  onChange={(e) => setScheduledDate(e.target.value)}
                  disabled={loading}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="dueDate">Due Date</Label>
                <Input
                  id="dueDate"
                  type="date"
                  value={dueDate}
                  onChange={(e) => setDueDate(e.target.value)}
                  min={scheduledDate}
                  disabled={loading}
                />
              </div>

              <Button
                onClick={handleScheduleAssessments}
                disabled={loading || traineesWithoutAssessment.length === 0}
                className="w-full"
              >
                {loading
                  ? 'Scheduling...'
                  : `Schedule ${traineesWithoutAssessment.length} Assessments`}
              </Button>

              {result && (
                <div
                  className={`rounded-md p-3 ${
                    result.success
                      ? 'border border-green-200 bg-green-50'
                      : 'border border-red-200 bg-red-50'
                  }`}
                >
                  <p className={`text-sm ${result.success ? 'text-green-800' : 'text-red-800'}`}>
                    {result.message}
                  </p>
                </div>
              )}
            </div>

            {/* Statistics */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Quarter Overview</h3>

              <div className="space-y-3">
                <div className="flex items-center justify-between rounded-lg bg-blue-50 p-3">
                  <div className="flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-600" />
                    <span className="font-medium">Active Trainees</span>
                  </div>
                  <Badge variant="secondary">{activeTrainees.length}</Badge>
                </div>

                <div className="flex items-center justify-between rounded-lg bg-green-50 p-3">
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <span className="font-medium">Already Scheduled</span>
                  </div>
                  <Badge variant="secondary">{existingAssessments.length}</Badge>
                </div>

                <div className="flex items-center justify-between rounded-lg bg-yellow-50 p-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-yellow-600" />
                    <span className="font-medium">Need Scheduling</span>
                  </div>
                  <Badge variant="secondary">{traineesWithoutAssessment.length}</Badge>
                </div>

                {traineesWithoutAssessment.length === 0 && (
                  <div className="flex items-center justify-between rounded-lg bg-gray-50 p-3">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-gray-600" />
                      <span className="font-medium">Status</span>
                    </div>
                    <Badge variant="outline">Complete</Badge>
                  </div>
                )}
              </div>

              {/* Trainees needing assessment */}
              {traineesWithoutAssessment.length > 0 && (
                <div>
                  <h4 className="mb-2 font-medium">Trainees Needing Assessment:</h4>
                  <div className="max-h-40 space-y-1 overflow-y-auto">
                    {traineesWithoutAssessment.map((trainee) => (
                      <div key={trainee.id} className="rounded bg-gray-50 p-2 text-sm">
                        {trainee.userName} - {trainee.college}
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Existing Assessments for Quarter */}
      {existingAssessments.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>
              Existing Assessments for Q{selectedQuarter} {selectedYear}
            </CardTitle>
            <CardDescription>Assessments already scheduled for this quarter</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {existingAssessments.map((assessment) => (
                <div
                  key={assessment.id}
                  className="flex items-center justify-between rounded-lg border p-3"
                >
                  <div>
                    <div className="font-medium">{assessment.trainee?.userName}</div>
                    <div className="text-sm text-gray-600">{assessment.trainee?.college}</div>
                  </div>
                  <div className="text-right">
                    <Badge className={getStatusColor(assessment.status)}>
                      {assessment.status.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                    </Badge>
                    <div className="mt-1 text-sm text-gray-500">
                      Due: {new Date(assessment.dueDate).toLocaleDateString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}

function getStatusColor(status: string) {
  switch (status) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800'
    case 'in_progress':
      return 'bg-blue-100 text-blue-800'
    case 'completed':
      return 'bg-green-100 text-green-800'
    case 'overdue':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}
