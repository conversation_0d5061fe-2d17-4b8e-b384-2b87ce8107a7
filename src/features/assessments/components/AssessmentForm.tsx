import { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { useAssessments } from '../hooks/useAssessments'
import { useTrainees } from '@/features/trainees/hooks/useTrainees'
import { Save, X, Calendar } from 'lucide-react'
import type { Assessment, CreateAssessmentForm } from '@/shared/types'

interface AssessmentFormProps {
  assessment?: Assessment
  onSave: (assessment: Assessment) => void
  onCancel: () => void
}

export function AssessmentForm({ assessment, onSave, onCancel }: AssessmentFormProps) {
  const { createAssessment, updateAssessment } = useAssessments()
  const { trainees } = useTrainees()

  const [formData, setFormData] = useState({
    traineeId: assessment?.traineeId || '',
    quarter: assessment?.quarter || Math.ceil((new Date().getMonth() + 1) / 3),
    year: assessment?.year || new Date().getFullYear(),
    scheduledDate: assessment?.scheduledDate
      ? new Date(assessment.scheduledDate).toISOString().split('T')[0]
      : new Date().toISOString().split('T')[0],
    dueDate: assessment?.dueDate
      ? new Date(assessment.dueDate).toISOString().split('T')[0]
      : new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
  })

  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.traineeId.trim()) {
      newErrors.traineeId = 'Please select a trainee'
    }

    if (formData.quarter < 1 || formData.quarter > 4) {
      newErrors.quarter = 'Quarter must be between 1 and 4'
    }

    if (formData.year < 2020 || formData.year > 2030) {
      newErrors.year = 'Year must be between 2020 and 2030'
    }

    if (!formData.scheduledDate) {
      newErrors.scheduledDate = 'Scheduled date is required'
    }

    if (!formData.dueDate) {
      newErrors.dueDate = 'Due date is required'
    } else if (new Date(formData.dueDate) <= new Date(formData.scheduledDate)) {
      newErrors.dueDate = 'Due date must be after scheduled date'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      const assessmentData: CreateAssessmentForm = {
        traineeId: formData.traineeId,
        quarter: formData.quarter,
        year: formData.year,
        scheduledDate: new Date(formData.scheduledDate).toISOString(),
        dueDate: new Date(formData.dueDate).toISOString(),
      }

      let savedAssessment
      if (assessment) {
        savedAssessment = await updateAssessment(assessment.id, assessmentData)
      } else {
        savedAssessment = await createAssessment(assessmentData)
      }

      onSave(savedAssessment)
    } catch (error) {
      console.error('Failed to save assessment:', error)
      setErrors({ submit: 'Failed to save assessment. Please try again.' })
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string | number) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }))
    }
  }

  const currentYear = new Date().getFullYear()
  const years = Array.from({ length: 11 }, (_, i) => currentYear + i)
  const quarters = [1, 2, 3, 4]

  return (
    <Card className="mx-auto w-full max-w-2xl">
      <CardHeader>
        <CardTitle>{assessment ? 'Edit Assessment' : 'Schedule New Assessment'}</CardTitle>
        <CardDescription>
          {assessment
            ? 'Update the assessment details below.'
            : 'Create a new assessment for a trainee. Select the trainee, quarter, and dates.'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="traineeId">Trainee</Label>
            <select
              id="traineeId"
              value={formData.traineeId}
              onChange={(e) => handleInputChange('traineeId', e.target.value)}
              className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
              disabled={loading}
            >
              <option value="">Select a trainee</option>
              {trainees.map((trainee) => (
                <option key={trainee.id} value={trainee.id}>
                  {trainee.userName || trainee.id} - {trainee.college}
                </option>
              ))}
            </select>
            {errors.traineeId && <p className="text-sm text-red-600">{errors.traineeId}</p>}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="quarter">Quarter</Label>
              <select
                id="quarter"
                value={formData.quarter}
                onChange={(e) => handleInputChange('quarter', parseInt(e.target.value))}
                className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              >
                {quarters.map((quarter) => (
                  <option key={quarter} value={quarter}>
                    Q{quarter}
                  </option>
                ))}
              </select>
              {errors.quarter && <p className="text-sm text-red-600">{errors.quarter}</p>}
            </div>

            <div className="space-y-2">
              <Label htmlFor="year">Year</Label>
              <select
                id="year"
                value={formData.year}
                onChange={(e) => handleInputChange('year', parseInt(e.target.value))}
                className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-2 focus:ring-blue-500"
                disabled={loading}
              >
                {years.map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
              {errors.year && <p className="text-sm text-red-600">{errors.year}</p>}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="scheduledDate">
              <Calendar className="mr-2 inline h-4 w-4" />
              Scheduled Date
            </Label>
            <Input
              id="scheduledDate"
              type="date"
              value={formData.scheduledDate}
              onChange={(e) => handleInputChange('scheduledDate', e.target.value)}
              min={new Date().toISOString().split('T')[0]}
              disabled={loading}
            />
            {errors.scheduledDate && <p className="text-sm text-red-600">{errors.scheduledDate}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="dueDate">
              <Calendar className="mr-2 inline h-4 w-4" />
              Due Date
            </Label>
            <Input
              id="dueDate"
              type="date"
              value={formData.dueDate}
              onChange={(e) => handleInputChange('dueDate', e.target.value)}
              min={formData.scheduledDate || new Date().toISOString().split('T')[0]}
              disabled={loading}
            />
            {errors.dueDate && <p className="text-sm text-red-600">{errors.dueDate}</p>}
          </div>

          {errors.submit && (
            <div className="rounded-md border border-red-200 bg-red-50 p-3">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel} disabled={loading}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="mr-2 h-4 w-4" />
              {loading ? 'Saving...' : assessment ? 'Update Assessment' : 'Schedule Assessment'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
