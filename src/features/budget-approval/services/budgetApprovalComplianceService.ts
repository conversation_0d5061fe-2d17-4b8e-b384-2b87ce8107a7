import type { BudgetApprovalState, ApprovalRequestDetails } from '../types/budgetApproval'

// Compliance types
export interface ComplianceCheck {
  id: string
  name: string
  description: string
  type: 'regulatory' | 'policy' | 'security' | 'financial' | 'operational'
  severity: 'low' | 'medium' | 'high' | 'critical'
  status: 'pending' | 'passed' | 'failed' | 'waived'
  result?: ComplianceResult
  checkedAt?: string
  checkedBy?: string
  dueDate?: string
  requirements: string[]
}

export interface ComplianceResult {
  passed: boolean
  score: number // 0-100
  details: string
  evidence?: string[]
  violations?: ComplianceViolation[]
  recommendations?: string[]
}

export interface ComplianceViolation {
  code: string
  description: string
  severity: 'low' | 'medium' | 'high' | 'critical'
  impact: string
  remediation: string
  dueDate: string
}

export interface SLAMetric {
  id: string
  name: string
  description: string
  category: 'approval_time' | 'response_time' | 'availability' | 'quality'
  target: number
  unit: 'hours' | 'percentage' | 'days' | 'minutes'
  current: number
  status: 'met' | 'breached' | 'warning' | 'unknown'
  period: 'daily' | 'weekly' | 'monthly' | 'quarterly'
  lastUpdated: string
  trend: 'improving' | 'stable' | 'declining'
}

export interface SLAReport {
  id: string
  period: string
  overallCompliance: number
  metrics: SLAMetric[]
  breaches: SLABreach[]
  recommendations: string[]
  generatedAt: string
  generatedBy: string
}

export interface SLABreach {
  id: string
  metricId: string
  metricName: string
  severity: 'minor' | 'major' | 'critical'
  description: string
  impact: string
  occurredAt: string
  resolvedAt?: string
  resolution?: string
  preventiveActions?: string[]
}

export interface ComplianceReport {
  id: string
  budgetId: string
  budgetTitle: string
  period: string
  overallScore: number
  status: 'compliant' | 'non_compliant' | 'partially_compliant' | 'pending'
  checks: ComplianceCheck[]
  violations: ComplianceViolation[]
  recommendations: string[]
  nextReviewDate: string
  generatedAt: string
  generatedBy: string
}

class BudgetApprovalComplianceService {
  private complianceChecks: Map<string, ComplianceCheck[]> = new Map()
  private slaMetrics: Map<string, SLAMetric[]> = new Map()
  private complianceReports: Map<string, ComplianceReport[]> = new Map()
  private slaReports: Map<string, SLAReport[]> = new Map()

  constructor() {
    this.initializeDefaultComplianceChecks()
    this.initializeDefaultSLAMetrics()
  }

  private initializeDefaultComplianceChecks(): void {
    const defaultChecks: ComplianceCheck[] = [
      {
        id: 'budget-authorization',
        name: 'Budget Authorization',
        description: 'Verify proper budget authorization and approval chain',
        type: 'financial',
        severity: 'high',
        status: 'pending',
        requirements: [
          'Proper approval chain followed',
          'Authorization limits respected',
          'Documentation complete',
        ],
      },
      {
        id: 'training-compliance',
        name: 'Training Compliance',
        description: 'Ensure training meets regulatory and compliance requirements',
        type: 'regulatory',
        severity: 'critical',
        status: 'pending',
        requirements: [
          'Regulatory requirements met',
          'Certifications valid',
          'Curriculum approved',
        ],
      },
      {
        id: 'vendor-compliance',
        name: 'Vendor Compliance',
        description: 'Verify vendor compliance with company policies',
        type: 'policy',
        severity: 'medium',
        status: 'pending',
        requirements: ['Vendor approved', 'Contract terms reviewed', 'Insurance coverage verified'],
      },
      {
        id: 'data-privacy',
        name: 'Data Privacy',
        description: 'Ensure data privacy and protection requirements are met',
        type: 'security',
        severity: 'high',
        status: 'pending',
        requirements: [
          'Data protection measures in place',
          'Privacy policy compliance',
          'Consent obtained where required',
        ],
      },
      {
        id: 'financial-controls',
        name: 'Financial Controls',
        description: 'Verify financial controls and segregation of duties',
        type: 'financial',
        severity: 'critical',
        status: 'pending',
        requirements: [
          'Segregation of duties maintained',
          'Financial controls documented',
          'Audit trail complete',
        ],
      },
    ]

    this.complianceChecks.set('default', defaultChecks)
  }

  private initializeDefaultSLAMetrics(): void {
    const defaultMetrics: SLAMetric[] = [
      {
        id: 'manager-approval-time',
        name: 'Manager Approval Time',
        description: 'Time taken for manager level approvals',
        category: 'approval_time',
        target: 24,
        unit: 'hours',
        current: 0,
        status: 'unknown',
        period: 'weekly',
        lastUpdated: new Date().toISOString(),
        trend: 'stable',
      },
      {
        id: 'executive-approval-time',
        name: 'Executive Approval Time',
        description: 'Time taken for executive level approvals',
        category: 'approval_time',
        target: 72,
        unit: 'hours',
        current: 0,
        status: 'unknown',
        period: 'weekly',
        lastUpdated: new Date().toISOString(),
        trend: 'stable',
      },
      {
        id: 'first-response-time',
        name: 'First Response Time',
        description: 'Time to first response on approval requests',
        category: 'response_time',
        target: 4,
        unit: 'hours',
        current: 0,
        status: 'unknown',
        period: 'daily',
        lastUpdated: new Date().toISOString(),
        trend: 'stable',
      },
      {
        id: 'approval-accuracy',
        name: 'Approval Accuracy',
        description: 'Percentage of approvals without requiring rework',
        category: 'quality',
        target: 95,
        unit: 'percentage',
        current: 0,
        status: 'unknown',
        period: 'monthly',
        lastUpdated: new Date().toISOString(),
        trend: 'stable',
      },
      {
        id: 'system-availability',
        name: 'System Availability',
        description: 'Availability of the approval system',
        category: 'availability',
        target: 99.5,
        unit: 'percentage',
        current: 0,
        status: 'unknown',
        period: 'monthly',
        lastUpdated: new Date().toISOString(),
        trend: 'stable',
      },
    ]

    this.slaMetrics.set('default', defaultMetrics)
  }

  async checkCompliance(request: ApprovalRequestDetails): Promise<ComplianceCheck[]> {
    const checks = this.complianceChecks.get('default') || []
    const results: ComplianceCheck[] = []

    for (const check of checks) {
      const result = await this.evaluateComplianceCheck(check, request)
      results.push({
        ...check,
        status: result.passed ? 'passed' : 'failed',
        result,
        checkedAt: new Date().toISOString(),
        checkedBy: 'system',
      })
    }

    return results
  }

  private async evaluateComplianceCheck(
    check: ComplianceCheck,
    request: ApprovalRequestDetails
  ): Promise<ComplianceResult> {
    switch (check.id) {
      case 'budget-authorization':
        return this.evaluateBudgetAuthorization(request)
      case 'training-compliance':
        return this.evaluateTrainingCompliance(request)
      case 'vendor-compliance':
        return this.evaluateVendorCompliance(request)
      case 'data-privacy':
        return this.evaluateDataPrivacy(request)
      case 'financial-controls':
        return this.evaluateFinancialControls(request)
      default:
        return {
          passed: true,
          score: 100,
          details: 'Compliance check passed by default',
        }
    }
  }

  private evaluateBudgetAuthorization(request: ApprovalRequestDetails): ComplianceResult {
    const violations: ComplianceViolation[] = []
    let score = 100

    // Check amount limits
    if (request.totalAmount > 50000) {
      violations.push({
        code: 'HIGH_AMOUNT_NO_EXECUTIVE',
        description: 'High amount budget requires executive approval',
        severity: 'high',
        impact: 'Budget may exceed spending limits',
        remediation: 'Obtain executive approval before proceeding',
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      })
      score -= 30
    }

    // Check department budget
    if (request.totalAmount > 10000) {
      violations.push({
        code: 'DEPARTMENT_BUDGET_EXCEEDED',
        description: 'Budget may exceed department monthly limit',
        severity: 'medium',
        impact: 'Department budget overspend risk',
        remediation: 'Verify department budget availability',
        dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
      })
      score -= 15
    }

    return {
      passed: violations.length === 0,
      score: Math.max(0, score),
      details:
        violations.length === 0
          ? 'Budget authorization requirements met'
          : `Found ${violations.length} authorization issues`,
      violations,
      recommendations:
        violations.length > 0
          ? ['Review approval chain', 'Verify budget limits', 'Obtain required approvals']
          : [],
    }
  }

  private evaluateTrainingCompliance(request: ApprovalRequestDetails): ComplianceResult {
    const violations: ComplianceViolation[] = []
    let score = 100

    // Check if compliance training
    if (request.trainingType.toLowerCase().includes('compliance')) {
      if (request.totalAmount > 5000) {
        violations.push({
          code: 'COMPLIANCE_TRAINING_HIGH_COST',
          description: 'High-cost compliance training requires additional review',
          severity: 'medium',
          impact: 'Compliance risk if not properly reviewed',
          remediation: 'Obtain compliance officer approval',
          dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
        })
        score -= 20
      }
    }

    return {
      passed: violations.length === 0,
      score: Math.max(0, score),
      details:
        violations.length === 0
          ? 'Training compliance requirements met'
          : `Found ${violations.length} compliance issues`,
      violations,
      recommendations:
        violations.length > 0
          ? ['Review compliance requirements', 'Obtain necessary approvals']
          : [],
    }
  }

  private evaluateVendorCompliance(request: ApprovalRequestDetails): ComplianceResult {
    // Mock vendor compliance check
    return {
      passed: true,
      score: 95,
      details: 'Vendor compliance verified with minor observations',
      recommendations: ['Ensure vendor contracts are up to date'],
    }
  }

  private evaluateDataPrivacy(request: ApprovalRequestDetails): ComplianceResult {
    // Mock data privacy check
    return {
      passed: true,
      score: 98,
      details: 'Data privacy requirements met',
      evidence: ['Privacy policy reviewed', 'Consent mechanisms in place'],
    }
  }

  private evaluateFinancialControls(request: ApprovalRequestDetails): ComplianceResult {
    const violations: ComplianceViolation[] = []
    let score = 100

    // Check for segregation of duties
    if (request.totalAmount > 25000) {
      violations.push({
        code: 'SEGREGATION_OF_DUTIES',
        description: 'High-value budgets require additional oversight',
        severity: 'medium',
        impact: 'Financial control risk',
        remediation: 'Implement dual approval requirement',
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(),
      })
      score -= 25
    }

    return {
      passed: violations.length === 0,
      score: Math.max(0, score),
      details:
        violations.length === 0
          ? 'Financial controls properly implemented'
          : `Found ${violations.length} control issues`,
      violations,
      recommendations:
        violations.length > 0
          ? ['Strengthen financial controls', 'Implement oversight mechanisms']
          : [],
    }
  }

  async generateComplianceReport(
    budgetId: string,
    request: ApprovalRequestDetails
  ): Promise<ComplianceReport> {
    const checks = await this.checkCompliance(request)
    const violations = checks.flatMap((check) => check.result?.violations || [])
    const overallScore =
      checks.reduce((sum, check) => sum + (check.result?.score || 0), 0) / checks.length

    let status: 'compliant' | 'non_compliant' | 'partially_compliant' | 'pending'
    if (overallScore >= 95) status = 'compliant'
    else if (overallScore >= 80) status = 'partially_compliant'
    else if (overallScore >= 60) status = 'non_compliant'
    else status = 'pending'

    const recommendations = [
      ...violations.map((v) => v.remediation),
      ...checks.flatMap((check) => check.result?.recommendations || []),
    ].filter((rec, index, arr) => arr.indexOf(rec) === index) // Remove duplicates

    const report: ComplianceReport = {
      id: `compliance_${Date.now()}`,
      budgetId,
      budgetTitle: request.title,
      period: new Date().toISOString(),
      overallScore,
      status,
      checks,
      violations,
      recommendations,
      nextReviewDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
      generatedAt: new Date().toISOString(),
      generatedBy: 'system',
    }

    const reports = this.complianceReports.get(budgetId) || []
    reports.push(report)
    this.complianceReports.set(budgetId, reports)

    return report
  }

  async updateSLAMetrics(metrics: Partial<SLAMetric>[]): Promise<void> {
    const currentMetrics = this.slaMetrics.get('default') || []

    for (const update of metrics) {
      const index = currentMetrics.findIndex((m) => m.id === update.id)
      if (index !== -1) {
        currentMetrics[index] = {
          ...currentMetrics[index],
          ...update,
          lastUpdated: new Date().toISOString(),
        }
      }
    }

    this.slaMetrics.set('default', currentMetrics)
  }

  async generateSLAReport(period: string): Promise<SLAReport> {
    const metrics = this.slaMetrics.get('default') || []
    const breaches: SLABreach[] = []

    // Identify breaches
    for (const metric of metrics) {
      if (metric.status === 'breached') {
        breaches.push({
          id: `breach_${Date.now()}_${metric.id}`,
          metricId: metric.id,
          metricName: metric.name,
          severity: metric.current > metric.target * 1.5 ? 'critical' : 'major',
          description: `${metric.name} target of ${metric.target}${metric.unit} not met`,
          impact: 'Service level agreement breach',
          occurredAt: metric.lastUpdated,
          preventiveActions: ['Review process', 'Increase monitoring', 'Add alerts'],
        })
      }
    }

    const overallCompliance =
      metrics.reduce((sum, m) => {
        const compliance =
          m.current <= m.target ? 100 : Math.max(0, 100 - ((m.current - m.target) / m.target) * 100)
        return sum + compliance
      }, 0) / metrics.length

    const recommendations = [
      ...breaches.map((b) => b.preventiveActions || []).flat(),
      'Regular SLA monitoring',
      'Process optimization',
      'Stakeholder communication',
    ].filter((rec, index, arr) => arr.indexOf(rec) === index)

    const report: SLAReport = {
      id: `sla_${Date.now()}`,
      period,
      overallCompliance,
      metrics,
      breaches,
      recommendations,
      generatedAt: new Date().toISOString(),
      generatedBy: 'system',
    }

    const reports = this.slaReports.get(period) || []
    reports.push(report)
    this.slaReports.set(period, reports)

    return report
  }

  async getComplianceReports(budgetId?: string): Promise<ComplianceReport[]> {
    if (budgetId) {
      return this.complianceReports.get(budgetId) || []
    }

    // Return all reports
    const allReports: ComplianceReport[] = []
    for (const reports of this.complianceReports.values()) {
      allReports.push(...reports)
    }
    return allReports
  }

  async getSLAReports(period?: string): Promise<SLAReport[]> {
    if (period) {
      return this.slaReports.get(period) || []
    }

    // Return all reports
    const allReports: SLAReport[] = []
    for (const reports of this.slaReports.values()) {
      allReports.push(...reports)
    }
    return allReports
  }

  async getSLAMetrics(): Promise<SLAMetric[]> {
    return this.slaMetrics.get('default') || []
  }

  async checkSLACompliance(
    workflowId: string,
    currentState: BudgetApprovalState,
    startTime: string
  ): Promise<{
    compliant: boolean
    breaches: SLABreach[]
    metrics: SLAMetric[]
  }> {
    const metrics = this.slaMetrics.get('default') || []
    const breaches: SLABreach[] = []
    const now = new Date()
    const start = new Date(startTime)
    const elapsedHours = (now.getTime() - start.getTime()) / (1000 * 60 * 60)

    // Check approval time SLAs
    const approvalTimeMetric = metrics.find((m) => m.id === 'manager-approval-time')
    if (approvalTimeMetric && elapsedHours > approvalTimeMetric.target) {
      breaches.push({
        id: `sla_breach_${Date.now()}`,
        metricId: approvalTimeMetric.id,
        metricName: approvalTimeMetric.name,
        severity: elapsedHours > approvalTimeMetric.target * 2 ? 'critical' : 'major',
        description: `Approval time exceeded SLA of ${approvalTimeMetric.target}${approvalTimeMetric.unit}`,
        impact: 'Workflow delay',
        occurredAt: now.toISOString(),
        preventiveActions: ['Escalate to manager', 'Send reminder notifications'],
      })
    }

    const compliant = breaches.length === 0

    return {
      compliant,
      breaches,
      metrics,
    }
  }
}

export const budgetApprovalComplianceService = new BudgetApprovalComplianceService()
