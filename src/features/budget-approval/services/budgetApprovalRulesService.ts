import type {
  BudgetApprovalState,
  ApprovalRequestDetails,
  BudgetApprovalWorkflow,
} from '../types/budgetApproval'

// Rule types
export interface ApprovalRule {
  id: string
  name: string
  description: string
  type:
    | 'amount_threshold'
    | 'department_policy'
    | 'risk_assessment'
    | 'compliance_check'
    | 'time_based'
    | 'custom'
  priority: 'low' | 'medium' | 'high' | 'critical'
  isActive: boolean
  conditions: RuleCondition[]
  actions: RuleAction[]
  createdAt: string
  updatedAt: string
}

export interface RuleCondition {
  field: string
  operator:
    | 'equals'
    | 'not_equals'
    | 'greater_than'
    | 'less_than'
    | 'contains'
    | 'not_contains'
    | 'in'
    | 'not_in'
  value: any
  logicalOperator?: 'and' | 'or'
}

export interface RuleAction {
  type: 'require_approval' | 'auto_approve' | 'escalate' | 'notify' | 'block' | 'add_condition'
  parameters: Record<string, any>
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
  appliedRules: AppliedRule[]
  suggestedActions: SuggestedAction[]
}

export interface ValidationError {
  code: string
  message: string
  field?: string
  severity: 'error' | 'warning'
  ruleId?: string
}

export interface ValidationWarning {
  code: string
  message: string
  field?: string
  recommendation?: string
}

export interface AppliedRule {
  ruleId: string
  ruleName: string
  action: string
  result: 'passed' | 'failed' | 'triggered'
  details?: string
}

export interface SuggestedAction {
  type: 'modify_amount' | 'add_documentation' | 'change_category' | 'escalate' | 'split_request'
  description: string
  reason: string
  impact: string
}

class BudgetApprovalRulesService {
  private rules: ApprovalRule[] = []
  private validationHistory: Map<string, ValidationResult[]> = new Map()

  constructor() {
    this.initializeDefaultRules()
  }

  private initializeDefaultRules(): void {
    this.rules = [
      {
        id: 'amount-threshold-level-1',
        name: 'Low Amount Auto-Approval',
        description: 'Auto-approve budgets under $1,000 for low-risk categories',
        type: 'amount_threshold',
        priority: 'medium',
        isActive: true,
        conditions: [
          { field: 'totalAmount', operator: 'less_than', value: 1000 },
          { field: 'riskLevel', operator: 'equals', value: 'low' },
          { field: 'trainingType', operator: 'in', value: ['mandatory', 'compliance'] },
        ],
        actions: [{ type: 'auto_approve', parameters: { reason: 'Low amount auto-approval' } }],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'amount-threshold-level-2',
        name: 'Medium Amount Manager Review',
        description: 'Require manager approval for budgets between $1,000-$10,000',
        type: 'amount_threshold',
        priority: 'high',
        isActive: true,
        conditions: [
          { field: 'totalAmount', operator: 'greater_than', value: 1000 },
          { field: 'totalAmount', operator: 'less_than', value: 10000 },
        ],
        actions: [{ type: 'require_approval', parameters: { level: 'manager' } }],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'amount-threshold-level-3',
        name: 'High Amount Executive Review',
        description: 'Require executive approval for budgets over $10,000',
        type: 'amount_threshold',
        priority: 'critical',
        isActive: true,
        conditions: [{ field: 'totalAmount', operator: 'greater_than', value: 10000 }],
        actions: [
          { type: 'require_approval', parameters: { level: 'executive' } },
          { type: 'notify', parameters: { recipients: ['finance', 'cfo'] } },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'department-budget-limit',
        name: 'Department Budget Limit Check',
        description: 'Check if budget exceeds department monthly limit',
        type: 'department_policy',
        priority: 'high',
        isActive: true,
        conditions: [{ field: 'department', operator: 'not_equals', value: null }],
        actions: [{ type: 'add_condition', parameters: { type: 'budget_limit_check' } }],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'compliance-mandatory-training',
        name: 'Compliance Training Validation',
        description: 'Ensure compliance training meets regulatory requirements',
        type: 'compliance_check',
        priority: 'critical',
        isActive: true,
        conditions: [
          { field: 'trainingType', operator: 'contains', value: 'compliance' },
          { field: 'totalAmount', operator: 'greater_than', value: 5000 },
        ],
        actions: [
          { type: 'require_approval', parameters: { level: 'compliance_officer' } },
          { type: 'add_condition', parameters: { type: 'documentation_required' } },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'risk-assessment-high',
        name: 'High Risk Assessment',
        description: 'Additional review for high-risk training programs',
        type: 'risk_assessment',
        priority: 'high',
        isActive: true,
        conditions: [{ field: 'riskLevel', operator: 'equals', value: 'high' }],
        actions: [
          { type: 'require_approval', parameters: { level: 'risk_manager' } },
          { type: 'escalate', parameters: { reason: 'High risk assessment required' } },
        ],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ]
  }

  async validateBudgetRequest(request: ApprovalRequestDetails): Promise<ValidationResult> {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []
    const appliedRules: AppliedRule[] = []
    const suggestedActions: SuggestedAction[] = []

    // Apply all active rules
    const activeRules = this.rules.filter((rule) => rule.isActive)

    for (const rule of activeRules) {
      const ruleResult = await this.evaluateRule(rule, request)
      appliedRules.push(ruleResult)

      if (ruleResult.result === 'failed') {
        errors.push({
          code: `RULE_${rule.id}_FAILED`,
          message: `Rule "${rule.name}" failed: ${ruleResult.details}`,
          severity: 'error',
          ruleId: rule.id,
        })
      }

      // Generate suggested actions based on rule failures
      if (ruleResult.result === 'failed') {
        const actions = this.generateSuggestedActions(rule, request)
        suggestedActions.push(...actions)
      }
    }

    // Perform basic validation
    const basicValidation = this.performBasicValidation(request)
    errors.push(...basicValidation.errors)
    warnings.push(...basicValidation.warnings)

    const isValid = errors.length === 0

    // Store validation history
    const history = this.validationHistory.get(request.id) || []
    history.push({
      isValid,
      errors,
      warnings,
      appliedRules,
      suggestedActions,
    })
    this.validationHistory.set(request.id, history)

    return {
      isValid,
      errors,
      warnings,
      appliedRules,
      suggestedActions,
    }
  }

  private async evaluateRule(
    rule: ApprovalRule,
    request: ApprovalRequestDetails
  ): Promise<AppliedRule> {
    try {
      const conditionsMet = this.evaluateConditions(rule.conditions, request)

      if (conditionsMet) {
        // Apply rule actions
        for (const action of rule.actions) {
          await this.applyAction(action, request)
        }

        return {
          ruleId: rule.id,
          ruleName: rule.name,
          action: rule.actions.map((a) => a.type).join(', '),
          result: 'triggered',
          details: `Rule conditions met, actions applied: ${rule.actions.map((a) => a.type).join(', ')}`,
        }
      }

      return {
        ruleId: rule.id,
        ruleName: rule.name,
        action: 'none',
        result: 'passed',
        details: 'Rule conditions not met',
      }
    } catch (error) {
      return {
        ruleId: rule.id,
        ruleName: rule.name,
        action: 'error',
        result: 'failed',
        details: error instanceof Error ? error.message : 'Unknown error',
      }
    }
  }

  private evaluateConditions(
    conditions: RuleCondition[],
    request: ApprovalRequestDetails
  ): boolean {
    if (conditions.length === 0) return true

    return conditions.every((condition) => {
      const fieldValue = this.getFieldValue(request, condition.field)
      return this.compareValues(fieldValue, condition.operator, condition.value)
    })
  }

  private getFieldValue(request: ApprovalRequestDetails, field: string): any {
    // Navigate through nested object properties
    const parts = field.split('.')
    let value: any = request

    for (const part of parts) {
      if (value && typeof value === 'object' && part in value) {
        value = value[part as keyof typeof value]
      } else {
        return undefined
      }
    }

    return value
  }

  private compareValues(fieldValue: any, operator: string, compareValue: any): boolean {
    switch (operator) {
      case 'equals':
        return fieldValue === compareValue
      case 'not_equals':
        return fieldValue !== compareValue
      case 'greater_than':
        return Number(fieldValue) > Number(compareValue)
      case 'less_than':
        return Number(fieldValue) < Number(compareValue)
      case 'contains':
        return String(fieldValue).toLowerCase().includes(String(compareValue).toLowerCase())
      case 'not_contains':
        return !String(fieldValue).toLowerCase().includes(String(compareValue).toLowerCase())
      case 'in':
        return Array.isArray(compareValue) && compareValue.includes(fieldValue)
      case 'not_in':
        return Array.isArray(compareValue) && !compareValue.includes(fieldValue)
      default:
        return false
    }
  }

  private async applyAction(action: RuleAction, request: ApprovalRequestDetails): Promise<void> {
    // In a real implementation, this would trigger the appropriate actions
    // For now, we'll just log the action
    console.log(`Applying action: ${action.type}`, action.parameters)
  }

  private generateSuggestedActions(
    rule: ApprovalRule,
    request: ApprovalRequestDetails
  ): SuggestedAction[] {
    const actions: SuggestedAction[] = []

    switch (rule.type) {
      case 'amount_threshold':
        if (request.totalAmount > 10000) {
          actions.push({
            type: 'split_request',
            description: 'Consider splitting this large budget into smaller requests',
            reason: 'Large budgets require additional approval levels',
            impact: 'May speed up approval process',
          })
        }
        break

      case 'compliance_check':
        actions.push({
          type: 'add_documentation',
          description: 'Add compliance documentation and risk assessment',
          reason: 'Compliance training requires additional documentation',
          impact: 'Required for approval',
        })
        break

      case 'risk_assessment':
        actions.push({
          type: 'escalate',
          description: 'Escalate to risk management team',
          reason: 'High-risk training requires expert review',
          impact: 'Ensures proper risk mitigation',
        })
        break
    }

    return actions
  }

  private performBasicValidation(request: ApprovalRequestDetails): {
    errors: ValidationError[]
    warnings: ValidationWarning[]
  } {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []

    // Basic field validation
    if (!request.title || request.title.trim().length === 0) {
      errors.push({
        code: 'MISSING_TITLE',
        message: 'Budget request title is required',
        field: 'title',
        severity: 'error',
      })
    }

    if (!request.description || request.description.trim().length === 0) {
      errors.push({
        code: 'MISSING_DESCRIPTION',
        message: 'Budget request description is required',
        field: 'description',
        severity: 'error',
      })
    }

    if (request.totalAmount <= 0) {
      errors.push({
        code: 'INVALID_AMOUNT',
        message: 'Budget amount must be greater than 0',
        field: 'totalAmount',
        severity: 'error',
      })
    }

    if (!request.department) {
      errors.push({
        code: 'MISSING_DEPARTMENT',
        message: 'Department is required',
        field: 'department',
        severity: 'error',
      })
    }

    // Warning validations
    if (request.totalAmount > 50000) {
      warnings.push({
        code: 'HIGH_AMOUNT_WARNING',
        message: 'This is a very large budget request that may require additional justification',
        field: 'totalAmount',
        recommendation: 'Consider providing detailed business justification',
      })
    }

    if (!request.deadline) {
      warnings.push({
        code: 'MISSING_DEADLINE',
        message: 'No deadline specified',
        field: 'deadline',
        recommendation: 'Adding a deadline may help prioritize the approval',
      })
    }

    return { errors, warnings }
  }

  async getRules(): Promise<ApprovalRule[]> {
    return [...this.rules]
  }

  async addRule(rule: Omit<ApprovalRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApprovalRule> {
    const newRule: ApprovalRule = {
      ...rule,
      id: `rule_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    this.rules.push(newRule)
    return newRule
  }

  async updateRule(id: string, updates: Partial<ApprovalRule>): Promise<ApprovalRule | null> {
    const ruleIndex = this.rules.findIndex((rule) => rule.id === id)
    if (ruleIndex === -1) return null

    this.rules[ruleIndex] = {
      ...this.rules[ruleIndex],
      ...updates,
      updatedAt: new Date().toISOString(),
    }

    return this.rules[ruleIndex]
  }

  async deleteRule(id: string): Promise<boolean> {
    const ruleIndex = this.rules.findIndex((rule) => rule.id === id)
    if (ruleIndex === -1) return false

    this.rules.splice(ruleIndex, 1)
    return true
  }

  async getValidationHistory(requestId: string): Promise<ValidationResult[]> {
    return this.validationHistory.get(requestId) || []
  }

  async checkAutoApproval(
    request: ApprovalRequestDetails
  ): Promise<{ canAutoApprove: boolean; reason?: string }> {
    const validation = await this.validateBudgetRequest(request)

    if (!validation.isValid) {
      return { canAutoApprove: false, reason: 'Validation failed' }
    }

    // Check if any auto-approval rules were triggered
    const autoApprovalRules = validation.appliedRules.filter((rule) =>
      rule.action.includes('auto_approve')
    )

    if (autoApprovalRules.length > 0) {
      return {
        canAutoApprove: true,
        reason: `Auto-approved by rules: ${autoApprovalRules.map((r) => r.ruleName).join(', ')}`,
      }
    }

    return { canAutoApprove: false, reason: 'No auto-approval rules matched' }
  }
}

export const budgetApprovalRulesService = new BudgetApprovalRulesService()
