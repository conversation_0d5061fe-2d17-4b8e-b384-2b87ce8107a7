import type { BudgetApprovalState } from '../types/budgetApproval'
import type {
  ExtendedApprovalAnalytics,
  ApprovalTimeMetrics,
  ApproverPerformance,
  ApprovalTrendData,
  ApprovalBottleneck,
} from '../types/analytics'

// Mock data service for analytics
class BudgetApprovalAnalyticsService {
  async getApprovalAnalytics(dateRange: string): Promise<ExtendedApprovalAnalytics> {
    // Simulate API call
    await new Promise((resolve) => setTimeout(resolve, 500))

    return {
      totalApprovals: 245,
      pendingApprovals: 18,
      approvedToday: 12,
      rejectedToday: 3,
      averageApprovalTime: 72, // minutes
      approvalRate: 87.5,
      rejectionRate: 8.2,
      escalationRate: 4.3,
      delegationRate: 3.1,
      overdueApprovals: 5,
      totalBudgetValue: 2500000,
      averageBudgetValue: 10204,
      statusBreakdown: {
        approved: 214,
        rejected: 20,
        pending: 18,
        cancelled: 3,
        expired: 2,
      },
      departmentMetrics: {
        Engineering: {
          approved: 85,
          rejected: 8,
          pending: 7,
          averageApprovalTime: 68,
        },
        Sales: {
          approved: 62,
          rejected: 5,
          pending: 5,
          averageApprovalTime: 75,
        },
        Marketing: {
          approved: 45,
          rejected: 4,
          pending: 4,
          averageApprovalTime: 82,
        },
        Operations: {
          approved: 53,
          rejected: 3,
          pending: 2,
          averageApprovalTime: 65,
        },
      },
      bottlenecks: [
        {
          type: 'Department Head Approval',
          severity: 'medium',
          description:
            'Department heads are taking longer than expected to review budgets over $50,000',
          affectedItems: 8,
          averageDelay: 180, // minutes
          impactScore: 25,
          recommendations: [
            'Implement automatic escalation for budgets pending > 48 hours',
            'Add reminder notifications for department heads',
            'Consider delegating approval authority to deputies',
          ],
        },
        {
          type: 'Finance Review',
          severity: 'low',
          description: 'Finance team backlog during month-end closing',
          affectedItems: 3,
          averageDelay: 120,
          impactScore: 15,
          recommendations: [
            'Stagger budget submissions away from month-end',
            'Add temporary finance staff during peak periods',
          ],
        },
      ],
    }
  }

  async getApprovalTimeMetrics(dateRange: string): Promise<ApprovalTimeMetrics> {
    await new Promise((resolve) => setTimeout(resolve, 300))

    return {
      averageApprovalTime: 72,
      medianApprovalTime: 65,
      p95ApprovalTime: 180,
      p99ApprovalTime: 240,
      slaTarget: 120, // 2 hours
      slaComplianceRate: 85.5,
      fastestApproval: 15,
      slowestApproval: 480,
      timeByState: {
        draft: 5,
        pending_approval: 72,
        approved: 2,
        rejected: 45,
      },
      timeByDepartment: {
        Engineering: 68,
        Sales: 75,
        Marketing: 82,
        Operations: 65,
      },
    }
  }

  async getApproverPerformance(dateRange: string): Promise<ApproverPerformance[]> {
    await new Promise((resolve) => setTimeout(resolve, 400))

    return [
      {
        approverId: 'user-1',
        approverName: 'Sarah Johnson',
        department: 'Engineering',
        totalApprovals: 45,
        approvalRate: 92,
        averageApprovalTime: 55,
        efficiencyScore: 95,
        workloadScore: 88,
        qualityScore: 94,
      },
      {
        approverId: 'user-2',
        approverName: 'Michael Chen',
        department: 'Finance',
        totalApprovals: 38,
        approvalRate: 89,
        averageApprovalTime: 48,
        efficiencyScore: 91,
        workloadScore: 82,
        qualityScore: 96,
      },
      {
        approverId: 'user-3',
        approverName: 'Emily Rodriguez',
        department: 'Operations',
        totalApprovals: 52,
        approvalRate: 85,
        averageApprovalTime: 62,
        efficiencyScore: 87,
        workloadScore: 91,
        qualityScore: 89,
      },
    ]
  }

  async getApprovalTrends(dateRange: string): Promise<ApprovalTrendData[]> {
    await new Promise((resolve) => setTimeout(resolve, 350))

    const periods =
      dateRange === '7d' ? 7 : dateRange === '30d' ? 30 : dateRange === '90d' ? 90 : 365
    const data: ApprovalTrendData[] = []

    for (let i = periods - 1; i >= 0; i--) {
      const date = new Date()
      date.setDate(date.getDate() - i)

      data.push({
        period: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        approved: Math.floor(Math.random() * 15) + 5,
        rejected: Math.floor(Math.random() * 5) + 1,
        pending: Math.floor(Math.random() * 8) + 2,
        totalBudgetValue: Math.floor(Math.random() * 200000) + 50000,
        averageApprovalTime: Math.floor(Math.random() * 60) + 45,
      })
    }

    return data
  }

  async generateApprovalReport(
    reportType: 'summary' | 'detailed' | 'performance',
    dateRange: string,
    filters?: Record<string, any>
  ): Promise<Blob> {
    // Simulate report generation
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // In a real implementation, this would generate a PDF/Excel report
    const reportData = {
      reportType,
      dateRange,
      filters,
      generatedAt: new Date().toISOString(),
      data: await this.getApprovalAnalytics(dateRange),
    }

    return new Blob([JSON.stringify(reportData, null, 2)], {
      type: 'application/json',
    })
  }

  async getComplianceMetrics(dateRange: string): Promise<{
    complianceRate: number
    violations: Array<{
      type: string
      count: number
      severity: 'low' | 'medium' | 'high'
      description: string
    }>
    recommendations: string[]
  }> {
    await new Promise((resolve) => setTimeout(resolve, 400))

    return {
      complianceRate: 94.5,
      violations: [
        {
          type: 'Approval Timeout',
          count: 3,
          severity: 'medium',
          description: 'Budgets approved beyond SLA timeframe',
        },
        {
          type: 'Missing Documentation',
          count: 2,
          severity: 'low',
          description: 'Budgets approved without required documentation',
        },
      ],
      recommendations: [
        'Implement automated SLA monitoring',
        'Add required field validation before submission',
        'Enhance approver training on compliance requirements',
      ],
    }
  }
}

export const budgetApprovalAnalyticsService = new BudgetApprovalAnalyticsService()
