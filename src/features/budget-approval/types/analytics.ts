import { z } from 'zod'

// Approval time metrics
export const ApprovalTimeMetricsSchema = z.object({
  averageApprovalTime: z.number(),
  medianApprovalTime: z.number(),
  p95ApprovalTime: z.number(),
  p99ApprovalTime: z.number(),
  slaTarget: z.number(),
  slaComplianceRate: z.number(),
  fastestApproval: z.number(),
  slowestApproval: z.number(),
  timeByState: z.record(z.string(), z.number()),
  timeByDepartment: z.record(z.string(), z.number()),
})

// Approver performance metrics
export const ApproverPerformanceSchema = z.object({
  approverId: z.string(),
  approverName: z.string(),
  department: z.string(),
  totalApprovals: z.number(),
  approvalRate: z.number(),
  averageApprovalTime: z.number(),
  efficiencyScore: z.number(),
  workloadScore: z.number(),
  qualityScore: z.number(),
})

// Approval trend data
export const ApprovalTrendDataSchema = z.object({
  period: z.string(),
  approved: z.number(),
  rejected: z.number(),
  pending: z.number(),
  totalBudgetValue: z.number(),
  averageApprovalTime: z.number(),
})

// Approval bottleneck analysis
export const ApprovalBottleneckSchema = z.object({
  type: z.string(),
  severity: z.enum(['low', 'medium', 'high']),
  description: z.string(),
  affectedItems: z.number(),
  averageDelay: z.number(),
  impactScore: z.number(),
  recommendations: z.array(z.string()).optional(),
})

// Department metrics
export const DepartmentMetricsSchema = z.object({
  approved: z.number(),
  rejected: z.number(),
  pending: z.number(),
  averageApprovalTime: z.number(),
})

// Extended approval analytics
export const ExtendedApprovalAnalyticsSchema = z.object({
  totalApprovals: z.number(),
  pendingApprovals: z.number(),
  approvedToday: z.number(),
  rejectedToday: z.number(),
  averageApprovalTime: z.number(),
  approvalRate: z.number(),
  rejectionRate: z.number(),
  escalationRate: z.number(),
  delegationRate: z.number(),
  overdueApprovals: z.number(),
  totalBudgetValue: z.number(),
  averageBudgetValue: z.number(),
  statusBreakdown: z.record(z.string(), z.number()),
  departmentMetrics: z.record(z.string(), DepartmentMetricsSchema),
  bottlenecks: z.array(ApprovalBottleneckSchema),
})

// Export types
export type ApprovalTimeMetrics = z.infer<typeof ApprovalTimeMetricsSchema>
export type ApproverPerformance = z.infer<typeof ApproverPerformanceSchema>
export type ApprovalTrendData = z.infer<typeof ApprovalTrendDataSchema>
export type ApprovalBottleneck = z.infer<typeof ApprovalBottleneckSchema>
export type DepartmentMetrics = z.infer<typeof DepartmentMetricsSchema>
export type ExtendedApprovalAnalytics = z.infer<typeof ExtendedApprovalAnalyticsSchema>
