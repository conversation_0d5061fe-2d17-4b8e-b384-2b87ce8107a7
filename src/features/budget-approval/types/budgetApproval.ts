import { z } from 'zod'

// Budget approval states
export const BudgetApprovalStateSchema = z.enum([
  'draft',
  'pending_approval',
  'under_review',
  'approved',
  'active',
  'rejected',
  'cancelled',
  'expired',
  'amended',
  'under_amendment_review',
  'escalated',
])

// Budget approval transition types
export const BudgetApprovalTransitionSchema = z.enum([
  'submit_for_approval',
  'approve',
  'reject',
  'cancel',
  'expire',
  'amend',
  'escalate',
  'delegate',
  'return_for_revision',
  'auto_approve',
])

// Approval level types
export const ApprovalLevelSchema = z.enum(['level_1', 'level_2', 'level_3', 'level_4', 'executive'])

// Approval chain configuration
export const ApprovalChainConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string().optional(),
  levels: z.array(
    z.object({
      level: ApprovalLevelSchema,
      name: z.string(),
      requiredApprovals: z.number().min(1),
      approvers: z.array(z.string()), // User IDs
      departments: z.array(z.string()).optional(),
      amountThreshold: z.number().optional(),
      canDelegate: z.boolean().default(true),
      timeoutHours: z.number().default(72),
      escalationLevel: ApprovalLevelSchema.optional(),
    })
  ),
  isActive: z.boolean().default(true),
  createdAt: z.string(),
  updatedAt: z.string(),
  createdBy: z.string(),
})

// Budget approval comment
export const BudgetApprovalCommentSchema = z.object({
  id: z.string(),
  budgetId: z.string(),
  approvalId: z.string().optional(),
  userId: z.string(),
  userName: z.string(),
  userRole: z.string(),
  content: z.string().min(1),
  type: z.enum(['comment', 'feedback', 'rejection_reason', 'escalation_note']),
  isInternal: z.boolean().default(false),
  createdAt: z.string(),
  updatedAt: z.string(),
  attachments: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        url: z.string(),
        size: z.number(),
        type: z.string(),
      })
    )
    .optional(),
})

// Budget approval step
export const BudgetApprovalStepSchema = z.object({
  id: z.string(),
  budgetId: z.string(),
  level: ApprovalLevelSchema,
  approverId: z.string(),
  approverName: z.string(),
  approverEmail: z.string(),
  approverRole: z.string(),
  department: z.string(),
  status: z.enum(['pending', 'approved', 'rejected', 'delegated', 'escalated']),
  decision: z.enum(['approve', 'reject', 'delegate', 'escalate']).optional(),
  comments: z.string().optional(),
  decidedAt: z.string().optional(),
  delegatedTo: z.string().optional(),
  escalatedTo: z.string().optional(),
  timeoutAt: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
})

// Budget approval workflow
export const BudgetApprovalWorkflowSchema = z.object({
  id: z.string(),
  budgetId: z.string(),
  currentState: BudgetApprovalStateSchema,
  previousState: BudgetApprovalStateSchema.optional(),
  approvalChainId: z.string(),
  currentLevel: ApprovalLevelSchema.optional(),
  steps: z.array(BudgetApprovalStepSchema),
  metadata: z.object({
    submittedBy: z.string(),
    submittedAt: z.string(),
    lastModifiedBy: z.string().optional(),
    lastModifiedAt: z.string().optional(),
    priority: z.enum(['low', 'medium', 'high', 'critical']).default('medium'),
    riskLevel: z.enum(['low', 'medium', 'high']).default('medium'),
    autoApprovalEligible: z.boolean().default(false),
    complianceChecks: z.array(
      z.object({
        name: z.string(),
        status: z.enum(['passed', 'failed', 'pending']),
        details: z.string().optional(),
      })
    ),
  }),
  escalationHistory: z
    .array(
      z.object({
        escalatedAt: z.string(),
        escalatedFrom: z.string(),
        escalatedTo: z.string(),
        reason: z.string(),
        escalatedBy: z.string(),
      })
    )
    .optional(),
  delegationHistory: z
    .array(
      z.object({
        delegatedAt: z.string(),
        delegatedFrom: z.string(),
        delegatedTo: z.string(),
        reason: z.string(),
        delegatedBy: z.string(),
      })
    )
    .optional(),
  createdAt: z.string(),
  updatedAt: z.string(),
})

// Approval request details
export const ApprovalRequestDetailsSchema = z.object({
  id: z.string(),
  budgetId: z.string(),
  workflowId: z.string(),
  title: z.string(),
  description: z.string(),
  totalAmount: z.number(),
  currency: z.string(),
  department: z.string(),
  trainingType: z.string(),
  submittedBy: z.string(),
  submittedAt: z.string(),
  currentState: BudgetApprovalStateSchema,
  currentLevel: ApprovalLevelSchema.optional(),
  priority: z.enum(['low', 'medium', 'high', 'critical']),
  riskLevel: z.enum(['low', 'medium', 'high']),
  requiresAction: z.boolean(),
  actionRequired: z.enum(['approve', 'reject', 'delegate', 'escalate']).optional(),
  deadline: z.string().optional(),
  attachments: z
    .array(
      z.object({
        id: z.string(),
        name: z.string(),
        url: z.string(),
        size: z.number(),
        type: z.string(),
      })
    )
    .optional(),
  complianceStatus: z.enum(['compliant', 'non_compliant', 'pending']),
  budgetDetails: z.object({
    items: z.array(
      z.object({
        category: z.string(),
        description: z.string(),
        estimatedCost: z.number(),
        quantity: z.number(),
      })
    ),
    schedules: z.array(
      z.object({
        title: z.string(),
        startDate: z.string(),
        endDate: z.string(),
        participants: z.number(),
      })
    ),
  }),
})

// Approval analytics
export const ApprovalAnalyticsSchema = z.object({
  totalApprovals: z.number(),
  pendingApprovals: z.number(),
  approvedToday: z.number(),
  rejectedToday: z.number(),
  averageApprovalTime: z.number(), // in hours
  approvalRate: z.number(), // percentage
  rejectionRate: z.number(), // percentage
  escalationRate: z.number(), // percentage
  delegationRate: z.number(), // percentage
  approvalsByLevel: z.record(z.string(), z.number()),
  approvalsByDepartment: z.record(z.string(), z.number()),
  averageTimeByLevel: z.record(z.string(), z.number()),
  bottleneckLevels: z.array(
    z.object({
      level: ApprovalLevelSchema,
      averageTime: z.number(),
      pendingCount: z.number(),
    })
  ),
  topPerformers: z.array(
    z.object({
      userId: z.string(),
      userName: z.string(),
      approvalsCount: z.number(),
      averageTime: z.number(),
    })
  ),
  complianceMetrics: z.object({
    totalChecks: z.number(),
    passedChecks: z.number(),
    failedChecks: z.number(),
    pendingChecks: z.number(),
  }),
})

// Approval dashboard filters
export const ApprovalDashboardFilterSchema = z.object({
  state: z.array(BudgetApprovalStateSchema).optional(),
  level: z.array(ApprovalLevelSchema).optional(),
  department: z.array(z.string()).optional(),
  priority: z.array(z.enum(['low', 'medium', 'high', 'critical'])).optional(),
  riskLevel: z.array(z.enum(['low', 'medium', 'high'])).optional(),
  dateRange: z
    .object({
      start: z.string(),
      end: z.string(),
    })
    .optional(),
  assignedTo: z.string().optional(),
  submittedBy: z.string().optional(),
  requiresAction: z.boolean().optional(),
  search: z.string().optional(),
})

// Bulk approval operations
export const BulkApprovalOperationSchema = z.object({
  ids: z.array(z.string()),
  action: z.enum(['approve', 'reject', 'delegate', 'escalate']),
  comments: z.string().optional(),
  delegatedTo: z.string().optional(),
  escalatedTo: z.string().optional(),
  applyToAll: z.boolean().default(false),
})

// Approval SLA configuration
export const ApprovalSLAConfigSchema = z.object({
  id: z.string(),
  name: z.string(),
  level: ApprovalLevelSchema,
  targetHours: z.number(), // Target approval time
  warningHours: z.number(), // Warning threshold
  criticalHours: z.number(), // Critical threshold
  escalationHours: z.number(), // When to escalate
  isActive: z.boolean().default(true),
  createdAt: z.string(),
  updatedAt: z.string(),
})

// Approval delegation
export const ApprovalDelegationSchema = z.object({
  id: z.string(),
  delegatorId: z.string(),
  delegatorName: z.string(),
  delegateId: z.string(),
  delegateName: z.string(),
  level: ApprovalLevelSchema,
  departments: z.array(z.string()),
  startDate: z.string(),
  endDate: z.string(),
  isActive: z.boolean(),
  reason: z.string(),
  createdAt: z.string(),
  createdBy: z.string(),
})

// Export types
export type BudgetApprovalState = z.infer<typeof BudgetApprovalStateSchema>
export type BudgetApprovalTransition = z.infer<typeof BudgetApprovalTransitionSchema>
export type ApprovalLevel = z.infer<typeof ApprovalLevelSchema>
export type ApprovalChainConfig = z.infer<typeof ApprovalChainConfigSchema>
export type BudgetApprovalComment = z.infer<typeof BudgetApprovalCommentSchema>
export type BudgetApprovalStep = z.infer<typeof BudgetApprovalStepSchema>
export type BudgetApprovalWorkflow = z.infer<typeof BudgetApprovalWorkflowSchema>
export type ApprovalRequestDetails = z.infer<typeof ApprovalRequestDetailsSchema>
export type ApprovalAnalytics = z.infer<typeof ApprovalAnalyticsSchema>
export type ApprovalDashboardFilter = z.infer<typeof ApprovalDashboardFilterSchema>
export type BulkApprovalOperation = z.infer<typeof BulkApprovalOperationSchema>
export type ApprovalSLAConfig = z.infer<typeof ApprovalSLAConfigSchema>
export type ApprovalDelegation = z.infer<typeof ApprovalDelegationSchema>

// Type guards
export const isValidBudgetApprovalState = (state: any): state is BudgetApprovalState => {
  return BudgetApprovalStateSchema.safeParse(state).success
}

export const isValidApprovalTransition = (
  transition: any
): transition is BudgetApprovalTransition => {
  return BudgetApprovalTransitionSchema.safeParse(transition).success
}

export const isValidApprovalLevel = (level: any): level is ApprovalLevel => {
  return ApprovalLevelSchema.safeParse(level).success

  // Additional analytics types for detailed reporting
  export const ApprovalTimeMetricsSchema = z.object({
    averageApprovalTime: z.number(),
    medianApprovalTime: z.number(),
    p95ApprovalTime: z.number(),
    p99ApprovalTime: z.number(),
    slaTarget: z.number(),
    slaComplianceRate: z.number(),
    fastestApproval: z.number(),
    slowestApproval: z.number(),
    timeByState: z.record(z.string(), z.number()),
    timeByDepartment: z.record(z.string(), z.number()),
  })

  export const ApproverPerformanceSchema = z.object({
    approverId: z.string(),
    approverName: z.string(),
    department: z.string(),
    totalApprovals: z.number(),
    approvalRate: z.number(),
    averageApprovalTime: z.number(),
    efficiencyScore: z.number(),
    workloadScore: z.number(),
    qualityScore: z.number(),
  })

  export const ApprovalTrendDataSchema = z.object({
    period: z.string(),
    approved: z.number(),
    rejected: z.number(),
    pending: z.number(),
    totalBudgetValue: z.number(),
    averageApprovalTime: z.number(),
  })

  export const ApprovalBottleneckSchema = z.object({
    type: z.string(),
    severity: z.enum(['low', 'medium', 'high']),
    description: z.string(),
    affectedItems: z.number(),
    averageDelay: z.number(),
    impactScore: z.number(),
    recommendations: z.array(z.string()).optional(),
  })

  // Department metrics type
  export const DepartmentMetricsSchema = z.object({
    approved: z.number(),
    rejected: z.number(),
    pending: z.number(),
    averageApprovalTime: z.number(),
  })

  // Extended analytics with additional properties
  export const ExtendedApprovalAnalyticsSchema = ApprovalAnalyticsSchema.extend({
    overdueApprovals: z.number().optional(),
    totalBudgetValue: z.number().optional(),
    averageBudgetValue: z.number().optional(),
    statusBreakdown: z.record(z.string(), z.number()).optional(),
    departmentMetrics: z.record(DepartmentMetricsSchema).optional(),
    bottlenecks: z.array(ApprovalBottleneckSchema).optional(),
  })

  // Export additional types
  export type ApprovalTimeMetrics = z.infer<typeof ApprovalTimeMetricsSchema>
  export type ApproverPerformance = z.infer<typeof ApproverPerformanceSchema>
  export type ApprovalTrendData = z.infer<typeof ApprovalTrendDataSchema>
  export type ApprovalBottleneck = z.infer<typeof ApprovalBottleneckSchema>
  export type DepartmentMetrics = z.infer<typeof DepartmentMetricsSchema>
  export type ExtendedApprovalAnalytics = z.infer<typeof ExtendedApprovalAnalyticsSchema>
}
