import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Textarea } from '@/shared/components/ui/textarea'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import { Avatar } from '@/shared/components/ui/avatar'
import { Progress } from '@/shared/components/ui/progress'
import { Separator } from '@/shared/components/ui/separator'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Calendar,
  DollarSign,
  FileText,
  MessageSquare,
  History,
  Shield,
  TrendingUp,
  Users,
  ArrowRight,
  ArrowUp,
} from 'lucide-react'
import type {
  BudgetApprovalWorkflow,
  BudgetApprovalStep,
  BudgetApprovalComment,
  BudgetApprovalState,
} from '../types/budgetApproval'
import { budgetApprovalWorkflowService } from '../services/budgetApprovalWorkflowService'

interface BudgetApprovalRequestDetailsProps {
  workflowId: string
  userId: string
  userName: string
  userRole: string
  isOpen: boolean
  onClose: () => void
  onActionComplete?: () => void
}

export const BudgetApprovalRequestDetails: React.FC<BudgetApprovalRequestDetailsProps> = ({
  workflowId,
  userId,
  userName,
  userRole,
  isOpen,
  onClose,
  onActionComplete,
}) => {
  const [workflow, setWorkflow] = useState<BudgetApprovalWorkflow | null>(null)
  const [comments, setComments] = useState('')
  const [delegateTo, setDelegateTo] = useState('')
  const [escalateTo, setEscalateTo] = useState('')
  const [loading, setLoading] = useState(false)
  const [activeTab, setActiveTab] = useState('details')

  // Load workflow data
  useEffect(() => {
    if (isOpen && workflowId) {
      loadWorkflow()
    }
  }, [isOpen, workflowId])

  const loadWorkflow = () => {
    const workflowData = budgetApprovalWorkflowService.getWorkflow(workflowId)
    setWorkflow(workflowData || null)
  }

  // Handle approval action
  const handleApprovalAction = async (action: 'approve' | 'reject' | 'delegate' | 'escalate') => {
    if (!workflow) return

    try {
      setLoading(true)

      const targetUser =
        action === 'delegate' ? delegateTo : action === 'escalate' ? escalateTo : undefined

      await budgetApprovalWorkflowService.processTransition(
        workflow.id,
        action,
        userId,
        comments,
        targetUser,
        targetUser
      )

      onActionComplete?.()
      onClose()
    } catch (error) {
      console.error(`Error processing ${action}:`, error)
    } finally {
      setLoading(false)
    }
  }

  // Get state badge variant
  const getStateBadgeVariant = (state: BudgetApprovalState) => {
    switch (state) {
      case 'approved':
        return 'default'
      case 'rejected':
      case 'cancelled':
        return 'destructive'
      case 'pending_approval':
      case 'under_review':
        return 'secondary'
      case 'escalated':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  // Get step status icon
  const getStepStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-600" />
      case 'delegated':
        return <ArrowRight className="h-4 w-4 text-blue-600" />
      case 'escalated':
        return <ArrowUp className="h-4 w-4 text-red-600" />
      default:
        return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  // Check if user can take action
  const canTakeAction = workflow?.steps.some(
    (step) => step.approverId === userId && step.status === 'pending'
  )

  if (!workflow) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          <div className="flex h-64 items-center justify-center">
            <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
          </div>
        </DialogContent>
      </Dialog>
    )
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>Budget Approval Request</span>
            <Badge variant={getStateBadgeVariant(workflow.currentState)}>
              {workflow.currentState.replace('_', ' ')}
            </Badge>
          </DialogTitle>
          <DialogDescription>Review and manage budget approval workflow</DialogDescription>
        </DialogHeader>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="mt-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="workflow">Workflow</TabsTrigger>
            <TabsTrigger value="comments">Comments</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="mt-4">
            <div className="space-y-6">
              {/* Budget Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <FileText className="h-5 w-5" />
                    <span>Budget Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Budget ID</Label>
                      <p className="text-muted-foreground text-sm">#{workflow.budgetId}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Training Type</Label>
                      <p className="text-muted-foreground text-sm">Technical Training</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Total Amount</Label>
                      <div className="flex items-center space-x-1">
                        <DollarSign className="text-muted-foreground h-4 w-4" />
                        <span className="font-medium">25,000</span>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Currency</Label>
                      <p className="text-muted-foreground text-sm">USD</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Priority</Label>
                      <Badge
                        variant={
                          workflow.metadata.priority === 'critical'
                            ? 'destructive'
                            : workflow.metadata.priority === 'high'
                              ? 'secondary'
                              : 'outline'
                        }
                      >
                        {workflow.metadata.priority}
                      </Badge>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Risk Level</Label>
                      <Badge
                        variant={
                          workflow.metadata.riskLevel === 'high'
                            ? 'destructive'
                            : workflow.metadata.riskLevel === 'medium'
                              ? 'secondary'
                              : 'outline'
                        }
                      >
                        {workflow.metadata.riskLevel} risk
                      </Badge>
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium">Description</Label>
                    <p className="text-muted-foreground mt-1 text-sm">
                      Advanced technical training for engineering team covering modern development
                      practices and tools.
                    </p>
                  </div>
                </CardContent>
              </Card>

              {/* Submission Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <User className="h-5 w-5" />
                    <span>Submission Information</span>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Submitted By</Label>
                      <div className="mt-1 flex items-center space-x-2">
                        <Avatar
                          className="h-6 w-6"
                          fallback={workflow.metadata.submittedBy.charAt(0).toUpperCase()}
                        />
                        <span className="text-sm">{workflow.metadata.submittedBy}</span>
                      </div>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Submitted At</Label>
                      <p className="text-muted-foreground text-sm">
                        {new Date(workflow.metadata.submittedAt).toLocaleString()}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Compliance Checks */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <Shield className="h-5 w-5" />
                    <span>Compliance Checks</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {workflow.metadata.complianceChecks.map((check, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <span className="text-sm">{check.name}</span>
                        <Badge
                          variant={
                            check.status === 'passed'
                              ? 'default'
                              : check.status === 'failed'
                                ? 'destructive'
                                : 'secondary'
                          }
                        >
                          {check.status}
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="workflow" className="mt-4">
            <div className="space-y-6">
              {/* Current Status */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5" />
                    <span>Approval Progress</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Current Level</span>
                      <Badge variant="outline">{workflow.currentLevel || 'Completed'}</Badge>
                    </div>

                    <div className="space-y-2">
                      {workflow.steps.map((step, index) => (
                        <div
                          key={step.id}
                          className="flex items-center space-x-3 rounded-lg border p-3"
                        >
                          {getStepStatusIcon(step.status)}
                          <div className="flex-1">
                            <div className="flex items-center justify-between">
                              <div>
                                <p className="font-medium">{step.approverName}</p>
                                <p className="text-muted-foreground text-sm">
                                  {step.approverRole} • {step.level}
                                </p>
                              </div>
                              <div className="text-right">
                                <Badge variant="outline">{step.status}</Badge>
                                {step.decidedAt && (
                                  <p className="text-muted-foreground mt-1 text-xs">
                                    {new Date(step.decidedAt).toLocaleString()}
                                  </p>
                                )}
                              </div>
                            </div>
                            {step.comments && (
                              <p className="text-muted-foreground mt-2 text-sm">{step.comments}</p>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="comments" className="mt-4">
            <div className="space-y-4">
              {/* Add Comment */}
              {canTakeAction && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center space-x-2">
                      <MessageSquare className="h-5 w-5" />
                      <span>Add Comment</span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <Textarea
                      placeholder="Enter your comments..."
                      value={comments}
                      onChange={(e) => setComments(e.target.value)}
                      rows={3}
                    />

                    {activeTab === 'comments' && (
                      <div className="flex space-x-2">
                        <Button
                          onClick={() => handleApprovalAction('approve')}
                          disabled={loading || !comments.trim()}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          Approve
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={() => handleApprovalAction('reject')}
                          disabled={loading || !comments.trim()}
                        >
                          Reject
                        </Button>
                        <div className="flex flex-1 space-x-2">
                          <Select value={delegateTo} onValueChange={setDelegateTo}>
                            <SelectTrigger className="flex-1">
                              <SelectValue placeholder="Delegate to..." />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="user1">John Manager</SelectItem>
                              <SelectItem value="user2">Jane Director</SelectItem>
                            </SelectContent>
                          </Select>
                          <Button
                            variant="outline"
                            onClick={() => handleApprovalAction('delegate')}
                            disabled={loading || !delegateTo}
                          >
                            Delegate
                          </Button>
                        </div>
                        <div className="flex flex-1 space-x-2">
                          <Select value={escalateTo} onValueChange={setEscalateTo}>
                            <SelectTrigger className="flex-1">
                              <SelectValue placeholder="Escalate to..." />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="exec1">CEO</SelectItem>
                              <SelectItem value="exec2">CTO</SelectItem>
                            </SelectContent>
                          </Select>
                          <Button
                            variant="outline"
                            onClick={() => handleApprovalAction('escalate')}
                            disabled={loading || !escalateTo}
                          >
                            Escalate
                          </Button>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}

              {/* Comments History */}
              <Card>
                <CardHeader>
                  <CardTitle>Comments History</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Mock comments - would come from comment service */}
                    <div className="flex space-x-3 rounded-lg border p-3">
                      <Avatar className="h-8 w-8" fallback="J" />
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">John Doe</p>
                          <p className="text-muted-foreground text-xs">2 hours ago</p>
                        </div>
                        <p className="text-muted-foreground mt-1 text-sm">
                          This budget looks reasonable for the planned training activities.
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="history" className="mt-4">
            <div className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <History className="h-5 w-5" />
                    <span>Approval History</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-3 rounded-lg border p-3">
                      <div className="h-2 w-2 rounded-full bg-blue-600"></div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <p className="font-medium">Workflow Created</p>
                          <p className="text-muted-foreground text-xs">
                            {new Date(workflow.createdAt).toLocaleString()}
                          </p>
                        </div>
                        <p className="text-muted-foreground mt-1 text-sm">
                          Budget approval workflow initiated by {workflow.metadata.submittedBy}
                        </p>
                      </div>
                    </div>

                    {workflow.escalationHistory?.map((escalation, index) => (
                      <div
                        key={index}
                        className="flex items-center space-x-3 rounded-lg border p-3"
                      >
                        <div className="h-2 w-2 rounded-full bg-red-600"></div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <p className="font-medium">Escalated</p>
                            <p className="text-muted-foreground text-xs">
                              {new Date(escalation.escalatedAt).toLocaleString()}
                            </p>
                          </div>
                          <p className="text-muted-foreground mt-1 text-sm">
                            Escalated from {escalation.escalatedFrom} to {escalation.escalatedTo}
                            {escalation.reason && `: ${escalation.reason}`}
                          </p>
                        </div>
                      </div>
                    ))}

                    {workflow.delegationHistory?.map((delegation, index) => (
                      <div
                        key={index}
                        className="flex items-center space-x-3 rounded-lg border p-3"
                      >
                        <div className="h-2 w-2 rounded-full bg-blue-600"></div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <p className="font-medium">Delegated</p>
                            <p className="text-muted-foreground text-xs">
                              {new Date(delegation.delegatedAt).toLocaleString()}
                            </p>
                          </div>
                          <p className="text-muted-foreground mt-1 text-sm">
                            Delegated from {delegation.delegatedFrom} to {delegation.delegatedTo}
                            {delegation.reason && `: ${delegation.reason}`}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
