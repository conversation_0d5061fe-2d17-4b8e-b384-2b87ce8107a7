import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../../shared/components/ui/card'
import { Badge } from '../../../shared/components/ui/badge'
import { Progress } from '../../../shared/components/ui/progress'
import { Button } from '../../../shared/components/ui/button'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '../../../shared/components/ui/tabs'
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Legend,
} from 'recharts'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  TrendingUp,
  TrendingDown,
  Users,
  DollarSign,
  Calendar,
  Filter,
} from 'lucide-react'
import { budgetApprovalAnalyticsService } from '../services/budgetApprovalAnalyticsService'
import type {
  ExtendedApprovalAnalytics,
  ApprovalTimeMetrics,
  ApproverPerformance,
  ApprovalTrendData,
  ApprovalBottleneck,
} from '../types/analytics'
import type { BudgetApprovalState } from '../types/budgetApproval'

interface BudgetApprovalAnalyticsProps {
  className?: string
}

export const BudgetApprovalAnalytics: React.FC<BudgetApprovalAnalyticsProps> = ({ className }) => {
  const [analytics, setAnalytics] = useState<ExtendedApprovalAnalytics | null>(null)
  const [timeMetrics, setTimeMetrics] = useState<ApprovalTimeMetrics | null>(null)
  const [approverPerformance, setApproverPerformance] = useState<ApproverPerformance[]>([])
  const [trendData, setTrendData] = useState<ApprovalTrendData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dateRange, setDateRange] = useState<'7d' | '30d' | '90d' | '1y'>('30d')

  useEffect(() => {
    loadAnalytics()
  }, [dateRange])

  const loadAnalytics = async () => {
    try {
      setLoading(true)
      setError(null)

      const [analyticsData, timeMetricsData, performanceData, trendDataData] = await Promise.all([
        budgetApprovalAnalyticsService.getApprovalAnalytics(dateRange),
        budgetApprovalAnalyticsService.getApprovalTimeMetrics(dateRange),
        budgetApprovalAnalyticsService.getApproverPerformance(dateRange),
        budgetApprovalAnalyticsService.getApprovalTrends(dateRange),
      ])

      setAnalytics(analyticsData)
      setTimeMetrics(timeMetricsData)
      setApproverPerformance(performanceData)
      setTrendData(trendDataData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load analytics')
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: BudgetApprovalStatus): string => {
    switch (status) {
      case 'approved':
        return '#10b981'
      case 'rejected':
        return '#ef4444'
      case 'pending':
        return '#f59e0b'
      case 'cancelled':
        return '#6b7280'
      case 'expired':
        return '#8b5cf6'
      default:
        return '#6b7280'
    }
  }

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) {
      return `${minutes}m`
    }
    const hours = Math.floor(minutes / 60)
    const remainingMinutes = minutes % 60
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader className="pb-2">
                <div className="h-4 w-3/4 rounded bg-gray-200"></div>
              </CardHeader>
              <CardContent>
                <div className="h-8 w-1/2 rounded bg-gray-200"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <AlertCircle className="mx-auto mb-4 h-12 w-12 text-red-500" />
            <h3 className="mb-2 text-lg font-semibold text-gray-900">Error Loading Analytics</h3>
            <p className="mb-4 text-gray-600">{error}</p>
            <Button onClick={loadAnalytics}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (!analytics || !timeMetrics) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="text-center">
            <p className="text-gray-600">No analytics data available</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const statusData = Object.entries(analytics.statusBreakdown).map(([status, count]) => ({
    name: status.charAt(0).toUpperCase() + status.slice(1),
    value: count,
    color: getStatusColor(status),
  }))

  const departmentData = Object.entries(analytics.departmentMetrics).map(
    ([department, metrics]) => ({
      department,
      pending: metrics.pending,
      approved: metrics.approved,
      rejected: metrics.rejected,
      avgTime: metrics.averageApprovalTime,
    })
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Budget Approval Analytics</h2>
        <div className="flex gap-2">
          {(['7d', '30d', '90d', '1y'] as const).map((range) => (
            <Button
              key={range}
              variant={dateRange === range ? 'default' : 'outline'}
              size="sm"
              onClick={() => setDateRange(range)}
            >
              {range === '7d'
                ? '7 Days'
                : range === '30d'
                  ? '30 Days'
                  : range === '90d'
                    ? '90 Days'
                    : '1 Year'}
            </Button>
          ))}
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Approvals</CardTitle>
            <CheckCircle className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.totalApprovals}</div>
            <p className="text-muted-foreground text-xs">{analytics.approvalRate}% approval rate</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Requests</CardTitle>
            <Clock className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics.pendingApprovals}</div>
            <p className="text-muted-foreground text-xs">{analytics.overdueApprovals} overdue</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Approval Time</CardTitle>
            <TrendingUp className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {formatDuration(timeMetrics.averageApprovalTime)}
            </div>
            <p className="text-muted-foreground text-xs">
              SLA: {formatDuration(timeMetrics.slaTarget)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Budget Value</CardTitle>
            <DollarSign className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(analytics.totalBudgetValue)}</div>
            <p className="text-muted-foreground text-xs">
              {formatCurrency(analytics.averageBudgetValue)} avg
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="bottlenecks">Bottlenecks</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Status Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Approval Status Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={statusData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {statusData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            {/* Department Metrics */}
            <Card>
              <CardHeader>
                <CardTitle>Department Performance</CardTitle>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={departmentData}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="department" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="approved" fill="#10b981" name="Approved" />
                    <Bar dataKey="pending" fill="#f59e0b" name="Pending" />
                    <Bar dataKey="rejected" fill="#ef4444" name="Rejected" />
                  </BarChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          {/* Approver Performance */}
          <Card>
            <CardHeader>
              <CardTitle>Approver Performance</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {approverPerformance.map((approver) => (
                  <div
                    key={approver.approverId}
                    className="flex items-center justify-between rounded-lg border p-4"
                  >
                    <div className="flex-1">
                      <div className="mb-2 flex items-center gap-2">
                        <Users className="h-4 w-4" />
                        <span className="font-medium">{approver.approverName}</span>
                        <Badge variant="outline">{approver.department}</Badge>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm md:grid-cols-4">
                        <div>
                          <span className="text-gray-500">Total:</span>
                          <span className="ml-2 font-medium">{approver.totalApprovals}</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Rate:</span>
                          <span className="ml-2 font-medium">{approver.approvalRate}%</span>
                        </div>
                        <div>
                          <span className="text-gray-500">Avg Time:</span>
                          <span className="ml-2 font-medium">
                            {formatDuration(approver.averageApprovalTime)}
                          </span>
                        </div>
                        <div>
                          <span className="text-gray-500">Efficiency:</span>
                          <span className="ml-2 font-medium">{approver.efficiencyScore}%</span>
                        </div>
                      </div>
                    </div>
                    <div className="ml-4">
                      <Progress value={approver.efficiencyScore} className="w-20" />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          {/* Approval Trends */}
          <Card>
            <CardHeader>
              <CardTitle>Approval Trends Over Time</CardTitle>
            </CardHeader>
            <CardContent>
              <ResponsiveContainer width="100%" height={400}>
                <LineChart data={trendData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="period" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line
                    type="monotone"
                    dataKey="approved"
                    stroke="#10b981"
                    strokeWidth={2}
                    name="Approved"
                  />
                  <Line
                    type="monotone"
                    dataKey="rejected"
                    stroke="#ef4444"
                    strokeWidth={2}
                    name="Rejected"
                  />
                  <Line
                    type="monotone"
                    dataKey="pending"
                    stroke="#f59e0b"
                    strokeWidth={2}
                    name="Pending"
                  />
                </LineChart>
              </ResponsiveContainer>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bottlenecks" className="space-y-4">
          {/* Bottleneck Analysis */}
          <Card>
            <CardHeader>
              <CardTitle>Approval Bottlenecks</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analytics.bottlenecks.map((bottleneck, index) => (
                  <div key={index} className="rounded-lg border p-4">
                    <div className="mb-2 flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <AlertCircle className="h-4 w-4 text-orange-500" />
                        <span className="font-medium">{bottleneck.type}</span>
                        <Badge
                          variant={bottleneck.severity === 'high' ? 'destructive' : 'secondary'}
                        >
                          {bottleneck.severity}
                        </Badge>
                      </div>
                      <span className="text-sm text-gray-500">
                        {bottleneck.affectedItems} items affected
                      </span>
                    </div>
                    <p className="mb-2 text-sm text-gray-600">{bottleneck.description}</p>
                    <div className="flex items-center gap-4 text-sm">
                      <span className="text-gray-500">
                        Avg Delay: {formatDuration(bottleneck.averageDelay)}
                      </span>
                      <span className="text-gray-500">Impact: {bottleneck.impactScore}%</span>
                    </div>
                    {bottleneck.recommendations && (
                      <div className="mt-2">
                        <p className="text-sm font-medium text-gray-700">Recommendations:</p>
                        <ul className="list-inside list-disc text-sm text-gray-600">
                          {bottleneck.recommendations.map((rec, i) => (
                            <li key={i}>{rec}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
