import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import { Avatar } from '@/shared/components/ui/avatar'
import { Progress } from '@/shared/components/ui/progress'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu'
import {
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  User,
  Calendar,
  DollarSign,
  Filter,
  Search,
  MoreHorizontal,
  TrendingUp,
  Users,
  FileText,
  Bell,
} from 'lucide-react'
import type {
  BudgetApprovalWorkflow,
  ApprovalDashboardFilter,
  ApprovalAnalytics,
  BudgetApprovalState,
} from '../types/budgetApproval'
import { budgetApprovalWorkflowService } from '../services/budgetApprovalWorkflowService'

interface BudgetApprovalDashboardProps {
  userId: string
  userName: string
  userRole: string
}

export const BudgetApprovalDashboard: React.FC<BudgetApprovalDashboardProps> = ({
  userId,
  userName,
  userRole,
}) => {
  const [workflows, setWorkflows] = useState<BudgetApprovalWorkflow[]>([])
  const [analytics, setAnalytics] = useState<ApprovalAnalytics | null>(null)
  const [filter, setFilter] = useState<ApprovalDashboardFilter>({})
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedTab, setSelectedTab] = useState('pending')
  const [loading, setLoading] = useState(true)

  // Load data
  useEffect(() => {
    loadData()
  }, [userId, filter, searchQuery])

  const loadData = async () => {
    try {
      setLoading(true)

      // Get workflows for user
      const userWorkflows = budgetApprovalWorkflowService.getWorkflowsForUser(userId, {
        ...filter,
        search: searchQuery || undefined,
      })

      // Get analytics
      const approvalAnalytics = budgetApprovalWorkflowService.getApprovalAnalytics()

      setWorkflows(userWorkflows)
      setAnalytics(approvalAnalytics)
    } catch (error) {
      console.error('Error loading approval data:', error)
    } finally {
      setLoading(false)
    }
  }

  // Handle approval action
  const handleApprovalAction = async (
    workflowId: string,
    action: 'approve' | 'reject' | 'delegate' | 'escalate',
    comments?: string,
    targetUser?: string
  ) => {
    try {
      await budgetApprovalWorkflowService.processTransition(
        workflowId,
        action,
        userId,
        comments,
        targetUser,
        targetUser
      )
      await loadData() // Refresh data
    } catch (error) {
      console.error(`Error processing ${action}:`, error)
    }
  }

  // Get state badge variant
  const getStateBadgeVariant = (state: BudgetApprovalState) => {
    switch (state) {
      case 'approved':
        return 'default'
      case 'rejected':
      case 'cancelled':
        return 'destructive'
      case 'pending_approval':
      case 'under_review':
        return 'secondary'
      case 'escalated':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  // Get state color
  const getStateColor = (state: BudgetApprovalState) => {
    switch (state) {
      case 'approved':
        return 'text-green-600'
      case 'rejected':
      case 'cancelled':
        return 'text-red-600'
      case 'pending_approval':
      case 'under_review':
        return 'text-yellow-600'
      case 'escalated':
        return 'text-red-600'
      default:
        return 'text-gray-600'
    }
  }

  // Filter workflows by tab
  const getFilteredWorkflows = () => {
    switch (selectedTab) {
      case 'pending':
        return workflows.filter(
          (w) =>
            ['pending_approval', 'under_review', 'escalated'].includes(w.currentState) &&
            w.steps.some((step) => step.approverId === userId && step.status === 'pending')
        )
      case 'approved':
        return workflows.filter((w) => w.currentState === 'approved')
      case 'rejected':
        return workflows.filter((w) => ['rejected', 'cancelled'].includes(w.currentState))
      case 'all':
        return workflows
      default:
        return workflows
    }
  }

  // Get pending count for user
  const getPendingCount = () => {
    return workflows.filter(
      (w) =>
        ['pending_approval', 'under_review', 'escalated'].includes(w.currentState) &&
        w.steps.some((step) => step.approverId === userId && step.status === 'pending')
    ).length
  }

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header with Analytics */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <Clock className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{getPendingCount()}</div>
            <p className="text-muted-foreground text-xs">Requiring your action</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Approved Today</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.approvedToday || 0}</div>
            <p className="text-muted-foreground text-xs">
              +{analytics?.approvalRate.toFixed(1)}% rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Average Time</CardTitle>
            <TrendingUp className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {analytics?.averageApprovalTime.toFixed(1) || 0}h
            </div>
            <p className="text-muted-foreground text-xs">Approval cycle time</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Workflows</CardTitle>
            <FileText className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{analytics?.totalApprovals || 0}</div>
            <p className="text-muted-foreground text-xs">All approval workflows</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Budget Approval Dashboard</CardTitle>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
                <Input
                  placeholder="Search budgets..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-64 pl-8"
                />
              </div>
              <Select
                value={filter.state?.[0] || ''}
                onValueChange={(value) =>
                  setFilter((prev) => ({
                    ...prev,
                    state: value ? [value as BudgetApprovalState] : undefined,
                  }))
                }
              >
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="All States" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All States</SelectItem>
                  <SelectItem value="pending_approval">Pending Approval</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="escalated">Escalated</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="pending" className="flex items-center space-x-2">
                <Clock className="h-4 w-4" />
                <span>Pending ({getPendingCount()})</span>
              </TabsTrigger>
              <TabsTrigger value="approved" className="flex items-center space-x-2">
                <CheckCircle className="h-4 w-4" />
                <span>Approved</span>
              </TabsTrigger>
              <TabsTrigger value="rejected" className="flex items-center space-x-2">
                <XCircle className="h-4 w-4" />
                <span>Rejected</span>
              </TabsTrigger>
              <TabsTrigger value="all" className="flex items-center space-x-2">
                <FileText className="h-4 w-4" />
                <span>All</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value={selectedTab} className="mt-4">
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Budget</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Submitted By</TableHead>
                      <TableHead>Current Level</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {getFilteredWorkflows().map((workflow) => {
                      const userStep = workflow.steps.find(
                        (step) => step.approverId === userId && step.status === 'pending'
                      )
                      const requiresAction = !!userStep

                      return (
                        <TableRow
                          key={workflow.id}
                          className={requiresAction ? 'bg-yellow-50' : ''}
                        >
                          <TableCell>
                            <div>
                              <div className="font-medium">Budget #{workflow.budgetId}</div>
                              <div className="text-muted-foreground text-sm">
                                {workflow.metadata.riskLevel} risk
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-1">
                              <DollarSign className="text-muted-foreground h-4 w-4" />
                              <span className="font-medium">
                                {/* Would get actual budget amount */}
                                25,000
                              </span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              <Avatar
                                className="h-8 w-8"
                                fallback={workflow.metadata.submittedBy.charAt(0).toUpperCase()}
                              />
                              <span>{workflow.metadata.submittedBy}</span>
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant="outline">{workflow.currentLevel || 'N/A'}</Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={getStateBadgeVariant(workflow.currentState)}>
                              {workflow.currentState.replace('_', ' ')}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant={
                                workflow.metadata.priority === 'critical'
                                  ? 'destructive'
                                  : workflow.metadata.priority === 'high'
                                    ? 'secondary'
                                    : 'outline'
                              }
                            >
                              {workflow.metadata.priority}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {new Date(workflow.metadata.submittedAt).toLocaleDateString()}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="flex items-center space-x-2">
                              {requiresAction && (
                                <>
                                  <Button
                                    size="sm"
                                    onClick={() => handleApprovalAction(workflow.id, 'approve')}
                                    className="bg-green-600 hover:bg-green-700"
                                  >
                                    Approve
                                  </Button>
                                  <Button
                                    size="sm"
                                    variant="destructive"
                                    onClick={() => handleApprovalAction(workflow.id, 'reject')}
                                  >
                                    Reject
                                  </Button>
                                </>
                              )}
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button variant="ghost" size="sm">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem>View Details</DropdownMenuItem>
                                  <DropdownMenuItem>View History</DropdownMenuItem>
                                  <DropdownMenuSeparator />
                                  {requiresAction && (
                                    <>
                                      <DropdownMenuItem
                                        onClick={() =>
                                          handleApprovalAction(workflow.id, 'delegate')
                                        }
                                      >
                                        Delegate
                                      </DropdownMenuItem>
                                      <DropdownMenuItem
                                        onClick={() =>
                                          handleApprovalAction(workflow.id, 'escalate')
                                        }
                                      >
                                        Escalate
                                      </DropdownMenuItem>
                                    </>
                                  )}
                                  <DropdownMenuItem>Add Comment</DropdownMenuItem>
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </TableCell>
                        </TableRow>
                      )
                    })}
                  </TableBody>
                </Table>

                {getFilteredWorkflows().length === 0 && (
                  <div className="py-8 text-center">
                    <FileText className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                    <h3 className="text-lg font-medium">No approval workflows found</h3>
                    <p className="text-muted-foreground">
                      {selectedTab === 'pending'
                        ? 'No pending approvals requiring your action'
                        : `No ${selectedTab} workflows found`}
                    </p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}
