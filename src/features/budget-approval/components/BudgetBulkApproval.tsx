import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Checkbox } from '@/shared/components/ui/checkbox'
import { Textarea } from '@/shared/components/ui/textarea'
import { Label } from '@/shared/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog'
import { Alert, AlertDescription, AlertTitle } from '@/shared/components/ui/alert'
import { Progress } from '@/shared/components/ui/progress'
import {
  CheckSquare,
  Square,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Users,
  DollarSign,
  Calendar,
  Clock,
  ArrowRight,
  ArrowUp,
  Loader2,
} from 'lucide-react'
import type {
  BudgetApprovalWorkflow,
  BulkApprovalOperation,
  BudgetApprovalState,
} from '../types/budgetApproval'
import { budgetApprovalWorkflowService } from '../services/budgetApprovalWorkflowService'

interface BudgetBulkApprovalProps {
  userId: string
  userName: string
  userRole: string
  isOpen: boolean
  onClose: () => void
  onActionComplete?: () => void
}

export const BudgetBulkApproval: React.FC<BudgetBulkApprovalProps> = ({
  userId,
  userName,
  userRole,
  isOpen,
  onClose,
  onActionComplete,
}) => {
  const [workflows, setWorkflows] = useState<BudgetApprovalWorkflow[]>([])
  const [selectedWorkflows, setSelectedWorkflows] = useState<Set<string>>(new Set())
  const [action, setAction] = useState<'approve' | 'reject' | 'delegate' | 'escalate'>('approve')
  const [comments, setComments] = useState('')
  const [delegateTo, setDelegateTo] = useState('')
  const [escalateTo, setEscalateTo] = useState('')
  const [loading, setLoading] = useState(false)
  const [processing, setProcessing] = useState(false)
  const [progress, setProgress] = useState(0)
  const [results, setResults] = useState<{
    success: number
    failed: number
    errors: string[]
  }>({ success: 0, failed: 0, errors: [] })

  // Load pending workflows
  useEffect(() => {
    if (isOpen) {
      loadPendingWorkflows()
    }
  }, [isOpen, userId])

  const loadPendingWorkflows = () => {
    const userWorkflows = budgetApprovalWorkflowService.getWorkflowsForUser(userId, {
      requiresAction: true,
    })

    // Filter to only show workflows that require user action
    const pendingWorkflows = userWorkflows.filter(
      (workflow) =>
        ['pending_approval', 'under_review', 'escalated'].includes(workflow.currentState) &&
        workflow.steps.some((step) => step.approverId === userId && step.status === 'pending')
    )

    setWorkflows(pendingWorkflows)
  }

  // Handle select all
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      const allIds = new Set(workflows.map((w) => w.id))
      setSelectedWorkflows(allIds)
    } else {
      setSelectedWorkflows(new Set())
    }
  }

  // Handle individual selection
  const handleSelectWorkflow = (workflowId: string, checked: boolean) => {
    const newSelected = new Set(selectedWorkflows)
    if (checked) {
      newSelected.add(workflowId)
    } else {
      newSelected.delete(workflowId)
    }
    setSelectedWorkflows(newSelected)
  }

  // Get state badge variant
  const getStateBadgeVariant = (state: BudgetApprovalState) => {
    switch (state) {
      case 'approved':
        return 'default'
      case 'rejected':
      case 'cancelled':
        return 'destructive'
      case 'pending_approval':
      case 'under_review':
        return 'secondary'
      case 'escalated':
        return 'destructive'
      default:
        return 'outline'
    }
  }

  // Get priority color
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical':
        return 'text-red-600'
      case 'high':
        return 'text-orange-600'
      case 'medium':
        return 'text-yellow-600'
      case 'low':
        return 'text-green-600'
      default:
        return 'text-gray-600'
    }
  }

  // Handle bulk action
  const handleBulkAction = async () => {
    if (selectedWorkflows.size === 0) return

    try {
      setProcessing(true)
      setProgress(0)
      setResults({ success: 0, failed: 0, errors: [] })

      const workflowIds = Array.from(selectedWorkflows)
      const totalWorkflows = workflowIds.length

      // Create bulk operation
      const bulkOperation: BulkApprovalOperation = {
        ids: workflowIds,
        action,
        comments: comments.trim() || undefined,
        delegatedTo: action === 'delegate' ? delegateTo : undefined,
        escalatedTo: action === 'escalate' ? escalateTo : undefined,
        applyToAll: false,
      }

      // Process workflows one by one for better progress tracking
      for (let i = 0; i < workflowIds.length; i++) {
        const workflowId = workflowIds[i]

        try {
          await budgetApprovalWorkflowService.processTransition(
            workflowId,
            action,
            userId,
            comments,
            delegateTo,
            escalateTo
          )
          setResults((prev) => ({ ...prev, success: prev.success + 1 }))
        } catch (error) {
          console.error(`Error processing workflow ${workflowId}:`, error)
          setResults((prev) => ({
            ...prev,
            failed: prev.failed + 1,
            errors: [
              ...prev.errors,
              `Workflow ${workflowId}: ${error instanceof Error ? error.message : 'Unknown error'}`,
            ],
          }))
        }

        // Update progress
        const currentProgress = Math.round(((i + 1) / totalWorkflows) * 100)
        setProgress(currentProgress)
      }

      // Clear selection after processing
      setSelectedWorkflows(new Set())
      setComments('')
      setDelegateTo('')
      setEscalateTo('')

      onActionComplete?.()
    } catch (error) {
      console.error('Error in bulk action:', error)
      setResults((prev) => ({
        ...prev,
        failed: prev.failed + 1,
        errors: [
          ...prev.errors,
          `Bulk operation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        ],
      }))
    } finally {
      setProcessing(false)
      setProgress(0)
    }
  }

  const isActionDisabled =
    selectedWorkflows.size === 0 ||
    processing ||
    (action === 'delegate' && !delegateTo) ||
    (action === 'escalate' && !escalateTo)

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] max-w-6xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center space-x-2">
            <CheckSquare className="h-5 w-5" />
            <span>Bulk Approval Actions</span>
          </DialogTitle>
          <DialogDescription>
            Select multiple budgets to perform bulk approval actions
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Summary Stats */}
          <div className="grid grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4 text-yellow-600" />
                  <div>
                    <p className="text-sm font-medium">Pending</p>
                    <p className="text-2xl font-bold">{workflows.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <CheckSquare className="h-4 w-4 text-blue-600" />
                  <div>
                    <p className="text-sm font-medium">Selected</p>
                    <p className="text-2xl font-bold">{selectedWorkflows.size}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-4 w-4 text-green-600" />
                  <div>
                    <p className="text-sm font-medium">Total Amount</p>
                    <p className="text-2xl font-bold">$125K</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-orange-600" />
                  <div>
                    <p className="text-sm font-medium">High Priority</p>
                    <p className="text-2xl font-bold">
                      {
                        workflows.filter(
                          (w) =>
                            w.metadata.priority === 'high' || w.metadata.priority === 'critical'
                        ).length
                      }
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Action Configuration */}
          <Card>
            <CardHeader>
              <CardTitle>Action Configuration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="action">Action</Label>
                  <Select value={action} onValueChange={(value: any) => setAction(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="approve">Approve</SelectItem>
                      <SelectItem value="reject">Reject</SelectItem>
                      <SelectItem value="delegate">Delegate</SelectItem>
                      <SelectItem value="escalate">Escalate</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {action === 'delegate' && (
                  <div>
                    <Label htmlFor="delegateTo">Delegate To</Label>
                    <Select value={delegateTo} onValueChange={setDelegateTo}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select delegate..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="user1">John Manager</SelectItem>
                        <SelectItem value="user2">Jane Director</SelectItem>
                        <SelectItem value="user3">Bob Senior</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {action === 'escalate' && (
                  <div>
                    <Label htmlFor="escalateTo">Escalate To</Label>
                    <Select value={escalateTo} onValueChange={setEscalateTo}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select escalation target..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="exec1">CEO</SelectItem>
                        <SelectItem value="exec2">CTO</SelectItem>
                        <SelectItem value="exec3">CFO</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}
              </div>

              <div>
                <Label htmlFor="comments">Comments (Optional)</Label>
                <Textarea
                  id="comments"
                  placeholder="Enter comments for this bulk action..."
                  value={comments}
                  onChange={(e) => setComments(e.target.value)}
                  rows={3}
                />
              </div>
            </CardContent>
          </Card>

          {/* Workflows Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Select Workflows</span>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="select-all"
                    checked={selectedWorkflows.size === workflows.length && workflows.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                  <Label htmlFor="select-all" className="text-sm">
                    Select All ({selectedWorkflows.size}/{workflows.length})
                  </Label>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {workflows.length === 0 ? (
                <div className="py-8 text-center">
                  <CheckSquare className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
                  <h3 className="text-lg font-medium">No pending approvals</h3>
                  <p className="text-muted-foreground">
                    You don't have any pending approvals requiring your action
                  </p>
                </div>
              ) : (
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">Select</TableHead>
                      <TableHead>Budget</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Submitted</TableHead>
                      <TableHead>Current Level</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {workflows.map((workflow) => (
                      <TableRow key={workflow.id} className="hover:bg-muted/50 cursor-pointer">
                        <TableCell>
                          <Checkbox
                            checked={selectedWorkflows.has(workflow.id)}
                            onCheckedChange={(checked) =>
                              handleSelectWorkflow(workflow.id, checked as boolean)
                            }
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">Budget #{workflow.budgetId}</div>
                            <div className="text-muted-foreground text-sm">
                              {workflow.metadata.riskLevel} risk • {workflow.metadata.priority}{' '}
                              priority
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-1">
                            <DollarSign className="text-muted-foreground h-4 w-4" />
                            <span className="font-medium">25,000</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant="outline"
                            className={getPriorityColor(workflow.metadata.priority)}
                          >
                            {workflow.metadata.priority}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={getStateBadgeVariant(workflow.currentState)}>
                            {workflow.currentState.replace('_', ' ')}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            {new Date(workflow.metadata.submittedAt).toLocaleDateString()}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{workflow.currentLevel || 'N/A'}</Badge>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              )}
            </CardContent>
          </Card>

          {/* Processing Progress */}
          {processing && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Loader2 className="h-5 w-5 animate-spin" />
                  <span>Processing Bulk Action</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Progress value={progress} className="w-full" />
                <p className="text-muted-foreground text-sm">
                  Processing {selectedWorkflows.size} workflows... {progress}% complete
                </p>

                {results.success > 0 && (
                  <Alert>
                    <CheckCircle className="h-4 w-4" />
                    <AlertTitle>Success</AlertTitle>
                    <AlertDescription>
                      {results.success} workflows processed successfully
                    </AlertDescription>
                  </Alert>
                )}

                {results.failed > 0 && (
                  <Alert variant="destructive">
                    <XCircle className="h-4 w-4" />
                    <AlertTitle>Errors</AlertTitle>
                    <AlertDescription>
                      {results.failed} workflows failed to process
                      {results.errors.length > 0 && (
                        <ul className="mt-2 text-sm">
                          {results.errors.map((error, index) => (
                            <li key={index}>• {error}</li>
                          ))}
                        </ul>
                      )}
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={onClose} disabled={processing}>
            Cancel
          </Button>
          <Button
            onClick={handleBulkAction}
            disabled={isActionDisabled}
            className={
              action === 'approve'
                ? 'bg-green-600 hover:bg-green-700'
                : action === 'reject'
                  ? 'bg-red-600 hover:bg-red-700'
                  : ''
            }
          >
            {processing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                {action === 'approve' && <CheckCircle className="mr-2 h-4 w-4" />}
                {action === 'reject' && <XCircle className="mr-2 h-4 w-4" />}
                {action === 'delegate' && <ArrowRight className="mr-2 h-4 w-4" />}
                {action === 'escalate' && <ArrowUp className="mr-2 h-4 w-4" />}
                {action.charAt(0).toUpperCase() + action.slice(1)} {selectedWorkflows.size} Budget
                {selectedWorkflows.size !== 1 ? 's' : ''}
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
