import React from 'react'
import { Badge } from '../../../shared/components/ui/badge'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../../shared/components/ui/card'
import { Progress } from '../../../shared/components/ui/progress'
import { Separator } from '../../../shared/components/ui/separator'
import { CalendarDays, Clock, MapPin, Users, User, Target, Award } from 'lucide-react'
import type { CalendarEvent } from '../types'
import type { EventType, EventPriority, EventStatus } from '../types'

interface TrainingEventProps {
  event: CalendarEvent
  onClick?: () => void
  compact?: boolean
}

/**
 * Training session event component
 */
export const TrainingSessionEvent: React.FC<TrainingEventProps> = ({
  event,
  onClick,
  compact = false,
}) => {
  const sessionType = event.metadata?.sessionType as string
  const instructor = (event.instructor || event.metadata?.instructor) as string
  const location = (event.location || event.metadata?.location) as string
  const capacity = event.metadata?.capacity as number
  const enrolledCount = event.metadata?.enrolledCount as number

  const getSessionTypeColor = (type?: string) => {
    switch (type) {
      case 'lecture':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'lab':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'workshop':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'assessment':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority?: EventPriority) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-500'
      case 'high':
        return 'bg-orange-500'
      case 'medium':
        return 'bg-yellow-500'
      case 'low':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  if (compact) {
    return (
      <div
        className="cursor-pointer rounded-lg border p-2 transition-colors hover:bg-gray-50"
        onClick={onClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`h-2 w-2 rounded-full ${getPriorityColor(event.priority)}`} />
            <span className="truncate text-sm font-medium">{event.title}</span>
          </div>
          <Badge className={getSessionTypeColor(sessionType)} variant="outline">
            {sessionType || 'session'}
          </Badge>
        </div>
        {(instructor || location) && (
          <div className="mt-1 flex items-center gap-2 text-xs text-gray-600">
            {instructor && (
              <div className="flex items-center gap-1">
                <User className="h-3 w-3" />
                <span>{instructor}</span>
              </div>
            )}
            {location && (
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{location}</span>
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <Card className="cursor-pointer transition-shadow hover:shadow-md" onClick={onClick}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">{event.title}</CardTitle>
          <div className="flex items-center gap-2">
            <div className={`h-3 w-3 rounded-full ${getPriorityColor(event.priority)}`} />
            <Badge className={getSessionTypeColor(sessionType)} variant="outline">
              {sessionType || 'session'}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {event.description && <p className="text-sm text-gray-600">{event.description}</p>}

        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <CalendarDays className="h-4 w-4" />
            <span>{new Date(event.start as string).toLocaleDateString()}</span>
          </div>
          <div className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            <span>
              {new Date(event.start as string).toLocaleTimeString([], {
                hour: '2-digit',
                minute: '2-digit',
              })}
              {event.end &&
                ` - ${new Date(event.end as string).toLocaleTimeString([], {
                  hour: '2-digit',
                  minute: '2-digit',
                })}`}
            </span>
          </div>
        </div>

        {(instructor || location) && (
          <div className="flex items-center gap-4 text-sm text-gray-600">
            {instructor && (
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>{instructor}</span>
              </div>
            )}
            {location && (
              <div className="flex items-center gap-1">
                <MapPin className="h-4 w-4" />
                <span>{location}</span>
              </div>
            )}
          </div>
        )}

        {capacity && (
          <div className="flex items-center gap-2 text-sm">
            <Users className="h-4 w-4 text-gray-600" />
            <span className="text-gray-600">
              Enrollment: {enrolledCount || 0}/{capacity}
            </span>
            <Progress value={((enrolledCount || 0) / capacity) * 100} className="h-2 flex-1" />
          </div>
        )}

        {event.metadata?.resources &&
          Array.isArray(event.metadata.resources) &&
          event.metadata.resources.length > 0 && (
            <div>
              <span className="text-sm font-medium text-gray-700">Resources:</span>
              <div className="mt-1 flex flex-wrap gap-1">
                {(event.metadata.resources as string[]).map((resource: string, index: number) => (
                  <Badge key={index} variant="secondary" className="text-xs">
                    {resource}
                  </Badge>
                ))}
              </div>
            </div>
          )}
      </CardContent>
    </Card>
  )
}

/**
 * Training milestone event component
 */
export const TrainingMilestoneEvent: React.FC<TrainingEventProps> = ({
  event,
  onClick,
  compact = false,
}) => {
  const milestoneType = event.metadata?.milestoneType as string
  const weight = event.metadata?.weight as number
  const status = event.status as EventStatus

  const getMilestoneTypeColor = (type?: string) => {
    switch (type) {
      case 'assessment':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'project':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'certification':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'review':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'deadline':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusColor = (status?: EventStatus) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500'
      case 'in_progress':
        return 'bg-blue-500'
      case 'scheduled':
        return 'bg-yellow-500'
      case 'cancelled':
        return 'bg-red-500'
      case 'postponed':
        return 'bg-gray-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getMilestoneIcon = (type?: string) => {
    switch (type) {
      case 'assessment':
        return <Target className="h-4 w-4" />
      case 'project':
        return <Award className="h-4 w-4" />
      case 'certification':
        return <Award className="h-4 w-4" />
      case 'review':
        return <Target className="h-4 w-4" />
      case 'deadline':
        return <Clock className="h-4 w-4" />
      default:
        return <Target className="h-4 w-4" />
    }
  }

  if (compact) {
    return (
      <div
        className="cursor-pointer rounded-lg border p-2 transition-colors hover:bg-gray-50"
        onClick={onClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`h-2 w-2 rounded-full ${getStatusColor(status)}`} />
            <span className="truncate text-sm font-medium">{event.title}</span>
          </div>
          <Badge className={getMilestoneTypeColor(milestoneType)} variant="outline">
            {milestoneType || 'milestone'}
          </Badge>
        </div>
        {weight && (
          <div className="mt-1 flex items-center gap-2 text-xs text-gray-600">
            <span>Weight: {weight}%</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <Card className="cursor-pointer transition-shadow hover:shadow-md" onClick={onClick}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`h-3 w-3 rounded-full ${getStatusColor(status)}`} />
            {getMilestoneIcon(milestoneType)}
            <CardTitle className="text-lg">{event.title}</CardTitle>
          </div>
          <Badge className={getMilestoneTypeColor(milestoneType)} variant="outline">
            {milestoneType || 'milestone'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {event.description && <p className="text-sm text-gray-600">{event.description}</p>}

        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <CalendarDays className="h-4 w-4" />
            <span>{new Date(event.start as string).toLocaleDateString()}</span>
          </div>
          {weight && (
            <div className="flex items-center gap-1">
              <Target className="h-4 w-4" />
              <span>Weight: {weight}%</span>
            </div>
          )}
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium text-gray-700">Status:</span>
          <Badge className={`${getStatusColor(status)} text-white`} variant="secondary">
            {status || 'scheduled'}
          </Badge>
        </div>

        {event.metadata?.dependencies &&
          Array.isArray(event.metadata.dependencies) &&
          event.metadata.dependencies.length > 0 && (
            <div>
              <span className="text-sm font-medium text-gray-700">Dependencies:</span>
              <div className="mt-1 flex flex-wrap gap-1">
                {(event.metadata.dependencies as string[]).map((depId: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    Milestone {depId}
                  </Badge>
                ))}
              </div>
            </div>
          )}
      </CardContent>
    </Card>
  )
}

/**
 * Training program event component (for program start/end events)
 */
export const TrainingProgramEvent: React.FC<TrainingEventProps> = ({
  event,
  onClick,
  compact = false,
}) => {
  const eventType = event.metadata?.eventType as string
  const instructor = (event.instructor || event.metadata?.instructor) as string
  const department = event.metadata?.department as string
  const maxCapacity = event.metadata?.maxCapacity as number
  const currentEnrollment = event.metadata?.currentEnrollment as number

  const getEventTypeColor = (type?: string) => {
    switch (type) {
      case 'program_start':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'program_end':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getEventTypeIcon = (type?: string) => {
    switch (type) {
      case 'program_start':
        return <CalendarDays className="h-4 w-4" />
      case 'program_end':
        return <Award className="h-4 w-4" />
      default:
        return <CalendarDays className="h-4 w-4" />
    }
  }

  if (compact) {
    return (
      <div
        className="cursor-pointer rounded-lg border p-2 transition-colors hover:bg-gray-50"
        onClick={onClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getEventTypeIcon(eventType)}
            <span className="truncate text-sm font-medium">{event.title}</span>
          </div>
          <Badge className={getEventTypeColor(eventType)} variant="outline">
            {eventType?.replace('_', ' ') || 'program'}
          </Badge>
        </div>
        {instructor && (
          <div className="mt-1 flex items-center gap-2 text-xs text-gray-600">
            <User className="h-3 w-3" />
            <span>{instructor}</span>
          </div>
        )}
      </div>
    )
  }

  return (
    <Card className="cursor-pointer transition-shadow hover:shadow-md" onClick={onClick}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {getEventTypeIcon(eventType)}
            <CardTitle className="text-lg">{event.title}</CardTitle>
          </div>
          <Badge className={getEventTypeColor(eventType)} variant="outline">
            {eventType?.replace('_', ' ') || 'program'}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {event.description && <p className="text-sm text-gray-600">{event.description}</p>}

        <div className="flex items-center gap-4 text-sm text-gray-600">
          <div className="flex items-center gap-1">
            <CalendarDays className="h-4 w-4" />
            <span>{new Date(event.start as string).toLocaleDateString()}</span>
          </div>
        </div>

        {(instructor || department) && (
          <div className="flex items-center gap-4 text-sm text-gray-600">
            {instructor && (
              <div className="flex items-center gap-1">
                <User className="h-4 w-4" />
                <span>{instructor}</span>
              </div>
            )}
            {department && (
              <div className="flex items-center gap-1">
                <span>Department: {department}</span>
              </div>
            )}
          </div>
        )}

        {maxCapacity && (
          <div className="flex items-center gap-2 text-sm">
            <Users className="h-4 w-4 text-gray-600" />
            <span className="text-gray-600">
              Enrollment: {currentEnrollment || 0}/{maxCapacity}
            </span>
            <Progress
              value={((currentEnrollment || 0) / maxCapacity) * 100}
              className="h-2 flex-1"
            />
          </div>
        )}
      </CardContent>
    </Card>
  )
}

/**
 * Main training event component that renders the appropriate event type
 */
export const TrainingEvent: React.FC<TrainingEventProps> = ({
  event,
  onClick,
  compact = false,
}) => {
  const eventType = event.metadata?.eventType as string

  switch (eventType) {
    case 'training_session':
      return <TrainingSessionEvent event={event} onClick={onClick} compact={compact} />
    case 'milestone':
      return <TrainingMilestoneEvent event={event} onClick={onClick} compact={compact} />
    case 'program_start':
    case 'program_end':
      return <TrainingProgramEvent event={event} onClick={onClick} compact={compact} />
    default:
      // Default to training session for backward compatibility
      return <TrainingSessionEvent event={event} onClick={onClick} compact={compact} />
  }
}

/**
 * Training events list component
 */
interface TrainingEventsListProps {
  events: CalendarEvent[]
  onEventClick?: (event: CalendarEvent) => void
  compact?: boolean
  groupByType?: boolean
}

export const TrainingEventsList: React.FC<TrainingEventsListProps> = ({
  events,
  onEventClick,
  compact = false,
  groupByType = false,
}) => {
  if (events.length === 0) {
    return (
      <div className="py-8 text-center text-gray-500">
        <CalendarDays className="mx-auto mb-2 h-12 w-12 opacity-50" />
        <p>No training events found</p>
      </div>
    )
  }

  if (!groupByType) {
    return (
      <div className="space-y-3">
        {events.map((event) => (
          <TrainingEvent
            key={event.id}
            event={event}
            onClick={() => onEventClick?.(event)}
            compact={compact}
          />
        ))}
      </div>
    )
  }

  // Group events by type
  const groupedEvents = events.reduce(
    (groups, event) => {
      const eventType = (event.metadata?.eventType as string) || 'training_session'
      if (!groups[eventType]) {
        groups[eventType] = []
      }
      groups[eventType].push(event)
      return groups
    },
    {} as Record<string, CalendarEvent[]>
  )

  return (
    <div className="space-y-6">
      {Object.entries(groupedEvents).map(([eventType, eventList]) => (
        <div key={eventType}>
          <div className="mb-3 flex items-center gap-2">
            <h3 className="text-lg font-semibold capitalize">{eventType.replace('_', ' ')}</h3>
            <Badge variant="secondary">{eventList.length}</Badge>
          </div>
          <Separator className="mb-3" />
          <div className="space-y-3">
            {eventList.map((event) => (
              <TrainingEvent
                key={event.id}
                event={event}
                onClick={() => onEventClick?.(event)}
                compact={compact}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

export default TrainingEvent
