import React, { useState } from 'react'
import { But<PERSON> } from '@/shared/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog'
import { ScrollArea } from '@/shared/components/ui/scroll-area'
import { Separator } from '@/shared/components/ui/separator'
import { DateUtils } from '@/lib/dateUtils'
import {
  Bell,
  Calendar,
  Clock,
  Mail,
  Smartphone,
  MessageSquare,
  Settings,
  Check,
  X,
  Plus,
  Trash,
  Edit,
  History,
  Filter,
} from 'lucide-react'

import type {
  CalendarNotification,
  EventReminder,
  NotificationPreferences,
  NotificationHistoryEntry,
  ReminderType,
  CalendarNotificationUserSettings,
} from '../types'

// Temporary Switch component since it's not available
interface SwitchProps {
  checked?: boolean
  onCheckedChange?: (checked: boolean) => void
}

function Switch({ checked, onCheckedChange }: SwitchProps) {
  return (
    <button
      type="button"
      role="switch"
      aria-checked={checked}
      onClick={() => onCheckedChange?.(!checked)}
      className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
        checked ? 'bg-blue-600' : 'bg-gray-200'
      }`}
    >
      <span
        className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
          checked ? 'translate-x-6' : 'translate-x-1'
        }`}
      />
    </button>
  )
}

interface NotificationsProps {
  notifications: CalendarNotification[]
  reminders: EventReminder[]
  preferences?: NotificationPreferences
  settings?: CalendarNotificationUserSettings
  onMarkAsRead?: (id: string) => void
  onDeleteNotification?: (id: string) => void
  onCancelReminder?: (id: string) => void
  onUpdatePreferences?: (preferences: Partial<NotificationPreferences>) => void
  onUpdateSettings?: (settings: Partial<CalendarNotificationUserSettings>) => void
}

/**
 * Main Notifications Component
 */
export function Notifications({
  notifications,
  reminders,
  preferences,
  settings,
  onMarkAsRead,
  onDeleteNotification,
  onCancelReminder,
  onUpdatePreferences,
  onUpdateSettings,
}: NotificationsProps) {
  const [activeTab, setActiveTab] = useState('notifications')

  return (
    <div className="mx-auto w-full max-w-4xl p-4">
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="reminders">Reminders</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="notifications" className="mt-4">
          <NotificationList
            notifications={notifications}
            onMarkAsRead={onMarkAsRead}
            onDeleteNotification={onDeleteNotification}
          />
        </TabsContent>

        <TabsContent value="reminders" className="mt-4">
          <ReminderList reminders={reminders} onCancelReminder={onCancelReminder} />
        </TabsContent>

        <TabsContent value="preferences" className="mt-4">
          <NotificationPreferences
            preferences={preferences}
            settings={settings}
            onUpdatePreferences={onUpdatePreferences}
            onUpdateSettings={onUpdateSettings}
          />
        </TabsContent>

        <TabsContent value="history" className="mt-4">
          <NotificationHistory />
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * Notification List Component
 */
interface NotificationListProps {
  notifications: CalendarNotification[]
  onMarkAsRead?: (id: string) => void
  onDeleteNotification?: (id: string) => void
}

function NotificationList({
  notifications,
  onMarkAsRead,
  onDeleteNotification,
}: NotificationListProps) {
  const [filter, setFilter] = useState<'all' | 'unread' | 'read'>('all')

  const filteredNotifications = notifications.filter((notification) => {
    if (filter === 'unread') return !notification.readAt
    if (filter === 'read') return !!notification.readAt
    return true
  })

  const unreadCount = notifications.filter((n) => !n.readAt).length

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notifications
            {unreadCount > 0 && <Badge variant="destructive">{unreadCount}</Badge>}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Select value={filter} onValueChange={(value: any) => setFilter(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="unread">Unread</SelectItem>
                <SelectItem value="read">Read</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          {filteredNotifications.length === 0 ? (
            <div className="text-muted-foreground py-8 text-center">
              <Bell className="mx-auto mb-4 h-12 w-12 opacity-50" />
              <p>No notifications found</p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredNotifications.map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  onMarkAsRead={onMarkAsRead}
                  onDeleteNotification={onDeleteNotification}
                />
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

/**
 * Individual Notification Item Component
 */
interface NotificationItemProps {
  notification: CalendarNotification
  onMarkAsRead?: (id: string) => void
  onDeleteNotification?: (id: string) => void
}

function NotificationItem({
  notification,
  onMarkAsRead,
  onDeleteNotification,
}: NotificationItemProps) {
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'event_reminder':
        return <Clock className="h-4 w-4 text-blue-500" />
      case 'event_created':
        return <Calendar className="h-4 w-4 text-green-500" />
      case 'event_updated':
        return <Edit className="h-4 w-4 text-yellow-500" />
      case 'event_deleted':
      case 'event_cancelled':
        return <X className="h-4 w-4 text-red-500" />
      case 'resource_conflict':
        return <Bell className="h-4 w-4 text-orange-500" />
      default:
        return <Bell className="h-4 w-4 text-gray-500" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low':
        return 'bg-gray-100 text-gray-800 border-gray-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  return (
    <div
      className={`rounded-lg border p-4 ${!notification.readAt ? 'border-blue-200 bg-blue-50' : 'bg-white'}`}
    >
      <div className="flex items-start justify-between">
        <div className="flex flex-1 items-start gap-3">
          <div className="mt-1">{getNotificationIcon(notification.type)}</div>
          <div className="min-w-0 flex-1">
            <div className="mb-1 flex items-center gap-2">
              <h4
                className={`font-medium ${!notification.readAt ? 'text-foreground' : 'text-muted-foreground'}`}
              >
                {notification.title}
              </h4>
              <Badge variant="outline" className={getPriorityColor(notification.priority)}>
                {notification.priority}
              </Badge>
            </div>
            <p className="text-muted-foreground mb-2 text-sm">{notification.message}</p>
            <div className="text-muted-foreground flex items-center gap-4 text-xs">
              <span className="flex items-center gap-1">
                <Calendar className="h-3 w-3" />
                {DateUtils.formatRelative(notification.scheduledFor)}
              </span>
              <span className="flex items-center gap-1">
                {getChannelIcon(notification.reminderType)}
                {notification.reminderType}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-1">
          {!notification.readAt && onMarkAsRead && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onMarkAsRead(notification.id)}
              className="h-8 w-8 p-0"
            >
              <Check className="h-4 w-4" />
            </Button>
          )}
          {onDeleteNotification && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onDeleteNotification(notification.id)}
              className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
            >
              <Trash className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

/**
 * Reminder List Component
 */
interface ReminderListProps {
  reminders: EventReminder[]
  onCancelReminder?: (id: string) => void
}

function ReminderList({ reminders, onCancelReminder }: ReminderListProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Event Reminders
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          {reminders.length === 0 ? (
            <div className="text-muted-foreground py-8 text-center">
              <Clock className="mx-auto mb-4 h-12 w-12 opacity-50" />
              <p>No reminders scheduled</p>
            </div>
          ) : (
            <div className="space-y-3">
              {reminders.map((reminder) => (
                <ReminderItem
                  key={reminder.id}
                  reminder={reminder}
                  onCancelReminder={onCancelReminder}
                />
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  )
}

/**
 * Individual Reminder Item Component
 */
interface ReminderItemProps {
  reminder: EventReminder
  onCancelReminder?: (id: string) => void
}

function ReminderItem({ reminder, onCancelReminder }: ReminderItemProps) {
  return (
    <div className="rounded-lg border bg-white p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Clock className="h-4 w-4 text-blue-500" />
          <div>
            <h4 className="font-medium">{reminder.metadata.eventTitle}</h4>
            <p className="text-muted-foreground text-sm">
              {DateUtils.formatRelative(reminder.scheduledFor)}
            </p>
            <div className="mt-1 flex items-center gap-2">
              <Badge variant="outline">{reminder.reminderType}</Badge>
              {reminder.sent ? (
                <Badge variant="secondary" className="text-green-600">
                  Sent
                </Badge>
              ) : (
                <Badge variant="outline" className="text-blue-600">
                  Scheduled
                </Badge>
              )}
            </div>
          </div>
        </div>
        {!reminder.sent && onCancelReminder && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onCancelReminder(reminder.id)}
            className="text-red-500 hover:text-red-700"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
}

/**
 * Notification Preferences Component
 */
interface NotificationPreferencesProps {
  preferences?: NotificationPreferences
  settings?: CalendarNotificationUserSettings
  onUpdatePreferences?: (preferences: Partial<NotificationPreferences>) => void
  onUpdateSettings?: (settings: Partial<CalendarNotificationUserSettings>) => void
}

function NotificationPreferences({
  preferences,
  settings,
  onUpdatePreferences,
  onUpdateSettings,
}: NotificationPreferencesProps) {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            General Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Enable Notifications</Label>
              <p className="text-muted-foreground text-sm">
                Turn all calendar notifications on or off
              </p>
            </div>
            <Switch
              checked={settings?.enabled ?? true}
              onCheckedChange={(enabled: boolean) => onUpdateSettings?.({ enabled })}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Notification Channels</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              <div>
                <Label>Email Notifications</Label>
                <p className="text-muted-foreground text-sm">Receive notifications via email</p>
              </div>
            </div>
            <Switch
              checked={settings?.emailNotifications ?? true}
              onCheckedChange={(emailNotifications: boolean) =>
                onUpdateSettings?.({ emailNotifications })
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Smartphone className="h-4 w-4" />
              <div>
                <Label>Push Notifications</Label>
                <p className="text-muted-foreground text-sm">
                  Receive push notifications on mobile
                </p>
              </div>
            </div>
            <Switch
              checked={settings?.pushNotifications ?? true}
              onCheckedChange={(pushNotifications: boolean) =>
                onUpdateSettings?.({ pushNotifications })
              }
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              <div>
                <Label>In-App Notifications</Label>
                <p className="text-muted-foreground text-sm">Show notifications within the app</p>
              </div>
            </div>
            <Switch
              checked={settings?.inAppNotifications ?? true}
              onCheckedChange={(inAppNotifications: boolean) =>
                onUpdateSettings?.({ inAppNotifications })
              }
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Reminder Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>Default Event Reminder (minutes before)</Label>
            <Input
              type="number"
              value={settings?.reminders?.beforeEvent ?? 15}
              onChange={(e) =>
                onUpdateSettings?.({
                  reminders: {
                    beforeEvent: parseInt(e.target.value) || 15,
                    beforeDeadline: settings?.reminders?.beforeDeadline || 60,
                  },
                })
              }
              min={1}
              max={1440}
            />
          </div>

          <div>
            <Label>Default Deadline Reminder (minutes before)</Label>
            <Input
              type="number"
              value={settings?.reminders?.beforeDeadline ?? 60}
              onChange={(e) =>
                onUpdateSettings?.({
                  reminders: {
                    beforeEvent: settings?.reminders?.beforeEvent || 15,
                    beforeDeadline: parseInt(e.target.value) || 60,
                  },
                })
              }
              min={1}
              max={1440}
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Quiet Hours</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <Label>Enable Quiet Hours</Label>
              <p className="text-muted-foreground text-sm">
                Limit notifications during specific hours
              </p>
            </div>
            <Switch
              checked={settings?.quietHours?.enabled ?? false}
              onCheckedChange={(enabled: boolean) =>
                onUpdateSettings?.({
                  quietHours: {
                    enabled,
                    startTime: settings?.quietHours?.startTime || '22:00',
                    endTime: settings?.quietHours?.endTime || '08:00',
                  },
                })
              }
            />
          </div>

          {settings?.quietHours?.enabled && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label>Start Time</Label>
                <Input
                  type="time"
                  value={settings.quietHours.startTime}
                  onChange={(e) =>
                    onUpdateSettings?.({
                      quietHours: {
                        ...settings.quietHours,
                        startTime: e.target.value,
                      },
                    })
                  }
                />
              </div>
              <div>
                <Label>End Time</Label>
                <Input
                  type="time"
                  value={settings.quietHours.endTime}
                  onChange={(e) =>
                    onUpdateSettings?.({
                      quietHours: {
                        ...settings.quietHours,
                        endTime: e.target.value,
                      },
                    })
                  }
                />
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Notification History Component
 */
function NotificationHistory() {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Notification History
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-muted-foreground py-8 text-center">
          <History className="mx-auto mb-4 h-12 w-12 opacity-50" />
          <p>Notification history will be available here</p>
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Helper function to get channel icon
 */
function getChannelIcon(type: ReminderType) {
  switch (type) {
    case 'email':
      return <Mail className="h-3 w-3" />
    case 'push':
      return <Smartphone className="h-3 w-3" />
    case 'in_app':
      return <MessageSquare className="h-3 w-3" />
    case 'sms':
      return <Smartphone className="h-3 w-3" />
    default:
      return <Bell className="h-3 w-3" />
  }
}

export default Notifications
