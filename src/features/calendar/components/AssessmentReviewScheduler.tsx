import React, { useState, useMemo } from 'react'
import { Card } from '../../../shared/components/ui/card'
import { Badge } from '../../../shared/components/ui/badge'
import { Button } from '../../../shared/components/ui/button'
import { Input } from '../../../shared/components/ui/input'
import { Label } from '../../../shared/components/ui/label'
import { Textarea } from '../../../shared/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../shared/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../shared/components/ui/tabs'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '../../../shared/components/ui/dialog'
import {
  Calendar,
  Clock,
  User,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertCircle,
  Users,
  Mail,
  Phone,
} from 'lucide-react'
import type { CalendarEvent } from '../types'
import type { AssessmentEventMetadata } from '../services/assessmentIntegrationService'
import type { Assessment, AssessmentReview } from '../../../shared/types/assessment'

/**
 * Helper function to safely convert DateInput to Date
 */
const safeToDate = (dateInput: any): Date => {
  if (dateInput instanceof Date) {
    return dateInput
  }
  if (typeof dateInput === 'string' || typeof dateInput === 'number') {
    return new Date(dateInput)
  }
  // Handle other cases (like arrays from FullCalendar)
  if (Array.isArray(dateInput)) {
    return new Date(
      dateInput[0],
      dateInput[1],
      dateInput[2],
      dateInput[3] || 0,
      dateInput[4] || 0,
      dateInput[5] || 0
    )
  }
  // Fallback
  return new Date()
}

/**
 * Props for AssessmentReviewScheduler component
 */
interface AssessmentReviewSchedulerProps {
  assessments: Assessment[]
  reviewEvents: CalendarEvent[]
  onScheduleReview?: (assessmentId: string, reviewData: ReviewScheduleData) => void
  onEditReview?: (reviewId: string, reviewData: ReviewScheduleData) => void
  onDeleteReview?: (reviewId: string) => void
  className?: string
}

/**
 * Review schedule data interface
 */
interface ReviewScheduleData {
  assessmentId: string
  reviewerId: string
  reviewerRole: string
  scheduledDate: string
  scheduledTime: string
  duration: number
  location: string
  notes?: string
  reminderSettings: {
    email: boolean
    sms: boolean
    advanceNotice: number
  }
}

/**
 * Assessment review scheduler component
 */
export const AssessmentReviewScheduler: React.FC<AssessmentReviewSchedulerProps> = ({
  assessments,
  reviewEvents,
  onScheduleReview,
  onEditReview,
  onDeleteReview,
  className = '',
}) => {
  const [selectedAssessment, setSelectedAssessment] = useState<Assessment | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [filterStatus, setFilterStatus] = useState<string>('all')
  const [filterReviewer, setFilterReviewer] = useState<string>('all')
  const [isScheduleDialogOpen, setIsScheduleDialogOpen] = useState(false)
  const [scheduleData, setScheduleData] = useState<ReviewScheduleData>({
    assessmentId: '',
    reviewerId: '',
    reviewerRole: '',
    scheduledDate: '',
    scheduledTime: '',
    duration: 60,
    location: '',
    notes: '',
    reminderSettings: {
      email: true,
      sms: false,
      advanceNotice: 24,
    },
  })

  // Filter assessments based on search and filters
  const filteredAssessments = useMemo(() => {
    return assessments.filter((assessment) => {
      const matchesSearch =
        !searchQuery ||
        assessment.trainee?.userName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        assessment.id.toLowerCase().includes(searchQuery.toLowerCase())

      const matchesStatus = filterStatus === 'all' || assessment.status === filterStatus

      return matchesSearch && matchesStatus
    })
  }, [assessments, searchQuery, filterStatus])

  // Get assessments that need reviews
  const assessmentsNeedingReviews = useMemo(() => {
    return filteredAssessments.filter(
      (assessment) =>
        assessment.status === 'in_progress' ||
        (assessment.status === 'completed' &&
          (!assessment.reviews || assessment.reviews.length === 0))
    )
  }, [filteredAssessments])

  // Get scheduled review events
  const scheduledReviews = useMemo(() => {
    return reviewEvents.filter((event) => {
      const metadata = event.metadata as AssessmentEventMetadata
      return metadata.type === 'review'
    })
  }, [reviewEvents])

  // Get reviewer statistics
  const reviewerStats = useMemo(() => {
    const stats: Record<string, { name: string; role: string; count: number; upcoming: number }> =
      {}

    assessments.forEach((assessment) => {
      assessment.reviews?.forEach((review) => {
        const key = review.reviewerId
        if (!stats[key]) {
          stats[key] = {
            name: review.reviewerId, // Would need to fetch user details
            role: review.reviewerRole,
            count: 0,
            upcoming: 0,
          }
        }
        stats[key].count++
      })
    })

    scheduledReviews.forEach((event) => {
      const metadata = event.metadata as AssessmentEventMetadata
      if (metadata.reviewerId) {
        const key = metadata.reviewerId
        if (stats[key]) {
          stats[key].upcoming++
        } else {
          stats[key] = {
            name: metadata.reviewerId,
            role: metadata.instructor || 'Unknown',
            count: 0,
            upcoming: 1,
          }
        }
      }
    })

    return stats
  }, [assessments, scheduledReviews])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatReviewDate = (dateInput: any) => {
    const date = safeToDate(dateInput)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  const handleScheduleReview = (assessment: Assessment) => {
    setSelectedAssessment(assessment)
    setScheduleData((prev) => ({
      ...prev,
      assessmentId: assessment.id,
      scheduledDate: new Date().toISOString().split('T')[0],
      scheduledTime: '09:00',
    }))
    setIsScheduleDialogOpen(true)
  }

  const handleSubmitReview = () => {
    if (onScheduleReview && selectedAssessment) {
      onScheduleReview(selectedAssessment.id, scheduleData)
      setIsScheduleDialogOpen(false)
      setScheduleData({
        assessmentId: '',
        reviewerId: '',
        reviewerRole: '',
        scheduledDate: '',
        scheduledTime: '',
        duration: 60,
        location: '',
        notes: '',
        reminderSettings: {
          email: true,
          sms: false,
          advanceNotice: 24,
        },
      })
      setSelectedAssessment(null)
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Assessment Review Scheduler</h2>
          <p className="text-gray-600">Schedule and manage assessment review sessions</p>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Pending Reviews</p>
              <p className="text-2xl font-bold">{assessmentsNeedingReviews.length}</p>
            </div>
            <AlertCircle className="h-8 w-8 text-yellow-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Scheduled Reviews</p>
              <p className="text-2xl font-bold">{scheduledReviews.length}</p>
            </div>
            <Calendar className="h-8 w-8 text-blue-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Reviewers</p>
              <p className="text-2xl font-bold">{Object.keys(reviewerStats).length}</p>
            </div>
            <Users className="h-8 w-8 text-green-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed Reviews</p>
              <p className="text-2xl font-bold">
                {assessments.filter((a) => a.reviews && a.reviews.length > 0).length}
              </p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card className="p-4">
        <div className="flex items-center space-x-4">
          <div className="relative flex-1">
            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
            <Input
              placeholder="Search assessments by trainee name or ID..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Filter by status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="overdue">Overdue</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      {/* Main Content */}
      <Tabs defaultValue="pending" className="space-y-4">
        <TabsList>
          <TabsTrigger value="pending">Pending Reviews</TabsTrigger>
          <TabsTrigger value="scheduled">Scheduled Reviews</TabsTrigger>
          <TabsTrigger value="reviewers">Reviewers</TabsTrigger>
        </TabsList>

        <TabsContent value="pending" className="space-y-4">
          <Card className="p-4">
            <h3 className="mb-4 text-lg font-semibold">Assessments Needing Reviews</h3>
            {assessmentsNeedingReviews.length === 0 ? (
              <div className="py-8 text-center text-gray-500">
                <CheckCircle className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>All assessments have scheduled reviews</p>
              </div>
            ) : (
              <div className="space-y-3">
                {assessmentsNeedingReviews.map((assessment) => (
                  <div
                    key={assessment.id}
                    className="flex items-center justify-between rounded-lg border p-4"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="h-2 w-2 rounded-full bg-yellow-500" />
                      <div>
                        <h4 className="font-medium">
                          {assessment.trainee?.userName || 'Unknown Trainee'} - Q
                          {assessment.quarter} {assessment.year}
                        </h4>
                        <p className="text-sm text-gray-600">
                          Status:{' '}
                          <Badge variant="outline" className={getStatusColor(assessment.status)}>
                            {assessment.status}
                          </Badge>
                        </p>
                        <p className="text-sm text-gray-600">
                          Scheduled: {formatReviewDate(assessment.scheduledDate)}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleScheduleReview(assessment)}
                      >
                        <Plus className="mr-2 h-4 w-4" />
                        Schedule Review
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="scheduled" className="space-y-4">
          <Card className="p-4">
            <h3 className="mb-4 text-lg font-semibold">Scheduled Review Sessions</h3>
            {scheduledReviews.length === 0 ? (
              <div className="py-8 text-center text-gray-500">
                <Calendar className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>No scheduled review sessions</p>
              </div>
            ) : (
              <div className="space-y-3">
                {scheduledReviews.map((event) => {
                  const metadata = event.metadata as AssessmentEventMetadata

                  return (
                    <div
                      key={event.id}
                      className="flex items-center justify-between rounded-lg border p-4"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="h-2 w-2 rounded-full bg-blue-500" />
                        <div>
                          <h4 className="font-medium">{event.title}</h4>
                          <p className="text-sm text-gray-600">
                            <Calendar className="mr-1 inline h-4 w-4" />
                            {formatReviewDate(event.start)}
                          </p>
                          <p className="text-sm text-gray-600">
                            <User className="mr-1 inline h-4 w-4" />
                            Reviewer: {metadata.reviewerId}
                          </p>
                          {event.location && (
                            <p className="text-sm text-gray-600">Location: {event.location}</p>
                          )}
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">Scheduled</Badge>
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                        <Button variant="outline" size="sm">
                          <XCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="reviewers" className="space-y-4">
          <Card className="p-4">
            <h3 className="mb-4 text-lg font-semibold">Reviewer Workload</h3>
            {Object.keys(reviewerStats).length === 0 ? (
              <div className="py-8 text-center text-gray-500">
                <Users className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>No reviewers assigned</p>
              </div>
            ) : (
              <div className="space-y-3">
                {Object.entries(reviewerStats).map(([reviewerId, stats]) => (
                  <div
                    key={reviewerId}
                    className="flex items-center justify-between rounded-lg border p-4"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="h-2 w-2 rounded-full bg-green-500" />
                      <div>
                        <h4 className="font-medium">{stats.name}</h4>
                        <p className="text-sm text-gray-600">Role: {stats.role}</p>
                        <p className="text-sm text-gray-600">
                          Total Reviews: {stats.count} | Upcoming: {stats.upcoming}
                        </p>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Badge variant="outline">{stats.count} total</Badge>
                      <Badge variant="secondary">{stats.upcoming} upcoming</Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </Card>
        </TabsContent>
      </Tabs>

      {/* Schedule Review Dialog */}
      <Dialog open={isScheduleDialogOpen} onOpenChange={setIsScheduleDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Schedule Assessment Review</DialogTitle>
          </DialogHeader>

          {selectedAssessment && (
            <div className="space-y-4">
              <div className="rounded-lg bg-gray-50 p-3">
                <h4 className="font-medium">
                  {selectedAssessment.trainee?.userName || 'Unknown Trainee'} - Q
                  {selectedAssessment.quarter} {selectedAssessment.year}
                </h4>
                <p className="text-sm text-gray-600">Assessment ID: {selectedAssessment.id}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="reviewerId">Reviewer ID</Label>
                  <Input
                    id="reviewerId"
                    value={scheduleData.reviewerId}
                    onChange={(e) =>
                      setScheduleData((prev) => ({ ...prev, reviewerId: e.target.value }))
                    }
                    placeholder="Enter reviewer ID"
                  />
                </div>

                <div>
                  <Label htmlFor="reviewerRole">Reviewer Role</Label>
                  <Select
                    value={scheduleData.reviewerRole}
                    onValueChange={(value) =>
                      setScheduleData((prev) => ({ ...prev, reviewerRole: value }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="manager">Manager</SelectItem>
                      <SelectItem value="mentor">Mentor</SelectItem>
                      <SelectItem value="ld_officer">LD Officer</SelectItem>
                      <SelectItem value="technical_lead">Technical Lead</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="scheduledDate">Date</Label>
                  <Input
                    id="scheduledDate"
                    type="date"
                    value={scheduleData.scheduledDate}
                    onChange={(e) =>
                      setScheduleData((prev) => ({ ...prev, scheduledDate: e.target.value }))
                    }
                  />
                </div>

                <div>
                  <Label htmlFor="scheduledTime">Time</Label>
                  <Input
                    id="scheduledTime"
                    type="time"
                    value={scheduleData.scheduledTime}
                    onChange={(e) =>
                      setScheduleData((prev) => ({ ...prev, scheduledTime: e.target.value }))
                    }
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="duration">Duration (minutes)</Label>
                  <Select
                    value={scheduleData.duration.toString()}
                    onValueChange={(value) =>
                      setScheduleData((prev) => ({ ...prev, duration: parseInt(value) }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="30">30 minutes</SelectItem>
                      <SelectItem value="60">1 hour</SelectItem>
                      <SelectItem value="90">1.5 hours</SelectItem>
                      <SelectItem value="120">2 hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    value={scheduleData.location}
                    onChange={(e) =>
                      setScheduleData((prev) => ({ ...prev, location: e.target.value }))
                    }
                    placeholder="Meeting room or virtual link"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea
                  id="notes"
                  value={scheduleData.notes}
                  onChange={(e) => setScheduleData((prev) => ({ ...prev, notes: e.target.value }))}
                  placeholder="Additional notes for the review session"
                  rows={3}
                />
              </div>

              <div>
                <Label>Reminder Settings</Label>
                <div className="mt-2 space-y-2">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="emailReminder"
                      checked={scheduleData.reminderSettings.email}
                      onChange={(e) =>
                        setScheduleData((prev) => ({
                          ...prev,
                          reminderSettings: { ...prev.reminderSettings, email: e.target.checked },
                        }))
                      }
                    />
                    <Label htmlFor="emailReminder" className="flex items-center space-x-2">
                      <Mail className="h-4 w-4" />
                      <span>Email reminder</span>
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="smsReminder"
                      checked={scheduleData.reminderSettings.sms}
                      onChange={(e) =>
                        setScheduleData((prev) => ({
                          ...prev,
                          reminderSettings: { ...prev.reminderSettings, sms: e.target.checked },
                        }))
                      }
                    />
                    <Label htmlFor="smsReminder" className="flex items-center space-x-2">
                      <Phone className="h-4 w-4" />
                      <span>SMS reminder</span>
                    </Label>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Label htmlFor="advanceNotice">Advance notice:</Label>
                    <Select
                      value={scheduleData.reminderSettings.advanceNotice.toString()}
                      onValueChange={(value) =>
                        setScheduleData((prev) => ({
                          ...prev,
                          reminderSettings: {
                            ...prev.reminderSettings,
                            advanceNotice: parseInt(value),
                          },
                        }))
                      }
                    >
                      <SelectTrigger className="w-32">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1">1 hour</SelectItem>
                        <SelectItem value="24">1 day</SelectItem>
                        <SelectItem value="48">2 days</SelectItem>
                        <SelectItem value="168">1 week</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setIsScheduleDialogOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleSubmitReview}>Schedule Review</Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default AssessmentReviewScheduler
