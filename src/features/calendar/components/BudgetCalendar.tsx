import React, { useState, use<PERSON><PERSON>back, useMemo, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../../shared/components/ui/card'
import { Button } from '../../../shared/components/ui/button'
import { Badge } from '../../../shared/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../shared/components/ui/tabs'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../../shared/components/ui/select'
// import { Switch } from '../../../shared/components/ui/switch'
import { Progress } from '../../../shared/components/ui/progress'
import {
  CalendarDays,
  CalendarRange,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  Target,
} from 'lucide-react'
import { cn } from '../../../lib/utils'
import { BudgetCalendarService } from '../services/budgetCalendarService'
import type {
  BudgetCalendarEvent,
  BudgetCalendarFilter,
  BudgetCalendarStats,
  BudgetCalendarView,
  BudgetDisplayOptions,
  BudgetEventType,
} from '../types/budgetCalendar'
import type { CalendarViewType } from '../types/calendar'

interface BudgetCalendarProps {
  className?: string
  initialView?: BudgetCalendarView
  onEventClick?: (event: BudgetCalendarEvent) => void
  onEventCreate?: (eventData: any) => void
  onEventUpdate?: (eventData: any) => void
  onEventDelete?: (eventId: string) => void
  readOnly?: boolean
}

const BudgetCalendar: React.FC<BudgetCalendarProps> = ({
  className,
  initialView,
  onEventClick,
  onEventCreate,
  onEventUpdate,
  onEventDelete,
  readOnly = false,
}) => {
  const [events, setEvents] = useState<BudgetCalendarEvent[]>([])
  const [filteredEvents, setFilteredEvents] = useState<BudgetCalendarEvent[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [selectedView, setSelectedView] = useState<CalendarViewType>('dayGridMonth')
  const [filter, setFilter] = useState<BudgetCalendarFilter>({})
  const [stats, setStats] = useState<BudgetCalendarStats | null>(null)
  const [displayOptions, setDisplayOptions] = useState<BudgetDisplayOptions>({
    colorBy: 'budget_type',
    colorScheme: 'default',
    showBudgetAmount: true,
    showUtilization: true,
    showAttendees: false,
    showInstructor: true,
    showResources: false,
    showProgressBars: true,
    showUtilizationHeatmap: false,
    showConflictIndicators: true,
    showMilestones: true,
    compactMode: false,
    groupByBudget: false,
    showBudgetTotals: true,
  })

  // Default views
  const defaultViews: BudgetCalendarView[] = [
    {
      id: 'timeline',
      name: 'Budget Timeline',
      type: 'timeline',
      defaultViewType: 'dayGridMonth',
      filters: {},
      displayOptions,
      permissions: {
        canCreate: !readOnly,
        canEdit: !readOnly,
        canDelete: !readOnly,
        canApprove: !readOnly,
        canViewFinancials: true,
        canManageResources: !readOnly,
        canExport: true,
      },
    },
    {
      id: 'utilization',
      name: 'Budget Utilization',
      type: 'utilization',
      defaultViewType: 'dayGridMonth',
      filters: {},
      displayOptions: {
        ...displayOptions,
        colorBy: 'utilization',
        showUtilizationHeatmap: true,
        showProgressBars: true,
      },
      permissions: {
        canCreate: false,
        canEdit: false,
        canDelete: false,
        canApprove: false,
        canViewFinancials: true,
        canManageResources: false,
        canExport: true,
      },
    },
    {
      id: 'resources',
      name: 'Resource Calendar',
      type: 'resources',
      defaultViewType: 'timeGridWeek',
      filters: {},
      displayOptions: {
        ...displayOptions,
        showResources: true,
        showInstructor: true,
      },
      permissions: {
        canCreate: !readOnly,
        canEdit: !readOnly,
        canDelete: !readOnly,
        canApprove: false,
        canViewFinancials: false,
        canManageResources: !readOnly,
        canExport: true,
      },
    },
    {
      id: 'deadlines',
      name: 'Deadlines & Approvals',
      type: 'deadlines',
      defaultViewType: 'listWeek',
      filters: {
        budgetType: ['budget_expiry', 'budget_approval_deadline', 'milestone_deadline'],
      },
      displayOptions,
      permissions: {
        canCreate: false,
        canEdit: false,
        canDelete: false,
        canApprove: !readOnly,
        canViewFinancials: true,
        canManageResources: false,
        canExport: true,
      },
    },
    {
      id: 'overview',
      name: 'Multi-Budget Overview',
      type: 'overview',
      defaultViewType: 'multiMonthYear',
      filters: {},
      displayOptions: {
        ...displayOptions,
        groupByBudget: true,
        showBudgetTotals: true,
        compactMode: true,
      },
      permissions: {
        canCreate: false,
        canEdit: false,
        canDelete: false,
        canApprove: false,
        canViewFinancials: true,
        canManageResources: false,
        canExport: true,
      },
    },
  ]

  const [currentView, setCurrentView] = useState<BudgetCalendarView>(initialView || defaultViews[0])

  // Load events and stats
  const loadData = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const [eventsData, statsData] = await Promise.all([
        BudgetCalendarService.getBudgetCalendarEvents(filter),
        BudgetCalendarService.getBudgetCalendarStats(filter),
      ])
      setEvents(eventsData)
      setStats(statsData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load budget calendar data')
    } finally {
      setLoading(false)
    }
  }, [filter])

  useEffect(() => {
    loadData()
  }, [loadData])

  // Apply filters to events
  useEffect(() => {
    let filtered = events

    // Apply view-specific filters
    if (currentView.filters) {
      const viewFilter = { ...filter, ...currentView.filters }
      filtered = events.filter((event) => {
        // Budget type filter
        if (
          viewFilter.budgetType?.length &&
          (!event.budgetType || !viewFilter.budgetType.includes(event.budgetType))
        ) {
          return false
        }
        // Budget status filter
        if (
          viewFilter.budgetStatus?.length &&
          (!event.budgetStatus || !viewFilter.budgetStatus.includes(event.budgetStatus))
        ) {
          return false
        }
        // Budget ID filter
        if (
          viewFilter.budgetId?.length &&
          (!event.budgetId || !viewFilter.budgetId.includes(event.budgetId))
        ) {
          return false
        }
        return true
      })
    }

    setFilteredEvents(filtered)
  }, [events, currentView, filter])

  // Get event color based on display options
  const getEventColor = useCallback(
    (event: BudgetCalendarEvent): string => {
      const { colorBy, colorScheme } = currentView.displayOptions

      switch (colorBy) {
        case 'budget_type':
          return getBudgetTypeColor(event.budgetType, colorScheme)
        case 'utilization':
          return getUtilizationColor(event.utilizationPercentage, colorScheme)
        case 'status':
          return getStatusColor(event.budgetStatus, colorScheme)
        case 'priority':
          return getPriorityColor(event.priority, colorScheme)
        case 'cost':
          return getCostColor(event.budgetAmount, colorScheme)
        default:
          return 'bg-gray-100'
      }
    },
    [currentView.displayOptions]
  )

  // Color helper functions
  const getBudgetTypeColor = (type?: BudgetEventType, scheme = 'default'): string => {
    if (!type) return 'bg-gray-100'

    const colors = {
      default: {
        budget_creation: 'bg-blue-100',
        budget_expiry: 'bg-red-100',
        budget_approval_deadline: 'bg-orange-100',
        training_session: 'bg-green-100',
        milestone_deadline: 'bg-purple-100',
        budget_review: 'bg-yellow-100',
        resource_allocation: 'bg-indigo-100',
        spending_milestone: 'bg-pink-100',
        cost_reminder: 'bg-teal-100',
        utilization_checkpoint: 'bg-cyan-100',
      },
      utilization: {
        budget_creation: 'bg-blue-500',
        budget_expiry: 'bg-red-500',
        budget_approval_deadline: 'bg-orange-500',
        training_session: 'bg-green-500',
        milestone_deadline: 'bg-purple-500',
        budget_review: 'bg-yellow-500',
        resource_allocation: 'bg-indigo-500',
        spending_milestone: 'bg-pink-500',
        cost_reminder: 'bg-teal-500',
        utilization_checkpoint: 'bg-cyan-500',
      },
    }
    const colorScheme = colors[scheme as keyof typeof colors] as Record<string, string>
    const defaultColors = colors.default as Record<string, string>
    return colorScheme?.[type] || defaultColors[type as keyof typeof defaultColors] || '#6B7280'
  }

  const getUtilizationColor = (utilization?: number, scheme = 'default'): string => {
    if (!utilization) return 'bg-gray-100'

    if (scheme === 'utilization') {
      if (utilization >= 90) return 'bg-red-500'
      if (utilization >= 75) return 'bg-orange-500'
      if (utilization >= 50) return 'bg-yellow-500'
      return 'bg-green-500'
    }

    if (utilization >= 90) return 'bg-red-100'
    if (utilization >= 75) return 'bg-orange-100'
    if (utilization >= 50) return 'bg-yellow-100'
    return 'bg-green-100'
  }

  const getStatusColor = (status?: string, scheme = 'default'): string => {
    if (!status) return 'bg-gray-100'

    const colors = {
      default: {
        planned: 'bg-gray-100',
        approved: 'bg-green-100',
        in_progress: 'bg-blue-100',
        completed: 'bg-emerald-100',
        cancelled: 'bg-red-100',
        expired: 'bg-orange-100',
      },
      utilization: {
        planned: 'bg-gray-500',
        approved: 'bg-green-500',
        in_progress: 'bg-blue-500',
        completed: 'bg-emerald-500',
        cancelled: 'bg-red-500',
        expired: 'bg-orange-500',
      },
    }
    const statusColors = colors[scheme as keyof typeof colors] as Record<string, string>
    const defaultColors = colors.default as Record<string, string>
    return (
      statusColors?.[status] || defaultColors[status as keyof typeof defaultColors] || '#6B7280'
    )
  }

  const getPriorityColor = (priority?: string, scheme = 'default'): string => {
    if (!priority) return 'bg-gray-100'

    const colors = {
      default: {
        low: 'bg-gray-100',
        medium: 'bg-blue-100',
        high: 'bg-orange-100',
        urgent: 'bg-red-100',
      },
      utilization: {
        low: 'bg-gray-500',
        medium: 'bg-blue-500',
        high: 'bg-orange-500',
        urgent: 'bg-red-500',
      },
    }
    const priorityColors = colors[scheme as keyof typeof colors] as Record<string, string>
    const defaultColors = colors.default as Record<string, string>
    return (
      priorityColors?.[priority] ||
      defaultColors[priority as keyof typeof defaultColors] ||
      '#6B7280'
    )
  }

  const getCostColor = (amount?: number, scheme = 'default'): string => {
    if (!amount) return 'bg-gray-100'

    if (amount >= 100000) return scheme === 'utilization' ? 'bg-red-500' : 'bg-red-100'
    if (amount >= 50000) return scheme === 'utilization' ? 'bg-orange-500' : 'bg-orange-100'
    if (amount >= 10000) return scheme === 'utilization' ? 'bg-yellow-500' : 'bg-yellow-100'
    return scheme === 'utilization' ? 'bg-green-500' : 'bg-green-100'
  }

  // Handle event click
  const handleEventClick = useCallback(
    (event: BudgetCalendarEvent) => {
      onEventClick?.(event)
    },
    [onEventClick]
  )

  // Handle view change
  const handleViewChange = useCallback((view: BudgetCalendarView) => {
    setCurrentView(view)
    setSelectedView(view.defaultViewType)
  }, [])

  // Handle filter change
  const handleFilterChange = useCallback((newFilter: Partial<BudgetCalendarFilter>) => {
    setFilter((prev) => ({ ...prev, ...newFilter }))
  }, [])

  // Format currency
  const formatCurrency = useCallback((amount?: number, currency?: string): string => {
    if (amount === undefined) return 'N/A'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }, [])

  // Render event card
  const renderEventCard = useCallback(
    (event: BudgetCalendarEvent) => {
      return (
        <div
          key={event.id}
          className={cn(
            'cursor-pointer rounded-lg border p-4 transition-all hover:shadow-md',
            getEventColor(event)
          )}
          onClick={() => handleEventClick(event)}
        >
          <div className="mb-2 flex items-start justify-between">
            <h4 className="text-sm font-medium">{event.title}</h4>
            <Badge variant="outline" className="text-xs">
              {event.budgetType?.replace('_', ' ')}
            </Badge>
          </div>

          {currentView.displayOptions.showBudgetAmount && event.budgetAmount && (
            <div className="mb-2 text-sm">
              <span className="font-medium">Budget: </span>
              {formatCurrency(event.budgetAmount, event.currency)}
            </div>
          )}

          {currentView.displayOptions.showUtilization &&
            event.utilizationPercentage !== undefined && (
              <div className="mb-2">
                <div className="mb-1 flex justify-between text-xs">
                  <span>Utilization</span>
                  <span>{event.utilizationPercentage.toFixed(1)}%</span>
                </div>
                <Progress value={event.utilizationPercentage} className="h-2" />
              </div>
            )}

          {currentView.displayOptions.showInstructor && event.instructor && (
            <div className="text-xs text-gray-600">Instructor: {event.instructor}</div>
          )}

          {currentView.displayOptions.showAttendees && event.attendeesCount !== undefined && (
            <div className="text-xs text-gray-600">
              Attendees: {event.attendeesCount}
              {event.maxParticipants && ` / ${event.maxParticipants}`}
            </div>
          )}

          {event.location && <div className="mt-1 text-xs text-gray-600">📍 {event.location}</div>}
        </div>
      )
    },
    [currentView.displayOptions, getEventColor, handleEventClick, formatCurrency]
  )

  // Render stats cards
  const renderStatsCards = useCallback(() => {
    if (!stats) return null

    return (
      <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <DollarSign className="h-5 w-5 text-green-600" />
              <div>
                <p className="text-sm text-gray-600">Total Budget</p>
                <p className="text-xl font-bold">{formatCurrency(stats.totalBudgetAmount)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Total Spent</p>
                <p className="text-xl font-bold">{formatCurrency(stats.totalSpent)}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Target className="h-5 w-5 text-purple-600" />
              <div>
                <p className="text-sm text-gray-600">Avg Utilization</p>
                <p className="text-xl font-bold">{stats.averageUtilization.toFixed(1)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm text-gray-600">Overdue</p>
                <p className="text-xl font-bold">{stats.overdueBudgets.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }, [stats, formatCurrency])

  if (loading) {
    return (
      <div className={cn('budget-calendar', className)}>
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center justify-center">
              <div className="border-primary mx-auto mb-4 h-8 w-8 animate-spin rounded-full border-b-2"></div>
              <p className="text-muted-foreground">Loading budget calendar...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error) {
    return (
      <div className={cn('budget-calendar', className)}>
        <Card>
          <CardContent className="p-8">
            <div className="flex items-center justify-center text-center">
              <AlertTriangle className="text-destructive mx-auto mb-4 h-8 w-8" />
              <p className="text-destructive mb-2 font-medium">Error loading budget calendar</p>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button onClick={loadData}>Try Again</Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={cn('budget-calendar space-y-6', className)}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Budget Calendar</h2>
          <p className="text-muted-foreground">
            Manage training budgets and schedules in calendar view
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Select
            value={currentView.id}
            onValueChange={(value) => {
              const view = defaultViews.find((v) => v.id === value)
              if (view) handleViewChange(view)
            }}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select view" />
            </SelectTrigger>
            <SelectContent>
              {defaultViews.map((view) => (
                <SelectItem key={view.id} value={view.id}>
                  {view.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Stats Cards */}
      {renderStatsCards()}

      {/* Main Content */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>{currentView.name}</CardTitle>

            {/* Display Options */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <label className="text-sm">Show Amount</label>
                <input
                  type="checkbox"
                  checked={currentView.displayOptions.showBudgetAmount}
                  onChange={(e) => {
                    setCurrentView((prev) => ({
                      ...prev,
                      displayOptions: {
                        ...prev.displayOptions,
                        showBudgetAmount: e.target.checked,
                      },
                    }))
                  }}
                  className="h-4 w-4"
                />
              </div>

              <div className="flex items-center space-x-2">
                <label className="text-sm">Show Utilization</label>
                <input
                  type="checkbox"
                  checked={currentView.displayOptions.showUtilization}
                  onChange={(e) => {
                    setCurrentView((prev) => ({
                      ...prev,
                      displayOptions: { ...prev.displayOptions, showUtilization: e.target.checked },
                    }))
                  }}
                  className="h-4 w-4"
                />
              </div>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <Tabs
            value={selectedView}
            onValueChange={(value: string) => setSelectedView(value as CalendarViewType)}
          >
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="dayGridMonth">Month</TabsTrigger>
              <TabsTrigger value="timeGridWeek">Week</TabsTrigger>
              <TabsTrigger value="timeGridDay">Day</TabsTrigger>
              <TabsTrigger value="listWeek">List</TabsTrigger>
              <TabsTrigger value="multiMonthYear">Year</TabsTrigger>
            </TabsList>

            <TabsContent value={selectedView} className="mt-4">
              <div className="grid gap-4">
                {currentView.displayOptions.groupByBudget ? (
                  // Group by budget
                  Object.entries(
                    filteredEvents.reduce(
                      (groups, event) => {
                        const key = event.budgetId || 'uncategorized'
                        if (!groups[key]) groups[key] = []
                        groups[key].push(event)
                        return groups
                      },
                      {} as Record<string, BudgetCalendarEvent[]>
                    )
                  ).map(([budgetId, budgetEvents]) => (
                    <div key={budgetId}>
                      <h3 className="mb-2 flex items-center space-x-2 font-medium">
                        <DollarSign className="h-4 w-4" />
                        <span>Budget {budgetId}</span>
                        <Badge variant="outline">{budgetEvents.length} events</Badge>
                      </h3>
                      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                        {budgetEvents.map(renderEventCard)}
                      </div>
                    </div>
                  ))
                ) : (
                  // Regular grid
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                    {filteredEvents.map(renderEventCard)}
                  </div>
                )}

                {filteredEvents.length === 0 && (
                  <div className="py-8 text-center">
                    <CalendarDays className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                    <p className="text-muted-foreground">No budget events found</p>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  )
}

export default BudgetCalendar
