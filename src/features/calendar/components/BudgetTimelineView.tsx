import React, { useState, useEffect, useMemo } from 'react'
import { format, parseISO, isAfter, isBefore, addDays, startOfMonth, endOfMonth } from 'date-fns'
import { Card, CardContent, CardHeader, CardTitle } from '../../../shared/components/ui/card'
import { Badge } from '../../../shared/components/ui/badge'
import { Button } from '../../../shared/components/ui/button'
import { Progress } from '../../../shared/components/ui/progress'
import { ScrollArea } from '../../../shared/components/ui/scroll-area'
import { Separator } from '../../../shared/components/ui/separator'
import {
  Calendar,
  DollarSign,
  Clock,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  XCircle,
  PlayCircle,
} from 'lucide-react'
import type { BudgetCalendarEvent } from '../types/budgetCalendar'

interface BudgetTimelineViewProps {
  events: BudgetCalendarEvent[]
  onEventClick?: (event: BudgetCalendarEvent) => void
  onEventUpdate?: (event: BudgetCalendarEvent) => void
  className?: string
}

/**
 * BudgetTimelineView - Displays budget events in a timeline format
 * Shows budget lifecycle from creation to expiry with spending milestones
 */
export const BudgetTimelineView: React.FC<BudgetTimelineViewProps> = ({
  events,
  onEventClick,
  onEventUpdate,
  className = '',
}) => {
  const [selectedBudget, setSelectedBudget] = useState<string | null>(null)
  const [timelineView, setTimelineView] = useState<'all' | 'active' | 'upcoming' | 'completed'>(
    'all'
  )

  // Group events by budget for timeline display
  const budgetGroups = useMemo(() => {
    const groups = events.reduce(
      (acc, event) => {
        const budgetId = event.budgetId || 'unknown'
        if (!acc[budgetId]) {
          acc[budgetId] = {
            budgetId,
            title: event.title,
            budgetAmount: event.budgetAmount || 0,
            currency: event.currency || 'USD',
            status: event.budgetStatus || 'unknown',
            events: [],
          }
        }
        acc[budgetId].events.push(event)
        return acc
      },
      {} as Record<string, any>
    )

    // Sort events within each budget by date
    Object.values(groups).forEach((group: any) => {
      group.events.sort(
        (a: BudgetCalendarEvent, b: BudgetCalendarEvent) =>
          new Date(a.start as string).getTime() - new Date(b.start as string).getTime()
      )
    })

    return groups
  }, [events])

  // Filter budget groups based on timeline view
  const filteredBudgetGroups = useMemo(() => {
    const filtered = Object.entries(budgetGroups).filter(([_, group]: [string, any]) => {
      const now = new Date()
      const hasActiveEvents = group.events.some((event: BudgetCalendarEvent) => {
        const start = new Date(event.start as string)
        const end = event.end ? new Date(event.end as string) : addDays(start, 1)
        return start <= now && end >= now
      })

      const hasUpcomingEvents = group.events.some(
        (event: BudgetCalendarEvent) => new Date(event.start as string) > now
      )

      const hasCompletedEvents = group.status === 'completed'

      switch (timelineView) {
        case 'active':
          return hasActiveEvents
        case 'upcoming':
          return hasUpcomingEvents && !hasActiveEvents
        case 'completed':
          return hasCompletedEvents
        default:
          return true
      }
    })

    return filtered.sort(([_, a]: [string, any], [__, b]: [string, any]) => {
      const aFirstEvent = a.events[0]
      const bFirstEvent = b.events[0]
      return (
        new Date(bFirstEvent.start as string).getTime() -
        new Date(aFirstEvent.start as string).getTime()
      )
    })
  }, [budgetGroups, timelineView])

  // Calculate budget statistics
  const getBudgetStats = (group: any) => {
    const totalSpent = group.events.reduce(
      (sum: number, event: BudgetCalendarEvent) => sum + (event.spentAmount || 0),
      0
    )
    const utilization = group.budgetAmount > 0 ? (totalSpent / group.budgetAmount) * 100 : 0

    return {
      totalSpent,
      utilization: Math.round(utilization),
      remainingBudget: group.budgetAmount - totalSpent,
    }
  }

  // Get status icon and color
  const getStatusInfo = (status: string) => {
    switch (status) {
      case 'approved':
        return { icon: CheckCircle, color: 'text-green-600', bgColor: 'bg-green-100' }
      case 'in_progress':
        return { icon: PlayCircle, color: 'text-blue-600', bgColor: 'bg-blue-100' }
      case 'completed':
        return { icon: CheckCircle, color: 'text-emerald-600', bgColor: 'bg-emerald-100' }
      case 'cancelled':
        return { icon: XCircle, color: 'text-red-600', bgColor: 'bg-red-100' }
      default:
        return { icon: Clock, color: 'text-gray-600', bgColor: 'bg-gray-100' }
    }
  }

  // Get event type icon
  const getEventTypeIcon = (budgetType: string) => {
    switch (budgetType) {
      case 'budget_creation':
        return DollarSign
      case 'training_session':
        return Calendar
      case 'budget_expiry':
        return AlertTriangle
      case 'milestone':
        return TrendingUp
      default:
        return Clock
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with view filters */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Budget Timeline</h2>
          <p className="mt-1 text-gray-600">Track budget lifecycle and spending milestones</p>
        </div>

        <div className="flex gap-2">
          {(['all', 'active', 'upcoming', 'completed'] as const).map((view) => (
            <Button
              key={view}
              variant={timelineView === view ? 'default' : 'outline'}
              size="sm"
              onClick={() => setTimelineView(view)}
              className="capitalize"
            >
              {view}
            </Button>
          ))}
        </div>
      </div>

      {/* Timeline display */}
      <ScrollArea className="h-[600px]">
        <div className="space-y-6">
          {filteredBudgetGroups.map(([budgetId, group]: [string, any]) => {
            const stats = getBudgetStats(group)
            const statusInfo = getStatusInfo(group.status)
            const StatusIcon = statusInfo.icon

            return (
              <Card
                key={budgetId}
                className={`cursor-pointer transition-all hover:shadow-md ${
                  selectedBudget === budgetId ? 'ring-2 ring-blue-500' : ''
                }`}
                onClick={() => setSelectedBudget(selectedBudget === budgetId ? null : budgetId)}
              >
                <CardHeader className="pb-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="mb-2 flex items-center gap-3">
                        <StatusIcon className={`h-5 w-5 ${statusInfo.color}`} />
                        <CardTitle className="text-lg">{group.title}</CardTitle>
                        <Badge className={`${statusInfo.bgColor} ${statusInfo.color}`}>
                          {group.status}
                        </Badge>
                      </div>

                      <div className="flex items-center gap-6 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <DollarSign className="h-4 w-4" />
                          <span>
                            {group.currency} {group.budgetAmount.toLocaleString()}
                          </span>
                        </div>
                        <div className="flex items-center gap-1">
                          <TrendingUp className="h-4 w-4" />
                          <span>{stats.utilization}% utilized</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <span
                            className={
                              stats.remainingBudget >= 0 ? 'text-green-600' : 'text-red-600'
                            }
                          >
                            {group.currency} {Math.abs(stats.remainingBudget).toLocaleString()}
                            {stats.remainingBudget >= 0 ? ' remaining' : ' over budget'}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Progress bar */}
                  <div className="mt-3">
                    <Progress value={Math.min(stats.utilization, 100)} className="h-2" />
                  </div>
                </CardHeader>

                {selectedBudget === budgetId && (
                  <CardContent className="pt-4">
                    <Separator className="mb-4" />

                    <div className="space-y-4">
                      <h4 className="font-medium text-gray-900">Budget Timeline Events</h4>

                      <div className="relative">
                        {/* Timeline line */}
                        <div className="absolute top-0 bottom-0 left-4 w-0.5 bg-gray-200" />

                        {/* Timeline events */}
                        <div className="space-y-4">
                          {group.events.map((event: BudgetCalendarEvent, index: number) => {
                            const EventIcon = getEventTypeIcon(event.budgetType!)
                            const isPast = new Date(event.start as string) < new Date()
                            const isFuture = event.end
                              ? new Date(event.end as string) > new Date()
                              : false

                            return (
                              <div key={event.id} className="relative flex items-start gap-4">
                                {/* Timeline dot */}
                                <div
                                  className={`relative z-10 flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                                    isPast
                                      ? 'border-green-500 bg-white'
                                      : isFuture
                                        ? 'border-blue-500 bg-white'
                                        : 'border-blue-500 bg-blue-500'
                                  }`}
                                >
                                  <EventIcon
                                    className={`h-4 w-4 ${
                                      isPast
                                        ? 'text-green-600'
                                        : isFuture
                                          ? 'text-blue-600'
                                          : 'text-white'
                                    }`}
                                  />
                                </div>

                                {/* Event content */}
                                <div className="min-w-0 flex-1">
                                  <div className="mb-1 flex items-center justify-between">
                                    <h5 className="truncate font-medium text-gray-900">
                                      {event.title}
                                    </h5>
                                    <div className="flex items-center gap-2 text-sm text-gray-500">
                                      <Calendar className="h-4 w-4" />
                                      {format(parseISO(event.start as string), 'MMM dd, yyyy')}
                                      {event.end &&
                                        ` - ${format(parseISO(event.end as string), 'MMM dd, yyyy')}`}
                                    </div>
                                  </div>

                                  {event.description && (
                                    <p className="mb-2 text-sm text-gray-600">
                                      {event.description}
                                    </p>
                                  )}

                                  <div className="flex items-center gap-4 text-sm">
                                    {event.budgetAmount && (
                                      <div className="flex items-center gap-1">
                                        <DollarSign className="h-3 w-3" />
                                        <span>
                                          {event.currency} {event.budgetAmount.toLocaleString()}
                                        </span>
                                      </div>
                                    )}

                                    {event.utilizationPercentage !== undefined && (
                                      <div className="flex items-center gap-1">
                                        <TrendingUp className="h-3 w-3" />
                                        <span>{event.utilizationPercentage}% utilized</span>
                                      </div>
                                    )}

                                    {event.location && (
                                      <div className="flex items-center gap-1 text-gray-500">
                                        <span>📍 {event.location}</span>
                                      </div>
                                    )}
                                  </div>

                                  {/* Action buttons */}
                                  <div className="mt-3 flex gap-2">
                                    <Button
                                      size="sm"
                                      variant="outline"
                                      onClick={(e: React.MouseEvent) => {
                                        e.stopPropagation()
                                        onEventClick?.(event)
                                      }}
                                    >
                                      View Details
                                    </Button>

                                    {onEventUpdate && (
                                      <Button
                                        size="sm"
                                        variant="outline"
                                        onClick={(e: React.MouseEvent) => {
                                          e.stopPropagation()
                                          onEventUpdate(event)
                                        }}
                                      >
                                        Edit
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                )}
              </Card>
            )
          })}

          {filteredBudgetGroups.length === 0 && (
            <Card>
              <CardContent className="py-12 text-center">
                <Calendar className="mx-auto mb-4 h-12 w-12 text-gray-400" />
                <h3 className="mb-2 text-lg font-medium text-gray-900">No budgets found</h3>
                <p className="text-gray-600">
                  {timelineView === 'all'
                    ? 'No budget events available at this time.'
                    : `No ${timelineView} budgets found.`}
                </p>
              </CardContent>
            </Card>
          )}
        </div>
      </ScrollArea>
    </div>
  )
}

export default BudgetTimelineView
