import React, { useState, useMemo } from 'react'
import { Card } from '../../../shared/components/ui/card'
import { Badge } from '../../../shared/components/ui/badge'
import { Button } from '../../../shared/components/ui/button'
import { Progress } from '../../../shared/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../shared/components/ui/tabs'
import {
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  XCircle,
  TrendingUp,
  TrendingDown,
  Users,
  Filter,
  RefreshCw,
  Download,
  Eye,
} from 'lucide-react'
import type { CalendarEvent } from '../types'
import type { AssessmentEventMetadata } from '../services/assessmentIntegrationService'

/**
 * Helper function to safely convert DateInput to Date
 */
const safeToDate = (dateInput: any): Date => {
  if (dateInput instanceof Date) {
    return dateInput
  }
  if (typeof dateInput === 'string' || typeof dateInput === 'number') {
    return new Date(dateInput)
  }
  // Handle other cases (like arrays from FullCalendar)
  if (Array.isArray(dateInput)) {
    return new Date(
      dateInput[0],
      dateInput[1],
      dateInput[2],
      dateInput[3] || 0,
      dateInput[4] || 0,
      dateInput[5] || 0
    )
  }
  // Fallback
  return new Date()
}

/**
 * Props for AssessmentDeadlineTracker component
 */
interface AssessmentDeadlineTrackerProps {
  events: CalendarEvent[]
  onEventClick?: (event: CalendarEvent) => void
  onEventEdit?: (event: CalendarEvent) => void
  className?: string
}

/**
 * Deadline tracking dashboard for assessments
 */
export const AssessmentDeadlineTracker: React.FC<AssessmentDeadlineTrackerProps> = ({
  events,
  onEventClick,
  onEventEdit,
  className = '',
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter'>('month')
  const [filterStatus, setFilterStatus] = useState<string>('all')

  // Filter deadline events
  const deadlineEvents = useMemo(() => {
    return events.filter((event) => {
      const metadata = event.metadata as AssessmentEventMetadata
      return metadata.type === 'deadline'
    })
  }, [events])

  // Filter events based on selected status
  const filteredEvents = useMemo(() => {
    if (filterStatus === 'all') return deadlineEvents

    return deadlineEvents.filter((event) => {
      const metadata = event.metadata as AssessmentEventMetadata
      return metadata.status === filterStatus
    })
  }, [deadlineEvents, filterStatus])

  // Calculate deadline statistics
  const deadlineStats = useMemo(() => {
    const now = new Date()
    const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
    const monthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
    const quarterFromNow = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000)

    const upcoming = filteredEvents.filter(
      (event) => safeToDate(event.start) > now && safeToDate(event.start) <= weekFromNow
    )

    const thisMonth = filteredEvents.filter(
      (event) => safeToDate(event.start) > now && safeToDate(event.start) <= monthFromNow
    )

    const thisQuarter = filteredEvents.filter(
      (event) => safeToDate(event.start) > now && safeToDate(event.start) <= quarterFromNow
    )

    const overdue = filteredEvents.filter((event) => safeToDate(event.start) < now)

    const completed = filteredEvents.filter((event) => {
      const metadata = event.metadata as AssessmentEventMetadata
      return metadata.status === 'completed'
    })

    const byStatus = filteredEvents.reduce(
      (acc, event) => {
        const metadata = event.metadata as AssessmentEventMetadata
        acc[metadata.status] = (acc[metadata.status] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    const byPriority = filteredEvents.reduce(
      (acc, event) => {
        acc[event.priority || 'medium'] = (acc[event.priority || 'medium'] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    return {
      total: filteredEvents.length,
      upcoming: upcoming.length,
      thisMonth: thisMonth.length,
      thisQuarter: thisQuarter.length,
      overdue: overdue.length,
      completed: completed.length,
      completionRate:
        filteredEvents.length > 0 ? (completed.length / filteredEvents.length) * 100 : 0,
      byStatus,
      byPriority,
      upcomingEvents: upcoming.sort(
        (a, b) => safeToDate(a.start).getTime() - safeToDate(b.start).getTime()
      ),
      overdueEvents: overdue.sort(
        (a, b) => safeToDate(b.start).getTime() - safeToDate(a.start).getTime()
      ),
    }
  }, [filteredEvents])

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-500'
      case 'high':
        return 'bg-orange-500'
      case 'medium':
        return 'bg-yellow-500'
      case 'low':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  const getDaysUntilDeadline = (dateInput: any) => {
    const deadline = safeToDate(dateInput)
    const now = new Date()
    const diffTime = deadline.getTime() - now.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  const formatDeadlineDate = (dateInput: any) => {
    const date = safeToDate(dateInput)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    })
  }

  const getDeadlineUrgency = (days: number) => {
    if (days < 0) return { color: 'text-red-600', label: 'Overdue', icon: XCircle }
    if (days <= 3) return { color: 'text-red-500', label: 'Critical', icon: AlertTriangle }
    if (days <= 7) return { color: 'text-orange-500', label: 'Urgent', icon: AlertTriangle }
    if (days <= 14) return { color: 'text-yellow-500', label: 'Soon', icon: Clock }
    return { color: 'text-green-500', label: 'On Track', icon: CheckCircle }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Assessment Deadline Tracker</h2>
          <p className="text-gray-600">Monitor and manage assessment deadlines</p>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm">
            <RefreshCw className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Deadlines</p>
              <p className="text-2xl font-bold">{deadlineStats.total}</p>
            </div>
            <Calendar className="h-8 w-8 text-blue-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Upcoming (7 days)</p>
              <p className="text-2xl font-bold text-orange-600">{deadlineStats.upcoming}</p>
            </div>
            <Clock className="h-8 w-8 text-orange-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Overdue</p>
              <p className="text-2xl font-bold text-red-600">{deadlineStats.overdue}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-500" />
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completion Rate</p>
              <p className="text-2xl font-bold text-green-600">
                {deadlineStats.completionRate.toFixed(1)}%
              </p>
            </div>
            <TrendingUp className="h-8 w-8 text-green-500" />
          </div>
        </Card>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card className="p-6">
          <h3 className="mb-4 text-lg font-semibold">Status Breakdown</h3>
          <div className="space-y-3">
            {Object.entries(deadlineStats.byStatus).map(([status, count]) => (
              <div key={status} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className={getStatusColor(status)}>
                    {status.replace('_', ' ')}
                  </Badge>
                  <span className="text-sm text-gray-600">{count} deadlines</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Progress value={(count / deadlineStats.total) * 100} className="w-20" />
                  <span className="text-sm font-medium">
                    {((count / deadlineStats.total) * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="mb-4 text-lg font-semibold">Priority Distribution</h3>
          <div className="space-y-3">
            {Object.entries(deadlineStats.byPriority).map(([priority, count]) => (
              <div key={priority} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div className={`h-3 w-3 rounded-full ${getPriorityColor(priority)}`} />
                  <span className="text-sm font-medium capitalize">{priority}</span>
                  <span className="text-sm text-gray-600">{count} deadlines</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Progress value={(count / deadlineStats.total) * 100} className="w-20" />
                  <span className="text-sm font-medium">
                    {((count / deadlineStats.total) * 100).toFixed(1)}%
                  </span>
                </div>
              </div>
            ))}
          </div>
        </Card>
      </div>

      {/* Deadline Lists */}
      <Tabs defaultValue="upcoming" className="space-y-4">
        <div className="flex items-center justify-between">
          <TabsList>
            <TabsTrigger value="upcoming">Upcoming Deadlines</TabsTrigger>
            <TabsTrigger value="overdue">Overdue</TabsTrigger>
            <TabsTrigger value="all">All Deadlines</TabsTrigger>
          </TabsList>

          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium">Filter:</span>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="rounded-md border px-3 py-1 text-sm"
            >
              <option value="all">All Status</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="completed">Completed</option>
              <option value="overdue">Overdue</option>
            </select>
          </div>
        </div>

        <TabsContent value="upcoming" className="space-y-4">
          <Card className="p-4">
            <h3 className="mb-4 text-lg font-semibold">Upcoming Deadlines</h3>
            {deadlineStats.upcomingEvents.length === 0 ? (
              <div className="py-8 text-center text-gray-500">
                <Clock className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>No upcoming deadlines</p>
              </div>
            ) : (
              <div className="space-y-3">
                {deadlineStats.upcomingEvents.map((event) => {
                  const metadata = event.metadata as AssessmentEventMetadata
                  const daysUntil = getDaysUntilDeadline(event.start)
                  const urgency = getDeadlineUrgency(daysUntil)
                  const UrgencyIcon = urgency.icon

                  return (
                    <div
                      key={event.id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border p-3 hover:bg-gray-50"
                      onClick={() => onEventClick?.(event)}
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className={`h-2 w-2 rounded-full ${getPriorityColor(event.priority || 'medium')}`}
                        />
                        <div>
                          <h4 className="font-medium">{event.title}</h4>
                          <p className="text-sm text-gray-600">
                            {formatDeadlineDate(event.start)} • {daysUntil} days remaining
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <div className={`flex items-center space-x-1 ${urgency.color}`}>
                          <UrgencyIcon className="h-4 w-4" />
                          <span className="text-sm font-medium">{urgency.label}</span>
                        </div>
                        <Badge variant="outline" className={getStatusColor(metadata.status)}>
                          {metadata.status}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            onEventEdit?.(event)
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="overdue" className="space-y-4">
          <Card className="p-4">
            <h3 className="mb-4 text-lg font-semibold">Overdue Deadlines</h3>
            {deadlineStats.overdueEvents.length === 0 ? (
              <div className="py-8 text-center text-gray-500">
                <CheckCircle className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>No overdue deadlines</p>
              </div>
            ) : (
              <div className="space-y-3">
                {deadlineStats.overdueEvents.map((event) => {
                  const metadata = event.metadata as AssessmentEventMetadata
                  const daysOverdue = Math.abs(getDaysUntilDeadline(event.start))

                  return (
                    <div
                      key={event.id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border border-red-200 p-3 hover:bg-red-50"
                      onClick={() => onEventClick?.(event)}
                    >
                      <div className="flex items-center space-x-3">
                        <div className="h-2 w-2 rounded-full bg-red-500" />
                        <div>
                          <h4 className="font-medium">{event.title}</h4>
                          <p className="text-sm text-red-600">
                            {daysOverdue} days overdue • Due {formatDeadlineDate(event.start)}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <Badge variant="destructive">{daysOverdue} days overdue</Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            onEventEdit?.(event)
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </Card>
        </TabsContent>

        <TabsContent value="all" className="space-y-4">
          <Card className="p-4">
            <h3 className="mb-4 text-lg font-semibold">All Deadlines</h3>
            {filteredEvents.length === 0 ? (
              <div className="py-8 text-center text-gray-500">
                <Calendar className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>No deadlines found</p>
              </div>
            ) : (
              <div className="space-y-3">
                {filteredEvents.map((event) => {
                  const metadata = event.metadata as AssessmentEventMetadata
                  const daysUntil = getDaysUntilDeadline(event.start)
                  const urgency = getDeadlineUrgency(daysUntil)
                  const UrgencyIcon = urgency.icon

                  return (
                    <div
                      key={event.id}
                      className="flex cursor-pointer items-center justify-between rounded-lg border p-3 hover:bg-gray-50"
                      onClick={() => onEventClick?.(event)}
                    >
                      <div className="flex items-center space-x-3">
                        <div
                          className={`h-2 w-2 rounded-full ${getPriorityColor(event.priority || 'medium')}`}
                        />
                        <div>
                          <h4 className="font-medium">{event.title}</h4>
                          <p className="text-sm text-gray-600">
                            {formatDeadlineDate(event.start)} •
                            {daysUntil >= 0
                              ? ` ${daysUntil} days remaining`
                              : ` ${Math.abs(daysUntil)} days overdue`}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center space-x-2">
                        <div className={`flex items-center space-x-1 ${urgency.color}`}>
                          <UrgencyIcon className="h-4 w-4" />
                          <span className="text-sm font-medium">{urgency.label}</span>
                        </div>
                        <Badge variant="outline" className={getStatusColor(metadata.status)}>
                          {metadata.status}
                        </Badge>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            onEventEdit?.(event)
                          }}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )
                })}
              </div>
            )}
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default AssessmentDeadlineTracker
