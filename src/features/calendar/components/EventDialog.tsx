import React, { useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { format } from 'date-fns'

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog'
import { Form, Field } from '@/shared/components/ui/Form'
import { Input } from '@/shared/components/ui/input'
import { Textarea } from '@/shared/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { Label } from '@/shared/components/ui/label'

import type { CalendarEvent, EventType, EventModalMode } from '../types/calendar'

// Form data interface
interface EventFormData {
  title: string
  description?: string
  type: EventType
  location?: string
  instructor?: string
  priority?: 'low' | 'medium' | 'high' | 'urgent'
}

interface EventDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  mode: EventModalMode
  event?: CalendarEvent | null
  selectedDates?: {
    start: Date
    end: Date
    allDay: boolean
  }
  onSubmit: (data: EventFormData) => void
  isLoading?: boolean
}

// Event type configuration with colors and labels
const eventTypeConfig: Record<EventType, { label: string; color: string }> = {
  training_session: { label: 'Training Session', color: 'bg-blue-500' },
  assessment: { label: 'Assessment', color: 'bg-red-500' },
  meeting: { label: 'Meeting', color: 'bg-green-500' },
  maintenance: { label: 'Maintenance', color: 'bg-yellow-500' },
  holiday: { label: 'Holiday', color: 'bg-purple-500' },
  deadline: { label: 'Deadline', color: 'bg-orange-500' },
  review: { label: 'Review', color: 'bg-pink-500' },
  other: { label: 'Other', color: 'bg-gray-500' },
}

const priorityConfig = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800' },
}

export function EventDialog({
  open,
  onOpenChange,
  mode,
  event,
  selectedDates,
  onSubmit,
  isLoading = false,
}: EventDialogProps) {
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
    setValue,
    watch,
  } = useForm<EventFormData>({
    defaultValues: {
      title: '',
      description: '',
      type: 'other',
      location: '',
      instructor: '',
      priority: 'medium',
    },
  })

  // Watch values for controlled components
  const watchedType = watch('type')
  const watchedPriority = watch('priority')

  // Populate form when editing an event
  useEffect(() => {
    if (event && mode === 'edit') {
      reset({
        title: event.title || '',
        description: event.description || '',
        type: event.type || 'other',
        location: event.location || '',
        instructor: event.instructor || '',
        priority: event.priority || 'medium',
      })
    } else if (mode === 'create') {
      // Set default type based on selected dates if available
      const defaultType = selectedDates?.allDay ? 'other' : 'meeting'
      reset({
        title: '',
        description: '',
        type: defaultType,
        location: '',
        instructor: '',
        priority: 'medium',
      })
    }
  }, [event, mode, reset, selectedDates])

  const handleFormSubmit = (data: EventFormData) => {
    onSubmit(data)
    if (!isLoading) {
      reset()
    }
  }

  const handleCancel = () => {
    reset()
    onOpenChange(false)
  }

  const getDialogTitle = () => {
    switch (mode) {
      case 'create':
        return 'Create New Event'
      case 'edit':
        return 'Edit Event'
      case 'view':
        return 'Event Details'
      default:
        return 'Event'
    }
  }

  const formatDateRange = () => {
    if (!selectedDates) return ''

    const { start, end, allDay } = selectedDates
    const startDate = format(start, allDay ? 'MMM d, yyyy' : 'MMM d, yyyy h:mm a')
    const endDate = format(end, allDay ? 'MMM d, yyyy' : 'MMM d, yyyy h:mm a')

    if (allDay) {
      return startDate === endDate ? startDate : `${startDate} - ${endDate}`
    } else {
      return `${startDate} - ${endDate}`
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] max-w-2xl overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{getDialogTitle()}</DialogTitle>
          {selectedDates && (
            <DialogDescription>Scheduled for: {formatDateRange()}</DialogDescription>
          )}
        </DialogHeader>

        <Form onSubmit={handleSubmit(handleFormSubmit)}>
          {/* Event Title */}
          <Field label="Event Title *" error={errors.title?.message}>
            <Input
              placeholder="Enter event title"
              {...register('title', {
                required: 'Event title is required',
                maxLength: { value: 100, message: 'Title must be less than 100 characters' },
              })}
              disabled={mode === 'view' || isLoading}
            />
          </Field>

          {/* Event Type */}
          <Field label="Event Type" error={errors.type?.message}>
            <Select
              value={watchedType}
              onValueChange={(value: EventType) => setValue('type', value)}
              disabled={mode === 'view' || isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select event type" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(eventTypeConfig).map(([value, config]) => (
                  <SelectItem key={value} value={value}>
                    <div className="flex items-center gap-2">
                      <div className={`h-3 w-3 rounded-full ${config.color}`} />
                      {config.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <input type="hidden" {...register('type', { required: 'Event type is required' })} />
          </Field>

          {/* Priority */}
          <Field label="Priority" error={errors.priority?.message}>
            <Select
              value={watchedPriority}
              onValueChange={(value: 'low' | 'medium' | 'high' | 'urgent') =>
                setValue('priority', value)
              }
              disabled={mode === 'view' || isLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select priority" />
              </SelectTrigger>
              <SelectContent>
                {Object.entries(priorityConfig).map(([value, config]) => (
                  <SelectItem key={value} value={value}>
                    <Badge variant="secondary" className={config.color}>
                      {config.label}
                    </Badge>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <input type="hidden" {...register('priority')} />
          </Field>

          {/* Description */}
          <Field label="Description" error={errors.description?.message}>
            <Textarea
              placeholder="Add event details (optional)"
              className="min-h-[100px]"
              {...register('description', {
                maxLength: { value: 500, message: 'Description must be less than 500 characters' },
              })}
              disabled={mode === 'view' || isLoading}
            />
          </Field>

          {/* Location */}
          <Field label="Location" error={errors.location?.message}>
            <Input
              placeholder="Enter location (optional)"
              {...register('location', {
                maxLength: { value: 200, message: 'Location must be less than 200 characters' },
              })}
              disabled={mode === 'view' || isLoading}
            />
          </Field>

          {/* Instructor */}
          <Field label="Instructor" error={errors.instructor?.message}>
            <Input
              placeholder="Enter instructor name (optional)"
              {...register('instructor', {
                maxLength: {
                  value: 100,
                  message: 'Instructor name must be less than 100 characters',
                },
              })}
              disabled={mode === 'view' || isLoading}
            />
          </Field>

          <DialogFooter className="flex justify-between">
            <div>
              {mode === 'edit' && (
                <Button
                  type="button"
                  variant="destructive"
                  onClick={() => {
                    // This would typically open a confirmation dialog
                    // For now, we'll just emit a delete event
                    if (window.confirm('Are you sure you want to delete this event?')) {
                      // Handle delete - this would need to be passed as a prop
                      console.log('Delete event:', event?.id)
                    }
                  }}
                  disabled={isLoading}
                >
                  Delete Event
                </Button>
              )}
            </div>
            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={handleCancel} disabled={isLoading}>
                {mode === 'view' ? 'Close' : 'Cancel'}
              </Button>
              {mode !== 'view' && (
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? 'Saving...' : mode === 'create' ? 'Create Event' : 'Update Event'}
                </Button>
              )}
            </div>
          </DialogFooter>
        </Form>
      </DialogContent>
    </Dialog>
  )
}

export default EventDialog
