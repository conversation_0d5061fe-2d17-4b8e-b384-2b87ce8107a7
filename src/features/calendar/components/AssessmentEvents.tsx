import React from 'react'
import { Badge } from '../../../shared/components/ui/badge'
import { Card } from '../../../shared/components/ui/card'
import { Button } from '../../../shared/components/ui/button'
import { Calendar, Clock, User, AlertTriangle, CheckCircle, XCircle } from 'lucide-react'
import type { CalendarEvent } from '../types'
import type { AssessmentEventMetadata } from '../services/assessmentIntegrationService'

/**
 * Helper function to safely convert DateInput to Date
 */
const safeToDate = (dateInput: any): Date => {
  if (dateInput instanceof Date) {
    return dateInput
  }
  if (typeof dateInput === 'string' || typeof dateInput === 'number') {
    return new Date(dateInput)
  }
  // Handle other cases (like arrays from FullCalendar)
  if (Array.isArray(dateInput)) {
    return new Date(
      dateInput[0],
      dateInput[1],
      dateInput[2],
      dateInput[3] || 0,
      dateInput[4] || 0,
      dateInput[5] || 0
    )
  }
  // Fallback
  return new Date()
}

/**
 * Props for AssessmentEvent component
 */
interface AssessmentEventProps {
  event: CalendarEvent
  onClick?: (event: CalendarEvent) => void
  onEdit?: (event: CalendarEvent) => void
  onDelete?: (eventId: string) => void
  compact?: boolean
}

/**
 * Assessment event component for displaying assessment-related calendar events
 */
export const AssessmentEvent: React.FC<AssessmentEventProps> = ({
  event,
  onClick,
  onEdit,
  onDelete,
  compact = false,
}) => {
  const metadata = event.metadata as AssessmentEventMetadata

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'deadline':
        return <AlertTriangle className="h-4 w-4" />
      case 'session':
        return <Calendar className="h-4 w-4" />
      case 'review':
        return <CheckCircle className="h-4 w-4" />
      case 'reminder':
        return <Clock className="h-4 w-4" />
      default:
        return <Calendar className="h-4 w-4" />
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-500'
      case 'high':
        return 'bg-orange-500'
      case 'medium':
        return 'bg-yellow-500'
      case 'low':
        return 'bg-green-500'
      default:
        return 'bg-gray-500'
    }
  }

  const formatDate = (dateInput: any) => {
    const d = safeToDate(dateInput)
    return d.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    })
  }

  const formatTime = (dateInput: any) => {
    const d = safeToDate(dateInput)
    return d.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  if (compact) {
    return (
      <div
        className={`cursor-pointer rounded-lg border p-2 transition-shadow hover:shadow-md ${getStatusColor(metadata.status)}`}
        onClick={() => onClick?.(event)}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {getTypeIcon(metadata.type)}
            <span className="truncate text-sm font-medium">{event.title}</span>
          </div>
          <div className="flex items-center space-x-1">
            <div
              className={`h-2 w-2 rounded-full ${getPriorityColor(event.priority || 'medium')}`}
            />
            <Badge variant="outline" className="text-xs">
              {metadata.type}
            </Badge>
          </div>
        </div>
        <div className="mt-1 text-xs text-gray-600">
          {formatDate(event.start)} {event.end && `- ${formatTime(event.end)}`}
        </div>
      </div>
    )
  }

  return (
    <Card
      className="cursor-pointer p-4 transition-shadow hover:shadow-lg"
      onClick={() => onClick?.(event)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <div className="mb-2 flex items-center space-x-2">
            {getTypeIcon(metadata.type)}
            <h3 className="text-lg font-semibold">{event.title}</h3>
            <Badge variant="outline" className={getStatusColor(metadata.status)}>
              {metadata.status}
            </Badge>
            <Badge variant="secondary">{metadata.type}</Badge>
          </div>

          <div className="space-y-2 text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <Calendar className="h-4 w-4" />
              <span>{formatDate(event.start)}</span>
              {event.end && (
                <>
                  <span>-</span>
                  <span>{formatTime(event.end)}</span>
                </>
              )}
            </div>

            {event.location && (
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>{event.location}</span>
              </div>
            )}

            {metadata.instructor && (
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4" />
                <span>{metadata.instructor}</span>
              </div>
            )}

            {event.description && <p className="text-gray-700">{event.description}</p>}
          </div>

          {/* Assessment-specific details */}
          <div className="mt-3 border-t pt-3">
            <div className="grid grid-cols-2 gap-4 text-sm">
              {metadata.quarter && metadata.year && (
                <div>
                  <span className="font-medium">Quarter:</span> Q{metadata.quarter} {metadata.year}
                </div>
              )}

              {metadata.overallScore !== undefined && (
                <div>
                  <span className="font-medium">Score:</span> {metadata.overallScore}%
                </div>
              )}

              {metadata.technicalScore !== undefined && (
                <div>
                  <span className="font-medium">Technical:</span> {metadata.technicalScore}%
                </div>
              )}

              {metadata.softSkillsScore !== undefined && (
                <div>
                  <span className="font-medium">Soft Skills:</span> {metadata.softSkillsScore}%
                </div>
              )}

              {metadata.onJobScore !== undefined && (
                <div>
                  <span className="font-medium">On Job:</span> {metadata.onJobScore}%
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="ml-4 flex flex-col items-end space-y-2">
          <div className={`h-3 w-3 rounded-full ${getPriorityColor(event.priority || 'medium')}`} />

          <div className="flex space-x-1">
            {onEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onEdit(event)
                }}
              >
                Edit
              </Button>
            )}

            {onDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation()
                  onDelete(event.id)
                }}
              >
                <XCircle className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </div>
    </Card>
  )
}

/**
 * Props for AssessmentEventList component
 */
interface AssessmentEventListProps {
  events: CalendarEvent[]
  onEventClick?: (event: CalendarEvent) => void
  onEventEdit?: (event: CalendarEvent) => void
  onEventDelete?: (eventId: string) => void
  compact?: boolean
  groupBy?: 'date' | 'type' | 'status' | 'priority'
  filterBy?: {
    type?: string[]
    status?: string[]
    priority?: string[]
  }
}

/**
 * List component for displaying multiple assessment events
 */
export const AssessmentEventList: React.FC<AssessmentEventListProps> = ({
  events,
  onEventClick,
  onEventEdit,
  onEventDelete,
  compact = false,
  groupBy = 'date',
  filterBy,
}) => {
  // Filter events based on filter criteria
  const filteredEvents = React.useMemo(() => {
    return events.filter((event) => {
      const metadata = event.metadata as AssessmentEventMetadata

      if (filterBy?.type && metadata.type && !filterBy.type.includes(metadata.type)) {
        return false
      }

      if (filterBy?.status && metadata.status && !filterBy.status.includes(metadata.status)) {
        return false
      }

      if (filterBy?.priority && event.priority && !filterBy.priority.includes(event.priority)) {
        return false
      }

      return true
    })
  }, [events, filterBy])

  // Group events based on groupBy criteria
  const groupedEvents = React.useMemo(() => {
    const groups: Record<string, CalendarEvent[]> = {}

    filteredEvents.forEach((event) => {
      const metadata = event.metadata as AssessmentEventMetadata
      let key = ''

      switch (groupBy) {
        case 'date':
          key = safeToDate(event.start).toDateString()
          break
        case 'type':
          key = metadata.type || 'unknown'
          break
        case 'status':
          key = metadata.status || 'unknown'
          break
        case 'priority':
          key = event.priority || 'medium'
          break
        default:
          key = safeToDate(event.start).toDateString()
      }

      if (!groups[key]) {
        groups[key] = []
      }
      groups[key].push(event)
    })

    return groups
  }, [filteredEvents, groupBy])

  const getGroupLabel = (key: string) => {
    switch (groupBy) {
      case 'date':
        return safeToDate(key).toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        })
      case 'type':
        return key.charAt(0).toUpperCase() + key.slice(1)
      case 'status':
        return key.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase())
      case 'priority':
        return key.charAt(0).toUpperCase() + key.slice(1)
      default:
        return key
    }
  }

  if (filteredEvents.length === 0) {
    return (
      <div className="py-8 text-center text-gray-500">
        <Calendar className="mx-auto mb-4 h-12 w-12 opacity-50" />
        <p>No assessment events found</p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {Object.entries(groupedEvents).map(([key, groupEvents]) => (
        <div key={key}>
          <h3 className="mb-3 text-lg font-semibold text-gray-800">
            {getGroupLabel(key)} ({groupEvents.length})
          </h3>
          <div className={compact ? 'space-y-2' : 'space-y-4'}>
            {groupEvents.map((event) => (
              <AssessmentEvent
                key={event.id}
                event={event}
                onClick={onEventClick}
                onEdit={onEventEdit}
                onDelete={onEventDelete}
                compact={compact}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}

/**
 * Props for AssessmentEventCard component
 */
interface AssessmentEventCardProps {
  event: CalendarEvent
  className?: string
}

/**
 * Card component for displaying a single assessment event in a compact format
 */
export const AssessmentEventCard: React.FC<AssessmentEventCardProps> = ({
  event,
  className = '',
}) => {
  const metadata = event.metadata as AssessmentEventMetadata

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'border-yellow-200 bg-yellow-50'
      case 'in_progress':
        return 'border-blue-200 bg-blue-50'
      case 'completed':
        return 'border-green-200 bg-green-50'
      case 'overdue':
        return 'border-red-200 bg-red-50'
      default:
        return 'border-gray-200 bg-gray-50'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'deadline':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'session':
        return <Calendar className="h-4 w-4 text-blue-500" />
      case 'review':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'reminder':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <Calendar className="h-4 w-4 text-gray-500" />
    }
  }

  return (
    <Card className={`p-3 ${getStatusColor(metadata.status)} ${className}`}>
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          {getTypeIcon(metadata.type)}
          <div>
            <h4 className="text-sm font-medium">{event.title}</h4>
            <p className="text-xs text-gray-600">{safeToDate(event.start).toLocaleDateString()}</p>
          </div>
        </div>
        <Badge variant="outline" className="text-xs">
          {metadata.type}
        </Badge>
      </div>
    </Card>
  )
}

export default AssessmentEvent
