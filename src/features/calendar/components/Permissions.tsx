import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/shared/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
// import { Switch } from '@/shared/components/ui/switch' // Commented out as it may not exist
import { Label } from '@/shared/components/ui/label'
import { Input } from '@/shared/components/ui/input'
import { Textarea } from '@/shared/components/ui/textarea'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/shared/components/ui/dialog'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import {
  Calendar,
  Clock,
  Shield,
  Users,
  Settings,
  AlertTriangle,
  CheckCircle,
  XCircle,
} from 'lucide-react'
import {
  useCalendarPermissions,
  useUserPermissions,
  usePermissionStatistics,
  usePermissionChecks,
} from '../hooks/usePermissions'
import { CalendarPermissionService } from '../services/permissionService'
import type {
  CalendarPermission,
  CalendarPermissionType,
  CalendarUserRole,
  PermissionConditions,
  UserPermissionSummary as UserPermissionSummaryType,
} from '../types/permissions'
import type { EntityId } from '@/shared/types/common'

/**
 * Permission-based wrapper component
 */
interface PermissionWrapperProps {
  permission: CalendarPermissionType
  eventId?: string
  calendarId?: string
  fallback?: React.ReactNode
  children: React.ReactNode
}

export const PermissionWrapper: React.FC<PermissionWrapperProps> = ({
  permission,
  eventId,
  calendarId,
  fallback = null,
  children,
}) => {
  const { hasPermission, loading } = useUserPermissions()

  if (loading) {
    return <div className="animate-pulse">Checking permissions...</div>
  }

  if (!hasPermission(permission)) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

/**
 * Permission indicator component
 */
interface PermissionIndicatorProps {
  permission: CalendarPermissionType
  granted?: boolean
  reason?: string
  size?: 'sm' | 'md' | 'lg'
}

export const PermissionIndicator: React.FC<PermissionIndicatorProps> = ({
  permission,
  granted,
  reason,
  size = 'md',
}) => {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-5 w-5',
    lg: 'h-6 w-6',
  }

  const Icon = granted ? CheckCircle : XCircle
  const colorClass = granted ? 'text-green-500' : 'text-red-500'

  return (
    <div className="flex items-center gap-2">
      <Icon className={`${sizeClasses[size]} ${colorClass}`} />
      <span className="text-sm">{permission}</span>
      {reason && <span className="text-muted-foreground text-xs">({reason})</span>}
    </div>
  )
}

/**
 * Calendar permission management component
 */
export const CalendarPermissionManager: React.FC = () => {
  const [selectedUser, setSelectedUser] = useState<string>('')
  const [selectedPermission, setSelectedPermission] = useState<CalendarPermissionType | ''>('')
  const [conditions, setConditions] = useState<PermissionConditions>({})
  const [expirationDate, setExpirationDate] = useState<string>('')
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  const { permissions, loading, error, grantPermission, revokePermission, refreshPermissions } =
    useCalendarPermissions()

  const { statistics } = usePermissionStatistics()

  const handleGrantPermission = async () => {
    if (!selectedUser || !selectedPermission) return

    try {
      await grantPermission(selectedUser, selectedPermission, conditions)
      setIsDialogOpen(false)
      setSelectedUser('')
      setSelectedPermission('')
      setConditions({})
      setExpirationDate('')
    } catch (err) {
      console.error('Failed to grant permission:', err)
    }
  }

  const handleRevokePermission = async (permissionId: EntityId) => {
    try {
      await revokePermission(permissionId)
    } catch (err) {
      console.error('Failed to revoke permission:', err)
    }
  }

  const permissionTypes: CalendarPermissionType[] = [
    'view_calendar',
    'create_events',
    'edit_events',
    'delete_events',
    'manage_permissions',
    'view_all_events',
    'manage_resources',
    'admin_calendar',
    'view_own_events',
    'edit_own_events',
    'delete_own_events',
    'view_team_events',
    'manage_team_events',
    'export_calendar',
    'import_calendar',
    'manage_calendar_settings',
  ]

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Calendar Permission Management
          </CardTitle>
          <CardDescription>
            Manage user permissions for calendar access and operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="mb-4 flex items-center justify-between">
            <div className="flex gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold">{statistics?.totalPermissions || 0}</div>
                <div className="text-muted-foreground text-sm">Total Permissions</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {statistics?.activePermissions || 0}
                </div>
                <div className="text-muted-foreground text-sm">Active</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">
                  {statistics?.expiredPermissions || 0}
                </div>
                <div className="text-muted-foreground text-sm">Expired</div>
              </div>
            </div>

            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
              <DialogTrigger asChild>
                <Button>Grant Permission</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Grant Calendar Permission</DialogTitle>
                  <DialogDescription>
                    Grant a new permission to a user with optional conditions
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="user">User ID</Label>
                    <Input
                      id="user"
                      value={selectedUser}
                      onChange={(e) => setSelectedUser(e.target.value)}
                      placeholder="Enter user ID"
                    />
                  </div>

                  <div>
                    <Label htmlFor="permission">Permission</Label>
                    <Select
                      value={selectedPermission}
                      onValueChange={(value) =>
                        setSelectedPermission(value as CalendarPermissionType)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select permission" />
                      </SelectTrigger>
                      <SelectContent>
                        {permissionTypes.map((permission) => (
                          <SelectItem key={permission} value={permission}>
                            {permission.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="expiration">Expiration Date (optional)</Label>
                    <Input
                      id="expiration"
                      type="datetime-local"
                      value={expirationDate}
                      onChange={(e) => setExpirationDate(e.target.value)}
                    />
                  </div>

                  <div className="flex justify-end gap-2">
                    <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
                      Cancel
                    </Button>
                    <Button onClick={handleGrantPermission}>Grant Permission</Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>

          {error && (
            <div className="mb-4 rounded-md border border-red-200 bg-red-50 p-3">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-4 w-4 text-red-500" />
                <span className="text-sm text-red-700">{error}</span>
              </div>
            </div>
          )}

          {loading ? (
            <div className="py-8 text-center">Loading permissions...</div>
          ) : (
            <div className="space-y-2">
              {permissions.map((permission) => (
                <div
                  key={permission.id}
                  className="flex items-center justify-between rounded-lg border p-3"
                >
                  <div className="flex items-center gap-3">
                    <div>
                      <div className="font-medium">
                        {permission.permission
                          .replace(/_/g, ' ')
                          .replace(/\b\w/g, (l) => l.toUpperCase())}
                      </div>
                      <div className="text-muted-foreground text-sm">
                        User: {permission.userId} • Granted:{' '}
                        {new Date(permission.grantedAt).toLocaleDateString()}
                        {permission.expiresAt &&
                          ` • Expires: ${new Date(permission.expiresAt).toLocaleDateString()}`}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={permission.isActive ? 'default' : 'secondary'}>
                      {permission.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRevokePermission(permission.id)}
                    >
                      Revoke
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * User permission summary component
 */
interface UserPermissionSummaryProps {
  userId: EntityId
}

export const UserPermissionSummaryComponent: React.FC<UserPermissionSummaryProps> = ({
  userId,
}) => {
  const { permissionSummary, loading, error, hasPermission, hasAnyPermission, hasAllPermissions } =
    useUserPermissions(userId)

  if (loading) {
    return <div className="animate-pulse">Loading permission summary...</div>
  }

  if (error) {
    return (
      <div className="rounded-md border border-red-200 bg-red-50 p-3">
        <div className="flex items-center gap-2">
          <AlertTriangle className="h-4 w-4 text-red-500" />
          <span className="text-sm text-red-700">{error}</span>
        </div>
      </div>
    )
  }

  if (!permissionSummary) {
    return <div>No permission data available</div>
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Permission Summary
        </CardTitle>
        <CardDescription>
          User: {permissionSummary.userId} • Role: {permissionSummary.userRole}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="effective" className="w-full">
          <TabsList>
            <TabsTrigger value="effective">Effective Permissions</TabsTrigger>
            <TabsTrigger value="explicit">Explicit Permissions</TabsTrigger>
            <TabsTrigger value="expired">Expired Permissions</TabsTrigger>
          </TabsList>

          <TabsContent value="effective" className="space-y-4">
            <div>
              <h4 className="mb-2 font-medium">
                Effective Permissions ({permissionSummary.effectivePermissions.size})
              </h4>
              <div className="flex flex-wrap gap-2">
                {Array.from(permissionSummary.effectivePermissions).map((permission) => (
                  <Badge key={permission} variant="default">
                    {permission.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                  </Badge>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="explicit" className="space-y-4">
            <div>
              <h4 className="mb-2 font-medium">
                Explicit Permissions ({permissionSummary.permissions.length})
              </h4>
              <div className="space-y-2">
                {permissionSummary.permissions.map((permission) => (
                  <div
                    key={permission.id}
                    className="flex items-center justify-between rounded border p-2"
                  >
                    <div>
                      <div className="font-medium">{permission.permission}</div>
                      <div className="text-muted-foreground text-sm">
                        Granted: {new Date(permission.grantedAt).toLocaleDateString()}
                        {permission.expiresAt &&
                          ` • Expires: ${new Date(permission.expiresAt).toLocaleDateString()}`}
                      </div>
                    </div>
                    <Badge variant={permission.isActive ? 'default' : 'secondary'}>
                      {permission.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="expired" className="space-y-4">
            <div>
              <h4 className="mb-2 font-medium">
                Expired Permissions ({permissionSummary.expiredPermissions.length})
              </h4>
              <div className="space-y-2">
                {permissionSummary.expiredPermissions.map((permission) => (
                  <div
                    key={permission.id}
                    className="flex items-center justify-between rounded border p-2"
                  >
                    <div>
                      <div className="font-medium">{permission.permission}</div>
                      <div className="text-muted-foreground text-sm">
                        Expired:{' '}
                        {permission.expiresAt
                          ? new Date(permission.expiresAt).toLocaleDateString()
                          : 'Unknown'}
                      </div>
                    </div>
                    <Badge variant="destructive">Expired</Badge>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}

/**
 * Permission check component for testing
 */
export const PermissionChecker: React.FC = () => {
  const [selectedPermission, setSelectedPermission] =
    useState<CalendarPermissionType>('view_calendar')
  const [eventId, setEventId] = useState<string>('')
  const [checkResult, setCheckResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const { checkPermission } = usePermissionChecks()

  const handleCheckPermission = async () => {
    setLoading(true)
    try {
      const result = await checkPermission(selectedPermission)
      setCheckResult(result)
    } catch (err) {
      console.error('Error checking permission:', err)
      setCheckResult({ granted: false, reason: 'Error checking permission' })
    } finally {
      setLoading(false)
    }
  }

  const permissionTypes: CalendarPermissionType[] = [
    'view_calendar',
    'create_events',
    'edit_events',
    'delete_events',
    'manage_permissions',
    'view_all_events',
    'manage_resources',
    'admin_calendar',
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Permission Checker
        </CardTitle>
        <CardDescription>Test user permissions for calendar operations</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div>
            <Label htmlFor="permission">Permission to Check</Label>
            <Select
              value={selectedPermission}
              onValueChange={(value) => setSelectedPermission(value as CalendarPermissionType)}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {permissionTypes.map((permission) => (
                  <SelectItem key={permission} value={permission}>
                    {permission.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="eventId">Event ID (optional)</Label>
            <Input
              id="eventId"
              value={eventId}
              onChange={(e) => setEventId(e.target.value)}
              placeholder="Enter event ID for event-specific permissions"
            />
          </div>

          <Button onClick={handleCheckPermission} disabled={loading}>
            {loading ? 'Checking...' : 'Check Permission'}
          </Button>

          {checkResult && (
            <div className="mt-4 rounded-lg border p-4">
              <div className="mb-2 flex items-center gap-2">
                {checkResult.granted ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <XCircle className="h-5 w-5 text-red-500" />
                )}
                <span className="font-medium">
                  Permission {checkResult.granted ? 'Granted' : 'Denied'}
                </span>
              </div>
              {checkResult.reason && (
                <div className="text-muted-foreground text-sm">Reason: {checkResult.reason}</div>
              )}
              {checkResult.expiresAt && (
                <div className="text-muted-foreground text-sm">
                  Expires: {new Date(checkResult.expiresAt).toLocaleDateString()}
                </div>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

/**
 * Role-based access control component
 */
interface RoleBasedAccessProps {
  requiredRoles?: CalendarUserRole[]
  requiredPermissions?: CalendarPermissionType[]
  fallback?: React.ReactNode
  children: React.ReactNode
}

export const RoleBasedAccessControl: React.FC<RoleBasedAccessProps> = ({
  requiredRoles = [],
  requiredPermissions = [],
  fallback = null,
  children,
}) => {
  const { hasPermission, hasAllPermissions, hasAnyPermission } = useUserPermissions()

  // Check role-based access
  const hasRoleAccess = requiredRoles.length === 0 // If no roles required, allow access

  // Check permission-based access
  let hasPermissionAccess = true
  if (requiredPermissions.length > 0) {
    hasPermissionAccess = hasAllPermissions(requiredPermissions)
  }

  if (!hasRoleAccess || !hasPermissionAccess) {
    return <>{fallback}</>
  }

  return <>{children}</>
}

export default {
  PermissionWrapper,
  PermissionIndicator,
  CalendarPermissionManager,
  UserPermissionSummary: UserPermissionSummaryComponent,
  PermissionChecker,
  RoleBasedAccessControl,
}
