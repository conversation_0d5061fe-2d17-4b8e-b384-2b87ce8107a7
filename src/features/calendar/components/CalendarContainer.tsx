import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '../../../shared/components/ui/card'
import { Button } from '../../../shared/components/ui/button'
import { Badge } from '../../../shared/components/ui/badge'
import { cn } from '../../../lib/utils'
import type { CalendarEvent, CalendarViewType } from '../types'

interface CalendarContainerProps {
  children: React.ReactNode
  title?: string
  currentEvents: CalendarEvent[]
  currentView?: CalendarViewType
  currentDate?: Date
  onTodayClick?: () => void
  onCreateEventClick?: () => void
  onViewChange?: (view: CalendarViewType) => void
  className?: string
}

const CalendarContainer: React.FC<CalendarContainerProps> = ({
  children,
  title = 'Calendar',
  currentEvents = [],
  currentDate = new Date(),
  onTodayClick,
  onCreateEventClick,
  className,
}) => {
  const [isCreatingEvent, setIsCreatingEvent] = useState(false)

  const handleCreateEvent = () => {
    setIsCreatingEvent(true)
    onCreateEventClick?.()
    // Reset after a short delay to allow for modal/dialog to open
    setTimeout(() => setIsCreatingEvent(false), 100)
  }

  const getEventTypeColor = (
    type?: string
  ): 'default' | 'secondary' | 'destructive' | 'outline' => {
    const typeColors: Record<string, 'default' | 'secondary' | 'destructive' | 'outline'> = {
      training_session: 'default',
      assessment: 'default',
      meeting: 'secondary',
      maintenance: 'outline',
      holiday: 'secondary',
      deadline: 'destructive',
      review: 'default',
      other: 'outline',
    }
    return typeColors[type || 'other'] || 'outline'
  }

  const getEventStats = () => {
    const stats = currentEvents.reduce(
      (acc, event) => {
        const type = event.type || 'other'
        acc[type] = (acc[type] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    return Object.entries(stats).map(([type, count]) => ({
      type,
      count,
      label: type.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase()),
    }))
  }

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric',
    })
  }

  return (
    <Card className={cn('calendar-container w-full', className)}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <div className="flex flex-col space-y-2">
          <CardTitle className="text-2xl font-bold">{title}</CardTitle>
          <p className="text-muted-foreground text-sm">
            {formatDate(currentDate)} • {currentEvents.length} events
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={onTodayClick} className="hidden sm:flex">
            Today
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleCreateEvent}
            disabled={isCreatingEvent}
          >
            {isCreatingEvent ? 'Creating...' : 'Create Event'}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Event Type Summary */}
        {getEventStats().length > 0 && (
          <div className="bg-muted/50 flex flex-wrap gap-2 rounded-lg p-3">
            <span className="text-muted-foreground text-sm font-medium">Event Types:</span>
            {getEventStats().map(({ type, count, label }) => (
              <Badge key={type} variant={getEventTypeColor(type)} className="text-xs">
                {label}: {count}
              </Badge>
            ))}
          </div>
        )}

        {/* Calendar Content */}
        <div className="w-full overflow-hidden">{children}</div>

        {/* Mobile Today Button */}
        <div className="flex justify-center pt-2 sm:hidden">
          <Button variant="outline" size="sm" onClick={onTodayClick} className="w-full">
            Today
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default CalendarContainer
