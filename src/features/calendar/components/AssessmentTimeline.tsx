import React, { useState, useMemo } from 'react'
import { Card } from '../../../shared/components/ui/card'
import { Badge } from '../../../shared/components/ui/badge'
import { Button } from '../../../shared/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '../../../shared/components/ui/tabs'
import {
  Calendar,
  Clock,
  User,
  AlertTriangle,
  CheckCircle,
  XCircle,
  ChevronLeft,
  ChevronRight,
  Filter,
  Download,
} from 'lucide-react'
import type { CalendarEvent } from '../types'
import type { AssessmentEventMetadata } from '../services/assessmentIntegrationService'

/**
 * Helper function to safely convert DateInput to Date
 */
const safeToDate = (dateInput: any): Date => {
  if (dateInput instanceof Date) {
    return dateInput
  }
  if (typeof dateInput === 'string' || typeof dateInput === 'number') {
    return new Date(dateInput)
  }
  // Handle other cases (like arrays from FullCalendar)
  if (Array.isArray(dateInput)) {
    return new Date(
      dateInput[0],
      dateInput[1],
      dateInput[2],
      dateInput[3] || 0,
      dateInput[4] || 0,
      dateInput[5] || 0
    )
  }
  // Fallback
  return new Date()
}

/**
 * Props for AssessmentTimeline component
 */
interface AssessmentTimelineProps {
  events: CalendarEvent[]
  onEventClick?: (event: CalendarEvent) => void
  onEventEdit?: (event: CalendarEvent) => void
  onEventDelete?: (eventId: string) => void
  className?: string
}

/**
 * Timeline view component for assessment events
 */
export const AssessmentTimeline: React.FC<AssessmentTimelineProps> = ({
  events,
  onEventClick,
  onEventEdit,
  onEventDelete,
  className = '',
}) => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month')
  const [filterType, setFilterType] = useState<string>('all')

  // Filter events based on selected filter
  const filteredEvents = useMemo(() => {
    if (filterType === 'all') return events

    return events.filter((event) => {
      const metadata = event.metadata as AssessmentEventMetadata
      return metadata.type === filterType
    })
  }, [events, filterType])

  // Group events by date for timeline view
  const timelineEvents = useMemo(() => {
    const sortedEvents = [...filteredEvents].sort(
      (a, b) => safeToDate(a.start).getTime() - safeToDate(b.start).getTime()
    )

    const grouped: Record<string, CalendarEvent[]> = {}

    sortedEvents.forEach((event) => {
      const dateKey = safeToDate(event.start).toDateString()
      if (!grouped[dateKey]) {
        grouped[dateKey] = []
      }
      grouped[dateKey].push(event)
    })

    return grouped
  }, [filteredEvents])

  // Get date range for current view
  const getDateRange = useMemo(() => {
    const start = new Date(currentDate)
    const end = new Date(currentDate)

    switch (viewMode) {
      case 'month':
        start.setDate(1)
        end.setMonth(end.getMonth() + 1, 0)
        break
      case 'week':
        const dayOfWeek = start.getDay()
        start.setDate(start.getDate() - dayOfWeek)
        end.setDate(start.getDate() + 6)
        break
      case 'day':
        // Same day
        break
    }

    return { start, end }
  }, [currentDate, viewMode])

  // Navigate to previous/next period
  const navigatePrevious = () => {
    const newDate = new Date(currentDate)
    switch (viewMode) {
      case 'month':
        newDate.setMonth(newDate.getMonth() - 1)
        break
      case 'week':
        newDate.setDate(newDate.getDate() - 7)
        break
      case 'day':
        newDate.setDate(newDate.getDate() - 1)
        break
    }
    setCurrentDate(newDate)
  }

  const navigateNext = () => {
    const newDate = new Date(currentDate)
    switch (viewMode) {
      case 'month':
        newDate.setMonth(newDate.getMonth() + 1)
        break
      case 'week':
        newDate.setDate(newDate.getDate() + 7)
        break
      case 'day':
        newDate.setDate(newDate.getDate() + 1)
        break
    }
    setCurrentDate(newDate)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'deadline':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'session':
        return <Calendar className="h-4 w-4 text-blue-500" />
      case 'review':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'reminder':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <Calendar className="h-4 w-4 text-gray-500" />
    }
  }

  const formatTimelineDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    })
  }

  const formatTime = (dateInput: any) => {
    const date = safeToDate(dateInput)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header with navigation and controls */}
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button variant="outline" size="sm" onClick={navigatePrevious}>
              <ChevronLeft className="h-4 w-4" />
            </Button>

            <div className="text-center">
              <h2 className="text-xl font-semibold">
                {viewMode === 'month' &&
                  currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
                {viewMode === 'week' && `Week of ${formatTimelineDate(getDateRange.start)}`}
                {viewMode === 'day' && formatTimelineDate(currentDate)}
              </h2>
            </div>

            <Button variant="outline" size="sm" onClick={navigateNext}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="flex items-center space-x-2">
            <Tabs value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
              <TabsList>
                <TabsTrigger value="day">Day</TabsTrigger>
                <TabsTrigger value="week">Week</TabsTrigger>
                <TabsTrigger value="month">Month</TabsTrigger>
              </TabsList>
            </Tabs>

            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>

            <Button variant="outline" size="sm">
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </div>
      </Card>

      {/* Filter options */}
      <Card className="p-4">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium">Show:</span>
          <div className="flex space-x-2">
            {['all', 'deadline', 'session', 'review', 'reminder'].map((type) => (
              <Button
                key={type}
                variant={filterType === type ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilterType(type)}
              >
                {type.charAt(0).toUpperCase() + type.slice(1)}
              </Button>
            ))}
          </div>
        </div>
      </Card>

      {/* Timeline content */}
      <div className="space-y-4">
        {Object.entries(timelineEvents).map(([dateKey, dayEvents]) => (
          <div key={dateKey} className="relative">
            {/* Date header */}
            <div className="bg-background sticky top-0 z-10">
              <div className="mb-2 flex items-center space-x-2">
                <div className="h-2 w-2 rounded-full bg-blue-500" />
                <h3 className="text-lg font-semibold">
                  {formatTimelineDate(safeToDate(dayEvents[0].start))}
                </h3>
                <Badge variant="secondary">
                  {dayEvents.length} event{dayEvents.length !== 1 ? 's' : ''}
                </Badge>
              </div>
            </div>

            {/* Timeline events for this day */}
            <div className="ml-4 space-y-3">
              {dayEvents.map((event, index) => {
                const metadata = event.metadata as AssessmentEventMetadata

                return (
                  <div key={event.id} className="relative">
                    {/* Timeline connector */}
                    {index < dayEvents.length - 1 && (
                      <div className="absolute top-8 left-0 h-full w-px bg-gray-200" />
                    )}

                    {/* Event node */}
                    <div className="flex items-start space-x-3">
                      <div
                        className={`flex h-8 w-8 items-center justify-center rounded-full ${getStatusColor(metadata.status)} border-2`}
                      >
                        {getTypeIcon(metadata.type)}
                      </div>

                      <Card
                        className="flex-1 cursor-pointer p-4 transition-shadow hover:shadow-md"
                        onClick={() => onEventClick?.(event)}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="mb-2 flex items-center space-x-2">
                              <h4 className="font-semibold">{event.title}</h4>
                              <Badge variant="outline" className={getStatusColor(metadata.status)}>
                                {metadata.status}
                              </Badge>
                              <Badge variant="secondary">{metadata.type}</Badge>
                            </div>

                            <div className="space-y-1 text-sm text-gray-600">
                              <div className="flex items-center space-x-2">
                                <Clock className="h-4 w-4" />
                                <span>{formatTime(event.start)}</span>
                                {event.end && (
                                  <>
                                    <span>-</span>
                                    <span>{formatTime(event.end)}</span>
                                  </>
                                )}
                              </div>

                              {event.location && (
                                <div className="flex items-center space-x-2">
                                  <User className="h-4 w-4" />
                                  <span>{event.location}</span>
                                </div>
                              )}

                              {event.description && (
                                <p className="text-gray-700">{event.description}</p>
                              )}
                            </div>

                            {/* Assessment-specific details */}
                            {metadata.quarter && metadata.year && (
                              <div className="mt-2 border-t pt-2 text-sm">
                                <span className="font-medium">
                                  Q{metadata.quarter} {metadata.year}
                                </span>
                                {metadata.overallScore !== undefined && (
                                  <span className="ml-2">Score: {metadata.overallScore}%</span>
                                )}
                              </div>
                            )}
                          </div>

                          <div className="ml-4 flex space-x-1">
                            {onEventEdit && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  onEventEdit(event)
                                }}
                              >
                                Edit
                              </Button>
                            )}

                            {onEventDelete && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  onEventDelete(event.id)
                                }}
                              >
                                <XCircle className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </div>
                      </Card>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        ))}
      </div>

      {/* Empty state */}
      {Object.keys(timelineEvents).length === 0 && (
        <Card className="p-8 text-center">
          <Calendar className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <h3 className="mb-2 text-lg font-semibold">No assessment events</h3>
          <p className="text-gray-600">
            No assessment events found for the selected time period and filters.
          </p>
        </Card>
      )}
    </div>
  )
}

/**
 * Props for AssessmentTimelineItem component
 */
interface AssessmentTimelineItemProps {
  event: CalendarEvent
  isLast?: boolean
  onClick?: (event: CalendarEvent) => void
  onEdit?: (event: CalendarEvent) => void
  onDelete?: (eventId: string) => void
}

/**
 * Individual timeline item component
 */
export const AssessmentTimelineItem: React.FC<AssessmentTimelineItemProps> = ({
  event,
  isLast = false,
  onClick,
  onEdit,
  onDelete,
}) => {
  const metadata = event.metadata as AssessmentEventMetadata

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'completed':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'overdue':
        return 'bg-red-100 text-red-800 border-red-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'deadline':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'session':
        return <Calendar className="h-4 w-4 text-blue-500" />
      case 'review':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'reminder':
        return <Clock className="h-4 w-4 text-yellow-500" />
      default:
        return <Calendar className="h-4 w-4 text-gray-500" />
    }
  }

  const formatTime = (dateInput: any) => {
    const date = safeToDate(dateInput)
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  return (
    <div className="relative">
      {/* Timeline connector */}
      {!isLast && <div className="absolute top-8 left-4 h-full w-px bg-gray-200" />}

      {/* Event node */}
      <div className="flex items-start space-x-3">
        <div
          className={`flex h-8 w-8 items-center justify-center rounded-full ${getStatusColor(metadata.status)} border-2`}
        >
          {getTypeIcon(metadata.type)}
        </div>

        <Card
          className="flex-1 cursor-pointer p-4 transition-shadow hover:shadow-md"
          onClick={() => onClick?.(event)}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="mb-2 flex items-center space-x-2">
                <h4 className="font-semibold">{event.title}</h4>
                <Badge variant="outline" className={getStatusColor(metadata.status)}>
                  {metadata.status}
                </Badge>
                <Badge variant="secondary">{metadata.type}</Badge>
              </div>

              <div className="text-sm text-gray-600">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>{formatTime(event.start)}</span>
                  {event.end && (
                    <>
                      <span>-</span>
                      <span>{formatTime(event.end)}</span>
                    </>
                  )}
                </div>

                {event.description && <p className="mt-1 text-gray-700">{event.description}</p>}
              </div>
            </div>

            <div className="ml-4 flex space-x-1">
              {onEdit && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onEdit(event)
                  }}
                >
                  Edit
                </Button>
              )}

              {onDelete && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={(e) => {
                    e.stopPropagation()
                    onDelete(event.id)
                  }}
                >
                  <XCircle className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}

export default AssessmentTimeline
