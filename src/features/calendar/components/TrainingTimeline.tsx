import React, { useState, useMemo } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../../../shared/components/ui/card'
import { Badge } from '../../../shared/components/ui/badge'
import { Button } from '../../../shared/components/ui/button'
import { Progress } from '../../../shared/components/ui/progress'
import { Separator } from '../../../shared/components/ui/separator'
import {
  CalendarDays,
  Clock,
  User,
  MapPin,
  Target,
  Award,
  ChevronLeft,
  ChevronRight,
  Filter,
  Download,
} from 'lucide-react'
import type { CalendarEvent } from '../types'
import type { TrainingProgram } from '../../../shared/types/trainingProgram'

interface TrainingTimelineProps {
  events: CalendarEvent[]
  programs?: TrainingProgram[]
  onEventClick?: (event: CalendarEvent) => void
  onProgramClick?: (program: TrainingProgram) => void
  className?: string
}

/**
 * Training timeline component for visualizing training programs and events
 */
export const TrainingTimeline: React.FC<TrainingTimelineProps> = ({
  events,
  programs = [],
  onEventClick,
  onProgramClick,
  className = '',
}) => {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [viewMode, setViewMode] = useState<'month' | 'week' | 'day'>('month')
  const [selectedProgram, setSelectedProgram] = useState<string | null>(null)

  // Filter events by selected program and date range
  const filteredEvents = useMemo(() => {
    let filtered = events

    if (selectedProgram) {
      filtered = filtered.filter((event) => event.trainingProgramId === selectedProgram)
    }

    // Filter by current view date range
    const dateRange = getDateRangeForView(currentDate, viewMode)
    filtered = filtered.filter((event) => {
      const eventDate = new Date(event.start as string)
      return eventDate >= dateRange.start && eventDate <= dateRange.end
    })

    return filtered.sort(
      (a, b) => new Date(a.start as string).getTime() - new Date(b.start as string).getTime()
    )
  }, [events, selectedProgram, currentDate, viewMode])

  // Group events by date
  const eventsByDate = useMemo(() => {
    const grouped: Record<string, CalendarEvent[]> = {}

    filteredEvents.forEach((event) => {
      const dateKey = new Date(event.start as string).toDateString()
      if (!grouped[dateKey]) {
        grouped[dateKey] = []
      }
      grouped[dateKey].push(event)
    })

    return grouped
  }, [filteredEvents])

  // Calculate progress for programs
  const programProgress = useMemo(() => {
    return programs.map((program) => {
      const programEvents = events.filter((e) => e.trainingProgramId === program.id)
      const totalEvents = programEvents.length
      const completedEvents = programEvents.filter((e) => e.status === 'completed').length

      return {
        program,
        progress: totalEvents > 0 ? (completedEvents / totalEvents) * 100 : 0,
        totalEvents,
        completedEvents,
      }
    })
  }, [programs, events])

  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate)

    switch (viewMode) {
      case 'day':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1))
        break
      case 'week':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7))
        break
      case 'month':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1))
        break
    }

    setCurrentDate(newDate)
  }

  const getDateRangeForView = (date: Date, view: 'month' | 'week' | 'day') => {
    const start = new Date(date)
    const end = new Date(date)

    switch (view) {
      case 'day':
        start.setHours(0, 0, 0, 0)
        end.setHours(23, 59, 59, 999)
        break
      case 'week':
        const dayOfWeek = start.getDay()
        start.setDate(start.getDate() - dayOfWeek)
        start.setHours(0, 0, 0, 0)
        end.setDate(start.getDate() + 6)
        end.setHours(23, 59, 59, 999)
        break
      case 'month':
        start.setDate(1)
        start.setHours(0, 0, 0, 0)
        end.setMonth(end.getMonth() + 1, 0)
        end.setHours(23, 59, 59, 999)
        break
    }

    return { start, end }
  }

  const getEventTypeColor = (eventType?: string) => {
    switch (eventType) {
      case 'training_session':
        return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'milestone':
        return 'bg-purple-100 text-purple-800 border-purple-200'
      case 'program_start':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'program_end':
        return 'bg-orange-100 text-orange-800 border-orange-200'
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500'
      case 'in_progress':
        return 'bg-blue-500'
      case 'scheduled':
        return 'bg-yellow-500'
      case 'cancelled':
        return 'bg-red-500'
      default:
        return 'bg-gray-500'
    }
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CalendarDays className="h-5 w-5" />
              Training Timeline
            </CardTitle>

            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1 rounded-lg border">
                <Button variant="ghost" size="sm" onClick={() => navigateDate('prev')}>
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="px-3 py-1 text-sm font-medium">
                  {currentDate.toLocaleDateString('en-US', {
                    month: 'long',
                    year: 'numeric',
                  })}
                </span>
                <Button variant="ghost" size="sm" onClick={() => navigateDate('next')}>
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center gap-1 rounded-lg border">
                {(['day', 'week', 'month'] as const).map((mode) => (
                  <Button
                    key={mode}
                    variant={viewMode === mode ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode(mode)}
                    className="capitalize"
                  >
                    {mode}
                  </Button>
                ))}
              </div>

              <Button variant="outline" size="sm">
                <Filter className="mr-1 h-4 w-4" />
                Filter
              </Button>

              <Button variant="outline" size="sm">
                <Download className="mr-1 h-4 w-4" />
                Export
              </Button>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Program Progress Overview */}
      {programProgress.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Program Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {programProgress.map(({ program, progress, totalEvents, completedEvents }) => (
                <div
                  key={program.id}
                  className="cursor-pointer rounded-lg border p-3 hover:bg-gray-50"
                  onClick={() => onProgramClick?.(program)}
                >
                  <div className="mb-2 flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <div className={`h-3 w-3 rounded-full ${getStatusColor(program.status)}`} />
                      <span className="font-medium">{program.name}</span>
                      <Badge variant="outline">{program.status}</Badge>
                    </div>
                    <span className="text-sm text-gray-600">
                      {completedEvents}/{totalEvents} events
                    </span>
                  </div>
                  <Progress value={progress} className="h-2" />
                  <div className="mt-1 flex justify-between">
                    <span className="text-xs text-gray-600">
                      {program.instructor && `Instructor: ${program.instructor}`}
                    </span>
                    <span className="text-xs text-gray-600">{Math.round(progress)}% complete</span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Timeline Events */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">Training Events ({filteredEvents.length})</CardTitle>
            {selectedProgram && (
              <Button variant="outline" size="sm" onClick={() => setSelectedProgram(null)}>
                Clear Filter
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {Object.keys(eventsByDate).length === 0 ? (
            <div className="py-8 text-center text-gray-500">
              <CalendarDays className="mx-auto mb-2 h-12 w-12 opacity-50" />
              <p>No training events found for this period</p>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(eventsByDate).map(([dateKey, dayEvents]) => (
                <div key={dateKey}>
                  <div className="mb-3 flex items-center gap-2">
                    <h3 className="text-lg font-semibold">
                      {new Date(dateKey).toLocaleDateString('en-US', {
                        weekday: 'long',
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                      })}
                    </h3>
                    <Badge variant="secondary">{dayEvents.length} events</Badge>
                  </div>

                  <div className="space-y-3">
                    {dayEvents.map((event, index) => (
                      <div
                        key={event.id}
                        className="flex cursor-pointer gap-4 rounded-lg border p-3 hover:bg-gray-50"
                        onClick={() => onEventClick?.(event)}
                      >
                        <div className="flex flex-col items-center">
                          <div className={`h-3 w-3 rounded-full ${getStatusColor(event.status)}`} />
                          {index < dayEvents.length - 1 && (
                            <div className="mt-1 h-16 w-0.5 bg-gray-300" />
                          )}
                        </div>

                        <div className="flex-1">
                          <div className="mb-2 flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">{event.title}</h4>
                              <Badge
                                className={getEventTypeColor(event.metadata?.eventType as string)}
                                variant="outline"
                              >
                                {(event.metadata?.eventType as string)?.replace('_', ' ') ||
                                  'event'}
                              </Badge>
                            </div>
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Clock className="h-4 w-4" />
                              <span>
                                {new Date(event.start as string).toLocaleTimeString([], {
                                  hour: '2-digit',
                                  minute: '2-digit',
                                })}
                                {event.end &&
                                  ` - ${new Date(event.end as string).toLocaleTimeString([], {
                                    hour: '2-digit',
                                    minute: '2-digit',
                                  })}`}
                              </span>
                            </div>
                          </div>

                          {event.description && (
                            <p className="mb-2 text-sm text-gray-600">{event.description}</p>
                          )}

                          <div className="flex items-center gap-4 text-sm text-gray-600">
                            {event.instructor && (
                              <div className="flex items-center gap-1">
                                <User className="h-4 w-4" />
                                <span>{event.instructor}</span>
                              </div>
                            )}
                            {event.location && (
                              <div className="flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                <span>{event.location}</span>
                              </div>
                            )}
                            {event.metadata?.sessionType && (
                              <Badge variant="outline" className="text-xs">
                                {event.metadata.sessionType as string}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  <Separator className="mt-4" />
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default TrainingTimeline
