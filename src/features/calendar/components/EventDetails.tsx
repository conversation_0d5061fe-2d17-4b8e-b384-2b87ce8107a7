import React from 'react'
import { format } from 'date-fns'
import {
  Card,
  CardHeader,
  CardContent,
  CardTitle,
  CardDescription,
  CardFooter,
} from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { Separator } from '@/shared/components/ui/separator'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu'
import {
  Calendar,
  Clock,
  MapPin,
  User,
  Edit,
  Copy,
  Trash2,
  MoreHorizontal,
  CheckCircle,
  XCircle,
  AlertCircle,
  Pause,
} from 'lucide-react'

import type { CalendarEvent, EventType, EventStatus, EventPriority } from '../types'
import { DateUtils } from '@/lib/dateUtils'

interface EventDetailsProps {
  event: CalendarEvent
  onEdit?: (event: CalendarEvent) => void
  onDuplicate?: (event: CalendarEvent) => void
  onDelete?: (event: CalendarEvent) => void
  onStatusChange?: (event: CalendarEvent, status: EventStatus) => void
  onClose?: () => void
  className?: string
}

// Event type configuration with colors and labels
const eventTypeConfig: Record<EventType, { label: string; color: string }> = {
  training_session: { label: 'Training Session', color: 'bg-blue-500' },
  assessment: { label: 'Assessment', color: 'bg-red-500' },
  meeting: { label: 'Meeting', color: 'bg-green-500' },
  maintenance: { label: 'Maintenance', color: 'bg-yellow-500' },
  holiday: { label: 'Holiday', color: 'bg-purple-500' },
  deadline: { label: 'Deadline', color: 'bg-orange-500' },
  review: { label: 'Review', color: 'bg-pink-500' },
  other: { label: 'Other', color: 'bg-gray-500' },
}

const statusConfig: Record<EventStatus, { label: string; icon: React.ReactNode; color: string }> = {
  scheduled: {
    label: 'Scheduled',
    icon: <Calendar className="h-3 w-3" />,
    color: 'bg-blue-100 text-blue-800',
  },
  in_progress: {
    label: 'In Progress',
    icon: <Clock className="h-3 w-3" />,
    color: 'bg-yellow-100 text-yellow-800',
  },
  completed: {
    label: 'Completed',
    icon: <CheckCircle className="h-3 w-3" />,
    color: 'bg-green-100 text-green-800',
  },
  cancelled: {
    label: 'Cancelled',
    icon: <XCircle className="h-3 w-3" />,
    color: 'bg-red-100 text-red-800',
  },
  postponed: {
    label: 'Postponed',
    icon: <Pause className="h-3 w-3" />,
    color: 'bg-gray-100 text-gray-800',
  },
}

const priorityConfig: Record<EventPriority, { label: string; color: string }> = {
  low: { label: 'Low', color: 'bg-gray-100 text-gray-800' },
  medium: { label: 'Medium', color: 'bg-blue-100 text-blue-800' },
  high: { label: 'High', color: 'bg-orange-100 text-orange-800' },
  urgent: { label: 'Urgent', color: 'bg-red-100 text-red-800' },
}

export function EventDetails({
  event,
  onEdit,
  onDuplicate,
  onDelete,
  onStatusChange,
  onClose,
  className,
}: EventDetailsProps) {
  const eventType = event.type || 'other'
  const eventStatus = event.status || 'scheduled'
  const eventPriority = event.priority || 'medium'
  const typeConfig = eventTypeConfig[eventType]
  const statusConfigItem = statusConfig[eventStatus]
  const priorityConfigItem = priorityConfig[eventPriority]

  const formatDateRange = () => {
    if (!event.start) return 'No date set'

    const startDate = new Date(event.start as string)
    const endDate = event.end ? new Date(event.end as string) : null

    if (event.allDay) {
      if (endDate && !DateUtils.isSameDay(startDate, endDate)) {
        return `${format(startDate, 'MMM d, yyyy')} - ${format(endDate, 'MMM d, yyyy')}`
      }
      return format(startDate, 'MMMM d, yyyy')
    } else {
      const startTime = format(startDate, 'h:mm a')
      if (endDate) {
        const endTime = format(endDate, 'h:mm a')
        const sameDay = DateUtils.isSameDay(startDate, endDate)
        if (sameDay) {
          return `${format(startDate, 'MMM d, yyyy')} ${startTime} - ${endTime}`
        } else {
          return `${format(startDate, 'MMM d, yyyy h:mm a')} - ${format(endDate, 'MMM d, yyyy h:mm a')}`
        }
      }
      return `${format(startDate, 'MMM d, yyyy')} ${startTime}`
    }
  }

  const handleStatusChange = (newStatus: EventStatus) => {
    if (onStatusChange) {
      onStatusChange(event, newStatus)
    }
  }

  const handleEdit = () => {
    if (onEdit) {
      onEdit(event)
    }
  }

  const handleDuplicate = () => {
    if (onDuplicate) {
      onDuplicate(event)
    }
  }

  const handleDelete = () => {
    if (onDelete) {
      onDelete(event)
    }
  }

  return (
    <Card className={`event-details ${className}`}>
      <CardHeader>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <div className="mb-2 flex items-center gap-2">
              <Badge variant="secondary" className={typeConfig.color}>
                {typeConfig.label}
              </Badge>
              {eventStatus !== 'scheduled' && (
                <Badge variant="outline" className={statusConfigItem.color}>
                  <span className="flex items-center gap-1">
                    {statusConfigItem.icon}
                    {statusConfigItem.label}
                  </span>
                </Badge>
              )}
              <Badge variant="outline" className={priorityConfigItem.color}>
                {priorityConfigItem.label}
              </Badge>
            </div>
            <CardTitle className="text-xl">{event.title}</CardTitle>
            <CardDescription className="mt-1 flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              {formatDateRange()}
            </CardDescription>
          </div>

          <div className="flex gap-2">
            <Button size="sm" variant="outline" onClick={handleEdit}>
              <Edit className="mr-1 h-4 w-4" />
              Edit
            </Button>
            <Button size="sm" variant="outline" onClick={handleDuplicate}>
              <Copy className="mr-1 h-4 w-4" />
              Duplicate
            </Button>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button size="sm" variant="outline">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => handleStatusChange('completed')}>
                  <CheckCircle className="mr-2 h-4 w-4" />
                  Mark as Completed
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('in_progress')}>
                  <Clock className="mr-2 h-4 w-4" />
                  Mark as In Progress
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusChange('postponed')}>
                  <Pause className="mr-2 h-4 w-4" />
                  Postpone Event
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleStatusChange('cancelled')}>
                  <XCircle className="mr-2 h-4 w-4" />
                  Cancel Event
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={handleDelete} className="text-destructive">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Event
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {event.description && (
          <div>
            <h4 className="text-muted-foreground mb-1 text-sm font-medium">Description</h4>
            <p className="text-sm">{event.description}</p>
          </div>
        )}

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {event.location && (
            <div className="flex items-center gap-2">
              <MapPin className="text-muted-foreground h-4 w-4" />
              <div>
                <h4 className="text-muted-foreground text-sm font-medium">Location</h4>
                <p className="text-sm">{event.location}</p>
              </div>
            </div>
          )}

          {event.instructor && (
            <div className="flex items-center gap-2">
              <User className="text-muted-foreground h-4 w-4" />
              <div>
                <h4 className="text-muted-foreground text-sm font-medium">Instructor</h4>
                <p className="text-sm">{event.instructor}</p>
              </div>
            </div>
          )}
        </div>

        {event.createdAt && (
          <div>
            <h4 className="text-muted-foreground mb-1 text-sm font-medium">Event Information</h4>
            <div className="space-y-1 text-sm">
              <p>Created: {DateUtils.formatLong(event.createdAt)}</p>
              {event.updatedAt && <p>Updated: {DateUtils.formatLong(event.updatedAt)}</p>}
              {event.id && <p>ID: {event.id}</p>}
            </div>
          </div>
        )}

        {event.metadata && Object.keys(event.metadata).length > 0 && (
          <div>
            <h4 className="text-muted-foreground mb-1 text-sm font-medium">
              Additional Information
            </h4>
            <div className="space-y-1 text-sm">
              {Object.entries(event.metadata).map(([key, value]) => (
                <p key={key}>
                  <span className="font-medium">{key}:</span> {String(value)}
                </p>
              ))}
            </div>
          </div>
        )}
      </CardContent>

      {onClose && (
        <CardFooter>
          <Button variant="outline" onClick={onClose} className="w-full">
            Close
          </Button>
        </CardFooter>
      )}
    </Card>
  )
}

export default EventDetails
