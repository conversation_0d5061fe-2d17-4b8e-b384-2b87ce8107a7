import React, { useState, useMemo } from 'react'
import { format, eachDayOfInterval, startOfMonth, endOfMonth, isSameDay, parseISO } from 'date-fns'
import { Card, CardContent, CardHeader, CardTitle } from '../../../shared/components/ui/card'
import { Badge } from '../../../shared/components/ui/badge'
import { Button } from '../../../shared/components/ui/button'
import { Progress } from '../../../shared/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../shared/components/ui/tabs'
import { ScrollArea } from '../../../shared/components/ui/scroll-area'
import {
  Calendar,
  DollarSign,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  PieChart,
  Activity,
} from 'lucide-react'
import type { BudgetCalendarEvent } from '../types/budgetCalendar'

interface BudgetUtilizationViewProps {
  events: BudgetCalendarEvent[]
  onEventClick?: (event: BudgetCalendarEvent) => void
  className?: string
}

/**
 * BudgetUtilizationView - Displays budget utilization patterns and spending trends
 * Shows how budgets are being used over time with visual indicators
 */
export const BudgetUtilizationView: React.FC<BudgetUtilizationViewProps> = ({
  events,
  onEventClick,
  className = '',
}) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'month' | 'quarter' | 'year'>('month')
  const [selectedBudget, setSelectedBudget] = useState<string | null>(null)

  // Calculate utilization metrics
  const utilizationMetrics = useMemo(() => {
    const budgetGroups = events.reduce(
      (acc, event) => {
        const budgetId = event.budgetId || 'unknown'
        if (!acc[budgetId]) {
          acc[budgetId] = {
            budgetId,
            title: event.title,
            budgetAmount: event.budgetAmount || 0,
            currency: event.currency || 'USD',
            status: event.budgetStatus || 'unknown',
            events: [],
            totalSpent: 0,
            utilizationPercentage: 0,
          }
        }
        acc[budgetId].events.push(event)
        acc[budgetId].totalSpent += event.spentAmount || 0
        return acc
      },
      {} as Record<string, any>
    )

    // Calculate utilization percentages
    Object.values(budgetGroups).forEach((budget: any) => {
      budget.utilizationPercentage =
        budget.budgetAmount > 0 ? (budget.totalSpent / budget.budgetAmount) * 100 : 0
    })

    return Object.values(budgetGroups)
  }, [events])

  // Utilization distribution
  const utilizationDistribution = useMemo(() => {
    const distribution = {
      underUtilized: 0, // < 50%
      optimal: 0, // 50-80%
      highUtilization: 0, // 80-95%
      overBudget: 0, // > 95%
    }

    utilizationMetrics.forEach((budget: any) => {
      if (budget.utilizationPercentage < 50) distribution.underUtilized++
      else if (budget.utilizationPercentage < 80) distribution.optimal++
      else if (budget.utilizationPercentage < 95) distribution.highUtilization++
      else distribution.overBudget++
    })

    return distribution
  }, [utilizationMetrics])

  // Monthly spending trend
  const monthlySpendingTrend = useMemo(() => {
    const trend = new Map<string, number>()

    events.forEach((event) => {
      if (event.spentAmount && event.start) {
        const monthKey = format(parseISO(event.start as string), 'yyyy-MM')
        trend.set(monthKey, (trend.get(monthKey) || 0) + event.spentAmount)
      }
    })

    return Array.from(trend.entries())
      .map(([month, amount]) => ({ month, amount }))
      .sort((a, b) => a.month.localeCompare(b.month))
  }, [events])

  // Get utilization color
  const getUtilizationColor = (percentage: number) => {
    if (percentage < 50) return 'text-blue-600'
    if (percentage < 80) return 'text-green-600'
    if (percentage < 95) return 'text-yellow-600'
    return 'text-red-600'
  }

  // Get utilization badge variant
  const getUtilizationBadge = (percentage: number) => {
    if (percentage < 50) return { label: 'Under Utilized', variant: 'secondary' as const }
    if (percentage < 80) return { label: 'Optimal', variant: 'default' as const }
    if (percentage < 95) return { label: 'High Utilization', variant: 'outline' as const }
    return { label: 'Over Budget', variant: 'destructive' as const }
  }

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
        return CheckCircle
      case 'in_progress':
        return Activity
      case 'completed':
        return CheckCircle
      default:
        return AlertTriangle
    }
  }

  const totalBudgetAmount = utilizationMetrics.reduce(
    (sum, budget: any) => sum + budget.budgetAmount,
    0
  )
  const totalSpentAmount = utilizationMetrics.reduce(
    (sum, budget: any) => sum + budget.totalSpent,
    0
  )
  const overallUtilization =
    totalBudgetAmount > 0 ? (totalSpentAmount / totalBudgetAmount) * 100 : 0

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Budget Utilization</h2>
          <p className="mt-1 text-gray-600">
            Monitor spending patterns and budget utilization trends
          </p>
        </div>

        <div className="flex gap-2">
          {(['month', 'quarter', 'year'] as const).map((period) => (
            <Button
              key={period}
              variant={selectedPeriod === period ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedPeriod(period)}
              className="capitalize"
            >
              {period}
            </Button>
          ))}
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <DollarSign className="h-4 w-4 text-blue-600" />
              <span className="text-xs text-gray-500">Total Budget</span>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalBudgetAmount.toLocaleString()}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <TrendingUp className="h-4 w-4 text-green-600" />
              <span className="text-xs text-gray-500">Total Spent</span>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              ${totalSpentAmount.toLocaleString()}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <BarChart3 className="h-4 w-4 text-purple-600" />
              <span className="text-xs text-gray-500">Utilization</span>
            </div>
          </CardHeader>
          <CardContent>
            <div className={`text-2xl font-bold ${getUtilizationColor(overallUtilization)}`}>
              {overallUtilization.toFixed(1)}%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <span className="text-xs text-gray-500">Over Budget</span>
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {utilizationDistribution.overBudget}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="trends">Trends</TabsTrigger>
          <TabsTrigger value="breakdown">Breakdown</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            {/* Utilization Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Utilization Distribution
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Under Utilized (&lt;50%)</span>
                    <Badge variant="secondary">{utilizationDistribution.underUtilized}</Badge>
                  </div>
                  <Progress
                    value={
                      (utilizationDistribution.underUtilized / utilizationMetrics.length) * 100
                    }
                    className="h-2"
                  />

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Optimal (50-80%)</span>
                    <Badge variant="default">{utilizationDistribution.optimal}</Badge>
                  </div>
                  <Progress
                    value={(utilizationDistribution.optimal / utilizationMetrics.length) * 100}
                    className="h-2"
                  />

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">High Utilization (80-95%)</span>
                    <Badge variant="outline">{utilizationDistribution.highUtilization}</Badge>
                  </div>
                  <Progress
                    value={
                      (utilizationDistribution.highUtilization / utilizationMetrics.length) * 100
                    }
                    className="h-2"
                  />

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Over Budget (&gt;95%)</span>
                    <Badge variant="destructive">{utilizationDistribution.overBudget}</Badge>
                  </div>
                  <Progress
                    value={(utilizationDistribution.overBudget / utilizationMetrics.length) * 100}
                    className="h-2"
                  />
                </div>
              </CardContent>
            </Card>

            {/* Budget List */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Budget Utilization Details
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[300px]">
                  <div className="space-y-3">
                    {utilizationMetrics.map((budget: any) => {
                      const StatusIcon = getStatusIcon(budget.status)
                      const badgeInfo = getUtilizationBadge(budget.utilizationPercentage)

                      return (
                        <div
                          key={budget.budgetId}
                          className="cursor-pointer rounded-lg border p-3 transition-colors hover:bg-gray-50"
                          onClick={() =>
                            setSelectedBudget(
                              selectedBudget === budget.budgetId ? null : budget.budgetId
                            )
                          }
                        >
                          <div className="mb-2 flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <StatusIcon className="h-4 w-4" />
                              <span className="text-sm font-medium">{budget.title}</span>
                            </div>
                            <Badge variant={badgeInfo.variant} className="text-xs">
                              {badgeInfo.label}
                            </Badge>
                          </div>

                          <div className="space-y-2">
                            <div className="flex justify-between text-xs text-gray-600">
                              <span>
                                {budget.currency} {budget.totalSpent.toLocaleString()} /{' '}
                                {budget.budgetAmount.toLocaleString()}
                              </span>
                              <span className={getUtilizationColor(budget.utilizationPercentage)}>
                                {budget.utilizationPercentage.toFixed(1)}%
                              </span>
                            </div>
                            <Progress
                              value={Math.min(budget.utilizationPercentage, 100)}
                              className="h-1"
                            />
                          </div>

                          {selectedBudget === budget.budgetId && (
                            <div className="mt-3 border-t pt-3">
                              <div className="space-y-1 text-xs text-gray-600">
                                <div>Status: {budget.status}</div>
                                <div>Events: {budget.events.length}</div>
                                <div>
                                  Remaining: {budget.currency}{' '}
                                  {Math.max(
                                    0,
                                    budget.budgetAmount - budget.totalSpent
                                  ).toLocaleString()}
                                </div>
                              </div>
                              <Button
                                size="sm"
                                variant="outline"
                                className="mt-2 w-full"
                                onClick={(e) => {
                                  e.stopPropagation()
                                  if (budget.events.length > 0) {
                                    onEventClick?.(budget.events[0])
                                  }
                                }}
                              >
                                View Details
                              </Button>
                            </div>
                          )}
                        </div>
                      )
                    })}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Monthly Spending Trend
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {monthlySpendingTrend.length > 0 ? (
                  <div className="space-y-3">
                    {monthlySpendingTrend.map(({ month, amount }) => (
                      <div key={month} className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          {format(new Date(month + '-01'), 'MMMM yyyy')}
                        </span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm text-gray-600">${amount.toLocaleString()}</span>
                          <div className="w-24">
                            <Progress
                              value={
                                (amount / Math.max(...monthlySpendingTrend.map((t) => t.amount))) *
                                100
                              }
                              className="h-2"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="py-8 text-center text-gray-500">
                    <BarChart3 className="mx-auto mb-2 h-12 w-12 opacity-50" />
                    <p>No spending data available for the selected period</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="breakdown" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5" />
                Budget Breakdown by Category
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {utilizationMetrics.map((budget: any) => {
                  const percentage =
                    totalBudgetAmount > 0 ? (budget.budgetAmount / totalBudgetAmount) * 100 : 0

                  return (
                    <div key={budget.budgetId} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{budget.title}</span>
                        <span className="text-sm text-gray-600">
                          {percentage.toFixed(1)}% of total
                        </span>
                      </div>
                      <div className="flex gap-2">
                        <Progress value={percentage} className="h-2 flex-1" />
                        <span className="min-w-[80px] text-right text-sm font-medium">
                          ${budget.budgetAmount.toLocaleString()}
                        </span>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default BudgetUtilizationView
