import React, { useState, useEffect } from 'react'
import { format, addDays, startOfWeek, endOfWeek, eachDayOfInterval } from 'date-fns'
import {
  Calendar,
  Clock,
  Users,
  MapPin,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Plus,
  Edit,
  Trash2,
  Filter,
  Search,
  BarChart3,
  <PERSON>tings,
  Eye,
} from 'lucide-react'
import type {
  Resource,
  ResourceBooking,
  ResourceConflict,
  ResourceAvailability,
  ResourceSchedulingRequest,
  UtilizationMetrics,
  ResourceSearchFilter,
  ResourceType,
  BookingStatus,
  ResourceStatus,
} from '../types/resources'
import type { EntityId } from '../../../shared/types/common'
import {
  useResources,
  useResourceAvailability,
  useResourceBooking,
  useResourceConflicts,
  useResourceUtilization,
  useResourceAnalytics,
} from '../hooks/useResourceScheduling'

/**
 * Resource availability calendar view component
 */
export const ResourceAvailabilityCalendar: React.FC<{
  resourceId: EntityId
  startDate: Date
  endDate: Date
}> = ({ resourceId, startDate, endDate }) => {
  const { availability, loading, error, getAvailability } = useResourceAvailability(resourceId)
  const { bookings } = useResourceBooking(resourceId)
  const [selectedDate, setSelectedDate] = useState<Date | null>(null)

  useEffect(() => {
    getAvailability(startDate, endDate)
  }, [resourceId, startDate, endDate, getAvailability])

  const days = eachDayOfInterval({ start: startDate, end: endDate })
  const hours = Array.from({ length: 24 }, (_, i) => i)

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4">
        <div className="flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
          <span className="text-red-700">{error}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="overflow-hidden rounded-lg bg-white shadow">
      <div className="border-b p-4">
        <h3 className="text-lg font-semibold text-gray-900">Resource Availability</h3>
        <p className="text-sm text-gray-600">
          {format(startDate, 'MMM dd, yyyy')} - {format(endDate, 'MMM dd, yyyy')}
        </p>
      </div>

      <div className="overflow-x-auto">
        <div className="min-w-full">
          {/* Header with days */}
          <div className="grid grid-cols-25 border-b bg-gray-50">
            <div className="p-2 text-xs font-medium text-gray-500">Time</div>
            {days.map((day) => (
              <div
                key={day.toISOString()}
                className="p-2 text-center text-xs font-medium text-gray-500"
              >
                <div>{format(day, 'EEE')}</div>
                <div>{format(day, 'dd')}</div>
              </div>
            ))}
          </div>

          {/* Time slots */}
          {hours.map((hour) => (
            <div key={hour} className="grid grid-cols-25 border-b">
              <div className="border-r p-2 text-xs text-gray-500">
                {format(new Date().setHours(hour, 0, 0, 0), 'HH:mm')}
              </div>
              {days.map((day) => {
                const dateTime = new Date(day)
                dateTime.setHours(hour, 0, 0, 0)

                const hasBooking = bookings.some((booking) => {
                  const bookingStart = new Date(booking.startTime)
                  const bookingEnd = new Date(booking.endTime)
                  return dateTime >= bookingStart && dateTime < bookingEnd
                })

                const isAvailable = availability.some(
                  (avail) =>
                    avail.date.toDateString() === day.toDateString() &&
                    avail.timeSlots.some((slot) => {
                      const [slotHour] = slot.startTime.split(':').map(Number)
                      return slotHour === hour && slot.available
                    })
                )

                return (
                  <div
                    key={`${day.toISOString()}-${hour}`}
                    className={`cursor-pointer border-r p-1 transition-colors ${
                      hasBooking
                        ? 'bg-red-100 hover:bg-red-200'
                        : isAvailable
                          ? 'bg-green-100 hover:bg-green-200'
                          : 'bg-gray-100 hover:bg-gray-200'
                    }`}
                    onClick={() => setSelectedDate(dateTime)}
                  >
                    <div className="flex h-6 w-full items-center justify-center">
                      {hasBooking && <div className="h-2 w-2 rounded-full bg-red-500"></div>}
                      {!hasBooking && isAvailable && (
                        <div className="h-2 w-2 rounded-full bg-green-500"></div>
                      )}
                    </div>
                  </div>
                )
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

/**
 * Resource booking interface component
 */
export const ResourceBookingInterface: React.FC<{
  resourceId: EntityId
  onBookingCreated?: (booking: ResourceBooking) => void
}> = ({ resourceId, onBookingCreated }) => {
  const { bookResource, loading } = useResourceBooking(resourceId)
  const { checkAvailability: checkResourceAvailability } = useResourceAvailability(resourceId)
  const [formData, setFormData] = useState({
    title: '',
    startTime: '',
    endTime: '',
    priority: 'medium' as const,
    notes: '',
  })
  const [availabilityCheck, setAvailabilityCheck] = useState<boolean | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError(null)

    try {
      const startTime = new Date(formData.startTime)
      const endTime = new Date(formData.endTime)

      if (startTime >= endTime) {
        setError('End time must be after start time')
        return
      }

      const request: ResourceSchedulingRequest = {
        resourceId,
        title: formData.title,
        startTime,
        endTime,
        priority: formData.priority,
        requestedBy: 'current-user', // Would come from auth context
        notes: formData.notes,
      }

      const result = await bookResource(request)

      if (result.success && result.booking) {
        onBookingCreated?.(result.booking)
        // Reset form
        setFormData({
          title: '',
          startTime: '',
          endTime: '',
          priority: 'medium',
          notes: '',
        })
        setAvailabilityCheck(null)
      } else {
        setError(result.message || 'Failed to create booking')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create booking')
    }
  }

  const handleCheckAvailability = async () => {
    if (!formData.startTime || !formData.endTime) return

    try {
      const startTime = new Date(formData.startTime)
      const endTime = new Date(formData.endTime)
      const isAvailable = await checkResourceAvailability(startTime, endTime)
      setAvailabilityCheck(isAvailable)
    } catch (err) {
      setAvailabilityCheck(false)
    }
  }

  return (
    <div className="rounded-lg bg-white p-6 shadow">
      <h3 className="mb-4 text-lg font-semibold text-gray-900">Book Resource</h3>

      {error && (
        <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-3">
          <div className="flex items-center">
            <XCircle className="mr-2 h-5 w-5 text-red-500" />
            <span className="text-red-700">{error}</span>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="mb-1 block text-sm font-medium text-gray-700">Booking Title</label>
          <input
            type="text"
            value={formData.title}
            onChange={(e) => setFormData({ ...formData, title: e.target.value })}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">Start Time</label>
            <input
              type="datetime-local"
              value={formData.startTime}
              onChange={(e) => setFormData({ ...formData, startTime: e.target.value })}
              className="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              required
            />
          </div>

          <div>
            <label className="mb-1 block text-sm font-medium text-gray-700">End Time</label>
            <input
              type="datetime-local"
              value={formData.endTime}
              onChange={(e) => setFormData({ ...formData, endTime: e.target.value })}
              className="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              required
            />
          </div>
        </div>

        <div>
          <label className="mb-1 block text-sm font-medium text-gray-700">Priority</label>
          <select
            value={formData.priority}
            onChange={(e) => setFormData({ ...formData, priority: e.target.value as any })}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
          >
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>

        <div>
          <label className="mb-1 block text-sm font-medium text-gray-700">Notes</label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            rows={3}
            className="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
          />
        </div>

        {availabilityCheck !== null && (
          <div
            className={`rounded-lg p-3 ${
              availabilityCheck
                ? 'border border-green-200 bg-green-50'
                : 'border border-red-200 bg-red-50'
            }`}
          >
            <div className="flex items-center">
              {availabilityCheck ? (
                <>
                  <CheckCircle className="mr-2 h-5 w-5 text-green-500" />
                  <span className="text-green-700">Resource is available for selected time</span>
                </>
              ) : (
                <>
                  <XCircle className="mr-2 h-5 w-5 text-red-500" />
                  <span className="text-red-700">Resource is not available for selected time</span>
                </>
              )}
            </div>
          </div>
        )}

        <div className="flex space-x-3">
          <button
            type="button"
            onClick={handleCheckAvailability}
            className="flex-1 rounded-md bg-gray-100 px-4 py-2 text-gray-700 transition-colors hover:bg-gray-200"
          >
            Check Availability
          </button>
          <button
            type="submit"
            disabled={loading || availabilityCheck === false}
            className="flex-1 rounded-md bg-blue-600 px-4 py-2 text-white transition-colors hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
          >
            {loading ? 'Creating...' : 'Create Booking'}
          </button>
        </div>
      </form>
    </div>
  )
}

/**
 * Conflict resolution dialog component
 */
export const ConflictResolutionDialog: React.FC<{
  conflicts: ResourceConflict[]
  onResolve: (conflictId: string, resolution: string) => void
  onCancel: () => void
}> = ({ conflicts, onResolve, onCancel }) => {
  const [selectedConflict, setSelectedConflict] = useState<string | null>(null)
  const [resolution, setResolution] = useState('')

  const handleResolve = () => {
    if (selectedConflict && resolution) {
      onResolve(selectedConflict, resolution)
    }
  }

  return (
    <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
      <div className="max-h-[80vh] w-full max-w-2xl overflow-hidden rounded-lg bg-white shadow-xl">
        <div className="border-b p-6">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Resolve Resource Conflicts</h3>
            <button onClick={onCancel} className="text-gray-400 hover:text-gray-600">
              <XCircle className="h-6 w-6" />
            </button>
          </div>
        </div>

        <div className="max-h-[60vh] overflow-y-auto p-6">
          <div className="space-y-4">
            {conflicts.map((conflict) => (
              <div
                key={conflict.id}
                className={`cursor-pointer rounded-lg border p-4 transition-colors ${
                  selectedConflict === conflict.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => setSelectedConflict(conflict.id)}
              >
                <div className="flex items-start">
                  <AlertTriangle
                    className={`mt-0.5 mr-3 h-5 w-5 ${
                      conflict.severity === 'critical'
                        ? 'text-red-500'
                        : conflict.severity === 'high'
                          ? 'text-orange-500'
                          : conflict.severity === 'medium'
                            ? 'text-yellow-500'
                            : 'text-gray-500'
                    }`}
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900">{conflict.description}</div>
                    <div className="mt-1 text-sm text-gray-600">
                      Type: {conflict.conflictType} • Severity: {conflict.severity}
                    </div>
                    <div className="text-sm text-gray-600">
                      {conflict.bookingIds.length} booking(s) affected
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {selectedConflict && (
            <div className="mt-6">
              <label className="mb-2 block text-sm font-medium text-gray-700">Resolution</label>
              <textarea
                value={resolution}
                onChange={(e) => setResolution(e.target.value)}
                rows={3}
                placeholder="Describe how you resolved this conflict..."
                className="w-full rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
              />
            </div>
          )}
        </div>

        <div className="border-t bg-gray-50 p-6">
          <div className="flex justify-end space-x-3">
            <button
              onClick={onCancel}
              className="rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleResolve}
              disabled={!selectedConflict || !resolution}
              className="rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 disabled:cursor-not-allowed disabled:opacity-50"
            >
              Resolve Conflict
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

/**
 * Resource utilization dashboard component
 */
export const ResourceUtilizationDashboard: React.FC<{
  resourceId?: EntityId
  dateRange: { start: Date; end: Date }
}> = ({ resourceId, dateRange }) => {
  const { utilization, loading, error, getUtilization } = useResourceUtilization(resourceId || '')
  const { analytics, loading: analyticsLoading } = useResourceAnalytics(dateRange)

  useEffect(() => {
    if (resourceId) {
      getUtilization(resourceId, dateRange)
    }
  }, [resourceId, dateRange, getUtilization])

  if (loading || analyticsLoading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4">
        <div className="flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
          <span className="text-red-700">{error}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Overall Analytics */}
      {analytics && (
        <div className="rounded-lg bg-white p-6 shadow">
          <h3 className="mb-4 text-lg font-semibold text-gray-900">Overall Resource Analytics</h3>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            <div className="rounded-lg bg-blue-50 p-4">
              <div className="flex items-center">
                <BarChart3 className="mr-3 h-8 w-8 text-blue-600" />
                <div>
                  <div className="text-2xl font-bold text-blue-900">{analytics.totalResources}</div>
                  <div className="text-sm text-blue-600">Total Resources</div>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-green-50 p-4">
              <div className="flex items-center">
                <CheckCircle className="mr-3 h-8 w-8 text-green-600" />
                <div>
                  <div className="text-2xl font-bold text-green-900">
                    {analytics.averageUtilizationRate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-green-600">Avg Utilization</div>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-yellow-50 p-4">
              <div className="flex items-center">
                <AlertTriangle className="mr-3 h-8 w-8 text-yellow-600" />
                <div>
                  <div className="text-2xl font-bold text-yellow-900">
                    {analytics.conflictStats.totalConflicts}
                  </div>
                  <div className="text-sm text-yellow-600">Total Conflicts</div>
                </div>
              </div>
            </div>

            <div className="rounded-lg bg-purple-50 p-4">
              <div className="flex items-center">
                <Users className="mr-3 h-8 w-8 text-purple-600" />
                <div>
                  <div className="text-2xl font-bold text-purple-900">
                    {analytics.conflictStats.resolvedConflicts}
                  </div>
                  <div className="text-sm text-purple-600">Resolved</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Resource-specific Utilization */}
      {utilization && (
        <div className="rounded-lg bg-white p-6 shadow">
          <h3 className="mb-4 text-lg font-semibold text-gray-900">Resource Utilization Details</h3>

          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            <div>
              <div className="mb-2 text-sm font-medium text-gray-500">Total Bookings</div>
              <div className="text-2xl font-bold text-gray-900">{utilization.totalBookings}</div>
            </div>

            <div>
              <div className="mb-2 text-sm font-medium text-gray-500">Total Hours</div>
              <div className="text-2xl font-bold text-gray-900">
                {utilization.totalHours.toFixed(1)}
              </div>
            </div>

            <div>
              <div className="mb-2 text-sm font-medium text-gray-500">Utilization Rate</div>
              <div className="text-2xl font-bold text-gray-900">
                {utilization.utilizationRate.toFixed(1)}%
              </div>
            </div>

            <div>
              <div className="mb-2 text-sm font-medium text-gray-500">Avg Duration</div>
              <div className="text-2xl font-bold text-gray-900">
                {utilization.averageBookingDuration.toFixed(0)}m
              </div>
            </div>

            <div>
              <div className="mb-2 text-sm font-medium text-gray-500">No-Show Rate</div>
              <div className="text-2xl font-bold text-gray-900">
                {utilization.noShowRate.toFixed(1)}%
              </div>
            </div>
          </div>

          {/* Booking Status Breakdown */}
          <div className="mt-6">
            <h4 className="mb-3 text-sm font-medium text-gray-500">Booking Status Breakdown</h4>
            <div className="space-y-2">
              {Object.entries(utilization.bookingsByStatus).map(([status, count]) => (
                <div key={status} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 capitalize">
                    {status.replace('_', ' ')}
                  </span>
                  <span className="text-sm font-medium text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Main resource list component
 */
export const ResourceList: React.FC<{
  onResourceSelect?: (resource: Resource) => void
}> = ({ onResourceSelect }) => {
  const { resources, loading, error, deleteResource } = useResources()
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState<ResourceType | ''>('')
  const [filterStatus, setFilterStatus] = useState<ResourceStatus | ''>('')

  const filteredResources = resources.filter((resource) => {
    const matchesSearch =
      resource.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      resource.description?.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = !filterType || resource.type === filterType
    const matchesStatus = !filterStatus || resource.status === filterStatus

    return matchesSearch && matchesType && matchesStatus
  })

  const getStatusColor = (status: ResourceStatus) => {
    switch (status) {
      case 'available':
        return 'text-green-600 bg-green-100'
      case 'occupied':
        return 'text-red-600 bg-red-100'
      case 'maintenance':
        return 'text-yellow-600 bg-yellow-100'
      case 'unavailable':
        return 'text-gray-600 bg-gray-100'
      case 'reserved':
        return 'text-blue-600 bg-blue-100'
      case 'retired':
        return 'text-gray-600 bg-gray-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  const getTypeIcon = (type: ResourceType) => {
    switch (type) {
      case 'room':
        return <MapPin className="h-4 w-4" />
      case 'equipment':
        return <Settings className="h-4 w-4" />
      case 'instructor':
        return <Users className="h-4 w-4" />
      default:
        return <Calendar className="h-4 w-4" />
    }
  }

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="rounded-lg border border-red-200 bg-red-50 p-4">
        <div className="flex items-center">
          <AlertTriangle className="mr-2 h-5 w-5 text-red-500" />
          <span className="text-red-700">{error}</span>
        </div>
      </div>
    )
  }

  return (
    <div className="rounded-lg bg-white shadow">
      <div className="border-b p-6">
        <div className="mb-4 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900">Resources</h3>
          <button className="flex items-center rounded-md bg-blue-600 px-3 py-2 text-white hover:bg-blue-700">
            <Plus className="mr-2 h-4 w-4" />
            Add Resource
          </button>
        </div>

        <div className="flex flex-col gap-4 sm:flex-row">
          <div className="relative flex-1">
            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
            <input
              type="text"
              placeholder="Search resources..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full rounded-md border border-gray-300 py-2 pr-3 pl-10 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            />
          </div>

          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value as ResourceType)}
            className="rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
          >
            <option value="">All Types</option>
            <option value="room">Room</option>
            <option value="equipment">Equipment</option>
            <option value="instructor">Instructor</option>
            <option value="material">Material</option>
            <option value="vehicle">Vehicle</option>
          </select>

          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value as ResourceStatus)}
            className="rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
          >
            <option value="">All Status</option>
            <option value="available">Available</option>
            <option value="occupied">Occupied</option>
            <option value="maintenance">Maintenance</option>
            <option value="unavailable">Unavailable</option>
            <option value="reserved">Reserved</option>
          </select>
        </div>
      </div>

      <div className="divide-y">
        {filteredResources.map((resource) => (
          <div
            key={resource.id}
            className="cursor-pointer p-4 transition-colors hover:bg-gray-50"
            onClick={() => onResourceSelect?.(resource)}
          >
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0">{getTypeIcon(resource.type)}</div>
                <div>
                  <div className="font-medium text-gray-900">{resource.name}</div>
                  <div className="text-sm text-gray-500">{resource.description}</div>
                  {resource.location && (
                    <div className="mt-1 flex items-center text-sm text-gray-500">
                      <MapPin className="mr-1 h-3 w-3" />
                      {resource.location}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900 capitalize">
                    {resource.type}
                  </div>
                  {resource.capacity && (
                    <div className="text-sm text-gray-500">Capacity: {resource.capacity}</div>
                  )}
                </div>

                <span
                  className={`rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(resource.status)}`}
                >
                  {resource.status}
                </span>

                <div className="flex items-center space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      // Edit functionality
                    }}
                    className="p-1 text-gray-400 hover:text-gray-600"
                  >
                    <Edit className="h-4 w-4" />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation()
                      deleteResource(resource.id).catch(console.error)
                    }}
                    className="p-1 text-gray-400 hover:text-red-600"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {filteredResources.length === 0 && (
        <div className="p-8 text-center text-gray-500">
          <Calendar className="mx-auto mb-4 h-12 w-12 text-gray-300" />
          <p>No resources found matching your criteria.</p>
        </div>
      )}
    </div>
  )
}

/**
 * Main resource scheduling component
 */
export const ResourceScheduling: React.FC = () => {
  const [selectedResource, setSelectedResource] = useState<Resource | null>(null)
  const [activeTab, setActiveTab] = useState<'list' | 'availability' | 'booking' | 'analytics'>(
    'list'
  )
  const [dateRange, setDateRange] = useState({
    start: startOfWeek(new Date()),
    end: endOfWeek(addDays(new Date(), 7)),
  })

  const handleResourceSelect = (resource: Resource) => {
    setSelectedResource(resource)
    setActiveTab('availability')
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Resource Scheduling</h2>
        <div className="flex items-center space-x-4">
          <button className="flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-gray-700 hover:bg-gray-50">
            <Filter className="mr-2 h-4 w-4" />
            Filters
          </button>
          <button className="flex items-center rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700">
            <BarChart3 className="mr-2 h-4 w-4" />
            Analytics
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="flex space-x-8">
          {[
            { id: 'list', label: 'Resources', icon: <Users className="h-4 w-4" /> },
            { id: 'availability', label: 'Availability', icon: <Calendar className="h-4 w-4" /> },
            { id: 'booking', label: 'Booking', icon: <Clock className="h-4 w-4" /> },
            { id: 'analytics', label: 'Analytics', icon: <BarChart3 className="h-4 w-4" /> },
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex items-center border-b-2 px-1 py-4 text-sm font-medium ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700'
              }`}
            >
              {tab.icon}
              <span className="ml-2">{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'list' && <ResourceList onResourceSelect={handleResourceSelect} />}

        {activeTab === 'availability' && selectedResource && (
          <ResourceAvailabilityCalendar
            resourceId={selectedResource.id}
            startDate={dateRange.start}
            endDate={dateRange.end}
          />
        )}

        {activeTab === 'booking' && selectedResource && (
          <ResourceBookingInterface
            resourceId={selectedResource.id}
            onBookingCreated={(booking) => {
              console.log('Booking created:', booking)
              setActiveTab('availability')
            }}
          />
        )}

        {activeTab === 'analytics' && (
          <ResourceUtilizationDashboard resourceId={selectedResource?.id} dateRange={dateRange} />
        )}

        {!selectedResource &&
          (activeTab === 'availability' ||
            activeTab === 'booking' ||
            activeTab === 'analytics') && (
            <div className="rounded-lg bg-white p-8 text-center shadow">
              <Users className="mx-auto mb-4 h-12 w-12 text-gray-300" />
              <p className="text-gray-500">Please select a resource to view {activeTab}.</p>
              <button
                onClick={() => setActiveTab('list')}
                className="mt-4 rounded-md bg-blue-600 px-4 py-2 text-white hover:bg-blue-700"
              >
                Browse Resources
              </button>
            </div>
          )}
      </div>
    </div>
  )
}

export default ResourceScheduling
