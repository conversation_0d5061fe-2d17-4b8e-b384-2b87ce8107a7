import React, { useState, useCallback } from 'react'
import { Input } from '@/shared/components/ui/input'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu'
import { Calendar, Filter, Search, X, ChevronDown, User, MapPin, Clock } from 'lucide-react'

import type {
  CalendarEventFilter,
  EventType,
  EventStatus,
  EventPriority,
  CalendarEvent,
} from '../types'

interface EventFiltersProps {
  filter: CalendarEventFilter
  onFilterChange: (filter: CalendarEventFilter) => void
  events?: CalendarEvent[]
  className?: string
}

// Event type configuration
const eventTypeOptions: { value: EventType; label: string }[] = [
  { value: 'training_session', label: 'Training Session' },
  { value: 'assessment', label: 'Assessment' },
  { value: 'meeting', label: 'Meeting' },
  { value: 'maintenance', label: 'Maintenance' },
  { value: 'holiday', label: 'Holiday' },
  { value: 'deadline', label: 'Deadline' },
  { value: 'review', label: 'Review' },
  { value: 'other', label: 'Other' },
]

const statusOptions: { value: EventStatus; label: string }[] = [
  { value: 'scheduled', label: 'Scheduled' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'cancelled', label: 'Cancelled' },
  { value: 'postponed', label: 'Postponed' },
]

const priorityOptions: { value: EventPriority; label: string }[] = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'urgent', label: 'Urgent' },
]

const dateRangePresets = [
  { label: 'Today', days: 0 },
  { label: 'This Week', days: 7 },
  { label: 'This Month', days: 30 },
  { label: 'Next 3 Months', days: 90 },
  { label: 'This Year', days: 365 },
]

export function EventFilters({ filter, onFilterChange, events, className }: EventFiltersProps) {
  const [searchTerm, setSearchTerm] = useState(filter.instructor || filter.location || '')

  const handleSearchChange = useCallback(
    (value: string) => {
      setSearchTerm(value)
      onFilterChange({
        ...filter,
        instructor: value || undefined,
        location: value || undefined,
      })
    },
    [filter, onFilterChange]
  )

  const handleTypeFilterChange = useCallback(
    (value: string) => {
      if (value === 'all') {
        onFilterChange({ ...filter, type: undefined })
      } else {
        onFilterChange({ ...filter, type: [value as EventType] })
      }
    },
    [filter, onFilterChange]
  )

  const handleStatusFilterChange = useCallback(
    (value: string) => {
      if (value === 'all') {
        onFilterChange({ ...filter, status: undefined })
      } else {
        onFilterChange({ ...filter, status: [value as EventStatus] })
      }
    },
    [filter, onFilterChange]
  )

  const handlePriorityFilterChange = useCallback(
    (value: string) => {
      if (value === 'all') {
        onFilterChange({ ...filter, priority: undefined })
      } else {
        onFilterChange({ ...filter, priority: [value as EventPriority] })
      }
    },
    [filter, onFilterChange]
  )

  const handleDateRangePreset = useCallback(
    (days: number) => {
      const end = new Date()
      const start = new Date()
      if (days > 0) {
        start.setDate(start.getDate() - days)
      } else {
        start.setHours(0, 0, 0, 0)
        end.setHours(23, 59, 59, 999)
      }

      onFilterChange({
        ...filter,
        dateRange: { start, end },
      })
    },
    [filter, onFilterChange]
  )

  const clearAllFilters = useCallback(() => {
    setSearchTerm('')
    onFilterChange({})
  }, [onFilterChange])

  const getActiveFiltersCount = () => {
    let count = 0
    if (filter.type?.length) count++
    if (filter.status?.length) count++
    if (filter.priority?.length) count++
    if (filter.dateRange) count++
    if (filter.instructor || filter.location) count++
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <div className={`event-filters space-y-4 ${className}`}>
      {/* Search Bar */}
      <div className="relative">
        <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
        <Input
          placeholder="Search events by title, instructor, or location..."
          value={searchTerm}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="pl-10"
        />
        {searchTerm && (
          <Button
            size="sm"
            variant="ghost"
            onClick={() => handleSearchChange('')}
            className="absolute top-1/2 right-1 h-8 w-8 -translate-y-1/2 transform p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Filter Controls */}
      <div className="flex flex-wrap gap-2">
        {/* Event Type Filter */}
        <Select value={filter.type?.[0] || 'all'} onValueChange={handleTypeFilterChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Event Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            {eventTypeOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Status Filter */}
        <Select value={filter.status?.[0] || 'all'} onValueChange={handleStatusFilterChange}>
          <SelectTrigger className="w-[140px]">
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            {statusOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Priority Filter */}
        <Select value={filter.priority?.[0] || 'all'} onValueChange={handlePriorityFilterChange}>
          <SelectTrigger className="w-[120px]">
            <SelectValue placeholder="Priority" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Priority</SelectItem>
            {priorityOptions.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {/* Date Range Presets */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="w-[140px] justify-between">
              <Calendar className="mr-2 h-4 w-4" />
              Date Range
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent>
            {dateRangePresets.map((preset) => (
              <DropdownMenuItem
                key={preset.label}
                onClick={() => handleDateRangePreset(preset.days)}
              >
                {preset.label}
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => onFilterChange({ ...filter, dateRange: undefined })}>
              Clear Date Filter
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Clear All Filters */}
        {activeFiltersCount > 0 && (
          <Button variant="outline" onClick={clearAllFilters}>
            <X className="mr-2 h-4 w-4" />
            Clear All ({activeFiltersCount})
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {filter.type?.map((type) => (
            <Badge key={type} variant="secondary" className="flex items-center gap-1">
              Type: {eventTypeOptions.find((opt) => opt.value === type)?.label}
              <Button
                size="sm"
                variant="ghost"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => handleTypeFilterChange('all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}

          {filter.status?.map((status) => (
            <Badge key={status} variant="secondary" className="flex items-center gap-1">
              Status: {statusOptions.find((opt) => opt.value === status)?.label}
              <Button
                size="sm"
                variant="ghost"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => handleStatusFilterChange('all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}

          {filter.priority?.map((priority) => (
            <Badge key={priority} variant="secondary" className="flex items-center gap-1">
              Priority: {priorityOptions.find((opt) => opt.value === priority)?.label}
              <Button
                size="sm"
                variant="ghost"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => handlePriorityFilterChange('all')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}

          {filter.dateRange && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Calendar className="h-3 w-3" />
              Date Range
              <Button
                size="sm"
                variant="ghost"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => onFilterChange({ ...filter, dateRange: undefined })}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}

          {(filter.instructor || filter.location) && (
            <Badge variant="secondary" className="flex items-center gap-1">
              <Search className="h-3 w-3" />
              Search: "{searchTerm}"
              <Button
                size="sm"
                variant="ghost"
                className="h-4 w-4 p-0 hover:bg-transparent"
                onClick={() => handleSearchChange('')}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          )}
        </div>
      )}

      {/* Results Summary */}
      {events && (
        <div className="text-muted-foreground text-sm">
          Showing {events.length} event{events.length !== 1 ? 's' : ''}
          {activeFiltersCount > 0 &&
            ` with ${activeFiltersCount} filter${activeFiltersCount !== 1 ? 's' : ''} applied`}
        </div>
      )}
    </div>
  )
}

export default EventFilters
