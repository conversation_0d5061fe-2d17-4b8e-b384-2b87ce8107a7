import React from 'react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubContent,
  DropdownMenuSubTrigger,
  DropdownMenuTrigger,
} from '@/shared/components/ui/dropdown-menu'
import { Button } from '@/shared/components/ui/button'
import {
  Edit,
  Copy,
  Trash2,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  Pause,
  MoreHorizontal,
  Move,
  Repeat,
} from 'lucide-react'

import type { CalendarEvent, EventStatus } from '../types'

interface EventContextMenuProps {
  event: CalendarEvent
  onEdit?: (event: CalendarEvent) => void
  onDuplicate?: (event: CalendarEvent, offsetDays?: number) => void
  onDelete?: (event: CalendarEvent) => void
  onStatusChange?: (event: CalendarEvent, status: EventStatus) => void
  onMove?: (event: CalendarEvent, newDate: Date) => void
  children: React.ReactNode
}

const duplicateOptions = [
  { label: 'Tomorrow', offsetDays: 1 },
  { label: 'Next Week', offsetDays: 7 },
  { label: 'Next Month', offsetDays: 30 },
  { label: 'Custom...', offsetDays: 0 },
]

export function EventContextMenu({
  event,
  onEdit,
  onDuplicate,
  onDelete,
  onStatusChange,
  onMove,
  children,
}: EventContextMenuProps) {
  const handleStatusChange = (status: EventStatus) => {
    if (onStatusChange) {
      onStatusChange(event, status)
    }
  }

  const handleDuplicate = (offsetDays?: number) => {
    if (onDuplicate) {
      onDuplicate(event, offsetDays)
    }
  }

  const handleEdit = () => {
    if (onEdit) {
      onEdit(event)
    }
  }

  const handleDelete = () => {
    if (onDelete) {
      onDelete(event)
    }
  }

  const handleMoveToToday = () => {
    if (onMove) {
      const today = new Date()
      const eventStart = new Date(event.start as string)
      const timeDiff = eventStart.getTime() - eventStart.setHours(0, 0, 0, 0)
      today.setHours(0, 0, 0, 0)
      today.setTime(today.getTime() + timeDiff)
      onMove(event, today)
    }
  }

  const handleMoveToTomorrow = () => {
    if (onMove) {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      const eventStart = new Date(event.start as string)
      const timeDiff = eventStart.getTime() - eventStart.setHours(0, 0, 0, 0)
      tomorrow.setHours(0, 0, 0, 0)
      tomorrow.setTime(tomorrow.getTime() + timeDiff)
      onMove(event, tomorrow)
    }
  }

  const handleMoveToNextWeek = () => {
    if (onMove) {
      const nextWeek = new Date()
      nextWeek.setDate(nextWeek.getDate() + 7)
      const eventStart = new Date(event.start as string)
      const timeDiff = eventStart.getTime() - eventStart.setHours(0, 0, 0, 0)
      nextWeek.setHours(0, 0, 0, 0)
      nextWeek.setTime(nextWeek.getTime() + timeDiff)
      onMove(event, nextWeek)
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>{children}</DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {/* Primary Actions */}
        <DropdownMenuItem onClick={handleEdit}>
          <Edit className="mr-2 h-4 w-4" />
          Edit Event
        </DropdownMenuItem>

        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <Copy className="mr-2 h-4 w-4" />
            Duplicate
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            {duplicateOptions.map((option) => (
              <DropdownMenuItem
                key={option.label}
                onClick={() => handleDuplicate(option.offsetDays)}
              >
                {option.label}
              </DropdownMenuItem>
            ))}
          </DropdownMenuSubContent>
        </DropdownMenuSub>

        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <Move className="mr-2 h-4 w-4" />
            Move to
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            <DropdownMenuItem onClick={handleMoveToToday}>
              <Calendar className="mr-2 h-4 w-4" />
              Today
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleMoveToTomorrow}>
              <Calendar className="mr-2 h-4 w-4" />
              Tomorrow
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleMoveToNextWeek}>
              <Calendar className="mr-2 h-4 w-4" />
              Next Week
            </DropdownMenuItem>
          </DropdownMenuSubContent>
        </DropdownMenuSub>

        <DropdownMenuSeparator />

        {/* Status Actions */}
        <DropdownMenuSub>
          <DropdownMenuSubTrigger>
            <Clock className="mr-2 h-4 w-4" />
            Change Status
          </DropdownMenuSubTrigger>
          <DropdownMenuSubContent>
            <DropdownMenuItem onClick={() => handleStatusChange('scheduled')}>
              <Calendar className="mr-2 h-4 w-4" />
              Scheduled
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleStatusChange('in_progress')}>
              <Clock className="mr-2 h-4 w-4" />
              In Progress
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleStatusChange('completed')}>
              <CheckCircle className="mr-2 h-4 w-4" />
              Completed
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleStatusChange('postponed')}>
              <Pause className="mr-2 h-4 w-4" />
              Postponed
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleStatusChange('cancelled')}>
              <XCircle className="mr-2 h-4 w-4" />
              Cancelled
            </DropdownMenuItem>
          </DropdownMenuSubContent>
        </DropdownMenuSub>

        <DropdownMenuSeparator />

        {/* Destructive Action */}
        <DropdownMenuItem onClick={handleDelete} className="text-destructive">
          <Trash2 className="mr-2 h-4 w-4" />
          Delete Event
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Context Menu Trigger Button for inline use
export function EventContextMenuTrigger({
  event,
  onEdit,
  onDuplicate,
  onDelete,
  onStatusChange,
  onMove,
  className,
}: EventContextMenuProps & { className?: string }) {
  return (
    <EventContextMenu
      event={event}
      onEdit={onEdit}
      onDuplicate={onDuplicate}
      onDelete={onDelete}
      onStatusChange={onStatusChange}
      onMove={onMove}
    >
      <Button variant="ghost" size="sm" className={`h-8 w-8 p-0 ${className}`}>
        <MoreHorizontal className="h-4 w-4" />
      </Button>
    </EventContextMenu>
  )
}

export default EventContextMenu
