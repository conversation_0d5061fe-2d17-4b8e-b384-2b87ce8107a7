import type {
  CalendarEvent,
  EventType,
  EventStatus,
  EventPriority,
  CalendarEventFilter,
  CalendarViewType,
} from './calendar'
import type {
  TrainingBudget,
  TrainingSchedule,
  TrainingBudgetItem,
  BudgetApproval,
  CreateTrainingSchedule,
  UpdateTrainingSchedule,
} from '../../../shared/types/trainingBudget'

/**
 * Budget-specific calendar event types
 */
export type BudgetEventType =
  | 'budget_creation'
  | 'budget_approval_deadline'
  | 'budget_expiry'
  | 'training_session'
  | 'milestone_deadline'
  | 'resource_allocation'
  | 'budget_review'
  | 'spending_milestone'
  | 'cost_reminder'
  | 'utilization_checkpoint'

/**
 * Extended event type that includes budget events
 */
export type BudgetCalendarEventType = EventType | BudgetEventType

/**
 * Budget calendar event interface extending CalendarEvent
 */
export interface BudgetCalendarEvent extends CalendarEvent {
  // Budget-specific properties
  budgetId?: string
  budgetType?: BudgetEventType
  budgetStatus?: 'planned' | 'approved' | 'in_progress' | 'completed' | 'cancelled' | 'expired'

  // Financial information
  budgetAmount?: number
  currency?: string
  spentAmount?: number
  remainingAmount?: number
  utilizationPercentage?: number

  // Budget metadata
  budgetCategory?: string
  budgetProvider?: string
  budgetLocation?: string
  trainingType?: string

  // Schedule-specific properties
  scheduleId?: string
  maxParticipants?: number
  attendeesCount?: number
  instructorCost?: number
  materialCost?: number

  // Approval workflow
  approvalStatus?: 'pending' | 'approved' | 'rejected'
  approverId?: string
  approvalDeadline?: Date

  // Milestone tracking
  milestoneType?: 'start' | 'midpoint' | 'completion' | 'review'
  milestoneProgress?: number

  // Resource allocation
  allocatedResources?: BudgetResourceAllocation[]
  resourceConflicts?: string[]

  // Notifications and reminders
  reminderSettings?: BudgetReminderSettings
  notificationSent?: boolean

  // Analytics data
  utilizationTrend?: number[]
  costEfficiency?: number
  roiMetrics?: ROIMetrics
}

/**
 * Resource allocation for budget events
 */
export interface BudgetResourceAllocation {
  id: string
  type: 'instructor' | 'facility' | 'equipment' | 'material' | 'travel'
  name: string
  allocatedCost: number
  actualCost?: number
  utilizationRate?: number
  availabilityWindow?: {
    start: Date
    end: Date
  }
}

/**
 * Reminder settings for budget events
 */
export interface BudgetReminderSettings {
  enabled: boolean
  reminderTimes: number[] // minutes before event
  reminderTypes: ('email' | 'in_app' | 'sms')[]
  escalationRules?: {
    triggerAfter: number // minutes
    escalationTo: string[]
  }
}

/**
 * ROI metrics for budget events
 */
export interface ROIMetrics {
  trainingEffectiveness: number
  costPerParticipant: number
  skillImprovement: number
  productivityGain: number
  timeToCompetency: number
}

/**
 * Budget calendar view configurations
 */
export interface BudgetCalendarView {
  id: string
  name: string
  type: 'timeline' | 'utilization' | 'resources' | 'deadlines' | 'overview'
  defaultViewType: CalendarViewType
  filters: BudgetCalendarFilter
  displayOptions: BudgetDisplayOptions
  permissions: BudgetViewPermissions
}

/**
 * Budget-specific calendar filters
 */
export interface BudgetCalendarFilter extends CalendarEventFilter {
  // Budget-specific filters
  budgetType?: BudgetEventType[]
  budgetStatus?: ('planned' | 'approved' | 'in_progress' | 'completed' | 'cancelled' | 'expired')[]
  budgetId?: string[]
  budgetAmountRange?: {
    min: number
    max: number
  }
  utilizationRange?: {
    min: number
    max: number
  }

  // Financial filters
  currency?: string[]
  hasBudgetOverrun?: boolean
  upcomingDeadline?: boolean

  // Training-specific filters
  trainingType?: string[]
  instructors?: string[]
  locations?: string[]
  participantCountRange?: {
    min: number
    max: number
  }

  // Approval filters
  approvalStatus?: ('pending' | 'approved' | 'rejected')[]
  approverId?: string[]

  // Resource filters
  resourceType?: ('instructor' | 'facility' | 'equipment' | 'material' | 'travel')[]
  hasResourceConflicts?: boolean
}

/**
 * Display options for budget calendar views
 */
export interface BudgetDisplayOptions {
  // Color coding
  colorBy: 'budget_type' | 'utilization' | 'status' | 'priority' | 'cost'
  colorScheme: 'default' | 'utilization' | 'risk' | 'status'

  // Information display
  showBudgetAmount: boolean
  showUtilization: boolean
  showAttendees: boolean
  showInstructor: boolean
  showResources: boolean

  // Visual elements
  showProgressBars: boolean
  showUtilizationHeatmap: boolean
  showConflictIndicators: boolean
  showMilestones: boolean

  // Layout options
  compactMode: boolean
  groupByBudget: boolean
  showBudgetTotals: boolean
}

/**
 * Permissions for budget calendar views
 */
export interface BudgetViewPermissions {
  canCreate: boolean
  canEdit: boolean
  canDelete: boolean
  canApprove: boolean
  canViewFinancials: boolean
  canManageResources: boolean
  canExport: boolean
  budgetIds?: string[] // Specific budgets user can access
}

/**
 * Budget calendar statistics
 */
export interface BudgetCalendarStats {
  // Overall statistics
  totalBudgets: number
  totalBudgetAmount: number
  totalSpent: number
  averageUtilization: number

  // Event breakdown
  eventsByBudgetType: Record<BudgetEventType, number>
  eventsByStatus: Record<string, number>
  upcomingDeadlines: BudgetCalendarEvent[]
  overdueBudgets: BudgetCalendarEvent[]

  // Financial metrics
  budgetOverruns: BudgetCalendarEvent[]
  underutilizedBudgets: BudgetCalendarEvent[]
  costEfficiency: number
  roiAverage: number

  // Resource metrics
  resourceUtilization: Record<string, number>
  resourceConflicts: BudgetCalendarEvent[]
  upcomingResourceAllocations: BudgetCalendarEvent[]

  // Time-based analytics
  monthlySpending: number[]
  monthlyUtilization: number[]
  trainingDensity: number[]
}

/**
 * Budget calendar analytics data
 */
export interface BudgetCalendarAnalytics {
  // Utilization trends
  utilizationTrends: {
    period: 'daily' | 'weekly' | 'monthly' | 'quarterly'
    data: Array<{
      date: string
      utilization: number
      budgetCount: number
      spent: number
    }>
  }

  // Cost analysis
  costAnalysis: {
    totalCost: number
    costByCategory: Record<string, number>
    costByTrainingType: Record<string, number>
    averageCostPerTraining: number
    costVariance: number
  }

  // Training effectiveness
  trainingEffectiveness: {
    completionRate: number
    averageAttendance: number
    skillImprovementScore: number
    timeToCompetency: number
    participantSatisfaction: number
  }

  // Resource optimization
  resourceOptimization: {
    utilizationRates: Record<string, number>
    conflictFrequency: number
    allocationEfficiency: number
    resourceCostSavings: number
  }

  // Predictive analytics
  predictions: {
    budgetExhaustion: Array<{
      budgetId: string
      estimatedExhaustionDate: Date
      confidence: number
    }>
    resourceShortages: Array<{
      resourceType: string
      shortageDate: Date
      severity: 'low' | 'medium' | 'high'
    }>
  }
}

/**
 * Budget calendar export options
 */
export interface BudgetCalendarExportOptions {
  format: 'json' | 'csv' | 'excel' | 'pdf' | 'ics'
  dateRange?: {
    start: Date
    end: Date
  }
  includeAnalytics: boolean
  includeFinancials: boolean
  includeResources: boolean
  filter?: BudgetCalendarFilter
  groupBy?: 'budget' | 'date' | 'type' | 'status'
}

/**
 * Budget calendar notification types
 */
export interface BudgetCalendarNotification {
  id: string
  type:
    | 'deadline_reminder'
    | 'budget_exhaustion'
    | 'resource_conflict'
    | 'approval_required'
    | 'milestone_reached'
  budgetId?: string
  eventId?: string
  title: string
  message: string
  severity: 'info' | 'warning' | 'error' | 'success'
  scheduledFor: Date
  sentAt?: Date
  recipients: string[]
  channels: ('email' | 'in_app' | 'sms')[]
  metadata?: Record<string, unknown>
}

/**
 * Drag and drop data for budget scheduling
 */
export interface BudgetDragDropData {
  eventType: 'budget_move' | 'schedule_move' | 'resource_reallocate'
  draggedEvent: BudgetCalendarEvent
  targetDate: Date
  targetResource?: string
  conflictResolution?: 'auto_resolve' | 'manual_review' | 'cancel'
}

/**
 * Budget calendar creation form data
 */
export interface CreateBudgetCalendarEvent {
  title: string
  description?: string
  start: Date
  end?: Date
  allDay?: boolean
  budgetId?: string
  budgetType: BudgetEventType
  budgetAmount?: number
  currency?: string
  location?: string
  instructor?: string
  maxParticipants?: number
  resources?: BudgetResourceAllocation[]
  reminderSettings?: BudgetReminderSettings
  metadata?: Record<string, unknown>
}

/**
 * Budget calendar update form data
 */
export interface UpdateBudgetCalendarEvent extends Partial<CreateBudgetCalendarEvent> {
  id: string
  status?: EventStatus
  budgetStatus?: 'planned' | 'approved' | 'in_progress' | 'completed' | 'cancelled' | 'expired'
  spentAmount?: number
  attendeesCount?: number
  milestoneProgress?: number
  approvalStatus?: 'pending' | 'approved' | 'rejected'
}

/**
 * Budget calendar conflict resolution
 */
export interface BudgetCalendarConflict {
  id: string
  type: 'resource_conflict' | 'budget_overrun' | 'schedule_overlap' | 'approval_deadline'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  involvedEvents: string[]
  suggestedResolution: string
  autoResolvable: boolean
  resolutionOptions: Array<{
    id: string
    label: string
    description: string
    impact: string
  }>
}

/**
 * Budget calendar settings
 */
export interface BudgetCalendarSettings {
  defaultView: BudgetCalendarView
  defaultCurrency: string
  workingHours: {
    start: string
    end: string
    days: number[]
  }
  notificationDefaults: BudgetReminderSettings
  conflictResolution: 'auto' | 'manual' | 'hybrid'
  analyticsRefreshInterval: number // minutes
  exportDefaults: BudgetCalendarExportOptions
}
