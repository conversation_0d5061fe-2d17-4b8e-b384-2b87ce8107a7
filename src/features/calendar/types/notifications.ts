import type { EntityId, ISODateString } from '../../../shared/types/common'
import type { CalendarEvent, EventType } from './calendar'

/**
 * Calendar notification types
 */
export type CalendarNotificationType =
  | 'event_reminder'
  | 'event_created'
  | 'event_updated'
  | 'event_deleted'
  | 'event_cancelled'
  | 'resource_conflict'
  | 'training_deadline'
  | 'assessment_reminder'
  | 'calendar_shared'
  | 'invitation_received'
  | 'invitation_accepted'
  | 'invitation_declined'

/**
 * Notification status tracking
 */
export type NotificationStatus = 'pending' | 'scheduled' | 'sent' | 'delivered' | 'read' | 'failed'

/**
 * Reminder types for different notification channels
 */
export type ReminderType = 'email' | 'push' | 'in_app' | 'sms'

/**
 * Notification priority levels
 */
export type NotificationPriority = 'low' | 'medium' | 'high' | 'urgent'

/**
 * Calendar notification interface
 */
export interface CalendarNotification {
  id: string
  type: CalendarNotificationType
  title: string
  message: string
  eventId?: string
  userId: string
  scheduledFor: Date
  sentAt?: Date
  readAt?: Date
  status: NotificationStatus
  priority: NotificationPriority
  reminderType: ReminderType
  metadata: NotificationMetadata
  createdAt: ISODateString
  updatedAt: ISODateString
}

/**
 * Event reminder interface
 */
export interface EventReminder {
  id: string
  eventId: string
  userId: string
  reminderType: ReminderType
  scheduledFor: Date
  sent: boolean
  sentAt?: Date
  metadata: ReminderMetadata
  createdAt: ISODateString
  updatedAt: ISODateString
}

/**
 * Notification scheduling interface
 */
export interface NotificationSchedule {
  id: string
  notificationId: string
  cronExpression?: string
  oneTimeAt?: Date
  recurring?: {
    frequency: 'daily' | 'weekly' | 'monthly' | 'yearly'
    interval: number
    endDate?: Date
  }
  enabled: boolean
  lastRun?: Date
  nextRun?: Date
  createdAt: ISODateString
  updatedAt: ISODateString
}

/**
 * Notification preference interface
 */
export interface NotificationPreferences {
  userId: string
  enabled: boolean
  eventReminders: {
    enabled: boolean
    defaultReminders: ReminderTime[]
    customReminders: CustomReminder[]
  }
  changeNotifications: {
    enabled: boolean
    eventCreated: boolean
    eventUpdated: boolean
    eventDeleted: boolean
    eventCancelled: boolean
  }
  resourceNotifications: {
    enabled: boolean
    conflicts: boolean
    availability: boolean
  }
  trainingNotifications: {
    enabled: boolean
    deadlines: boolean
    milestones: boolean
    completion: boolean
  }
  assessmentNotifications: {
    enabled: boolean
    deadlines: boolean
    reviews: boolean
    results: boolean
  }
  sharingNotifications: {
    enabled: boolean
    invitations: boolean
    acceptance: boolean
  }
  channels: {
    email: boolean
    push: boolean
    inApp: boolean
    sms: boolean
  }
  quietHours: {
    enabled: boolean
    startTime: string // HH:mm format
    endTime: string // HH:mm format
    timezone: string
  }
  createdAt: ISODateString
  updatedAt: ISODateString
}

/**
 * Reminder time configuration
 */
export interface ReminderTime {
  value: number
  unit: 'minutes' | 'hours' | 'days'
  beforeEvent: boolean
}

/**
 * Custom reminder configuration
 */
export interface CustomReminder {
  id: string
  name: string
  eventType?: EventType
  reminderTime: ReminderTime
  reminderType: ReminderType
  enabled: boolean
}

/**
 * Notification metadata
 */
export interface NotificationMetadata {
  eventId?: string
  eventTitle?: string
  eventStart?: Date
  eventEnd?: Date
  eventLocation?: string
  eventInstructor?: string
  resourceType?: string
  resourceName?: string
  conflictDetails?: ConflictDetails
  invitationDetails?: InvitationDetails
  [key: string]: unknown
}

/**
 * Reminder metadata
 */
export interface ReminderMetadata {
  eventId: string
  eventTitle: string
  eventStart: Date
  eventEnd?: Date
  reminderOffset: number // in minutes
  reminderType: ReminderType
  [key: string]: unknown
}

/**
 * Conflict details for resource conflicts
 */
export interface ConflictDetails {
  resourceId: string
  resourceName: string
  resourceType: string
  conflictType: 'double_booking' | 'overlapping' | 'capacity_exceeded'
  conflictingEvents: Array<{
    id: string
    title: string
    start: Date
    end: Date
  }>
  suggestedAlternatives?: Array<{
    resourceId: string
    resourceName: string
    availableSlots: Array<{
      start: Date
      end: Date
    }>
  }>
}

/**
 * Invitation details for calendar sharing
 */
export interface InvitationDetails {
  calendarId: string
  calendarName: string
  inviterName: string
  inviterEmail: string
  permissionLevel: 'view' | 'edit' | 'manage'
  invitationToken?: string
  expiresAt?: Date
}

/**
 * Notification history entry
 */
export interface NotificationHistoryEntry {
  id: string
  notificationId: string
  userId: string
  action: 'sent' | 'delivered' | 'read' | 'failed' | 'cancelled'
  timestamp: Date
  details?: string
  error?: string
}

/**
 * Notification template
 */
export interface NotificationTemplate {
  id: string
  type: CalendarNotificationType
  name: string
  subject: string
  bodyTemplate: string // Template with placeholders like {{eventTitle}}, {{eventDate}}, etc.
  variables: Array<{
    name: string
    type: 'string' | 'date' | 'number' | 'boolean'
    required: boolean
    defaultValue?: unknown
  }>
  enabled: boolean
  createdAt: ISODateString
  updatedAt: ISODateString
}

/**
 * Notification batch operation
 */
export interface NotificationBatch {
  id: string
  type: 'send' | 'cancel' | 'reschedule'
  notificationIds: string[]
  status: 'pending' | 'processing' | 'completed' | 'failed'
  processedCount: number
  totalCount: number
  errors?: Array<{
    notificationId: string
    error: string
  }>
  createdAt: ISODateString
  completedAt?: ISODateString
}

/**
 * Notification statistics
 */
export interface NotificationStats {
  totalNotifications: number
  notificationsByType: Record<CalendarNotificationType, number>
  notificationsByStatus: Record<NotificationStatus, number>
  notificationsByChannel: Record<ReminderType, number>
  sentToday: number
  sentThisWeek: number
  sentThisMonth: number
  deliveryRate: number
  readRate: number
  failureRate: number
  averageDeliveryTime: number // in milliseconds
}

/**
 * Notification filter options
 */
export interface NotificationFilter {
  type?: CalendarNotificationType[]
  status?: NotificationStatus[]
  priority?: NotificationPriority[]
  reminderType?: ReminderType[]
  userId?: string
  eventId?: string
  dateRange?: {
    start: Date
    end: Date
  }
  read?: boolean
  search?: string
}

/**
 * Notification delivery options
 */
export interface NotificationDeliveryOptions {
  immediate?: boolean
  scheduledFor?: Date
  retryOnFailure?: boolean
  maxRetries?: number
  retryDelay?: number // in minutes
  priority?: NotificationPriority
  quietHoursRespect?: boolean
}

/**
 * Calendar notification settings (simplified version for user preferences)
 */
export interface CalendarNotificationUserSettings {
  enabled: boolean
  reminders: {
    beforeEvent: number // minutes before
    beforeDeadline: number // minutes before
  }
  emailNotifications: boolean
  inAppNotifications: boolean
  pushNotifications: boolean
  smsNotifications: boolean
  quietHours: {
    enabled: boolean
    startTime: string
    endTime: string
  }
}
