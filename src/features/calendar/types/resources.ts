import type { EntityId, ISODateString } from '../../../shared/types/common'
import type { CalendarEvent } from './calendar'

/**
 * Resource types supported by the scheduling system
 */
export type ResourceType =
  | 'room'
  | 'equipment'
  | 'instructor'
  | 'material'
  | 'vehicle'
  | 'facility'
  | 'software'
  | 'other'

/**
 * Resource status tracking
 */
export type ResourceStatus =
  | 'available'
  | 'occupied'
  | 'maintenance'
  | 'unavailable'
  | 'reserved'
  | 'retired'

/**
 * Booking status for resource allocations
 */
export type BookingStatus =
  | 'pending'
  | 'confirmed'
  | 'cancelled'
  | 'completed'
  | 'no_show'
  | 'rejected'

/**
 * Priority levels for resource bookings
 */
export type BookingPriority = 'low' | 'medium' | 'high' | 'critical'

/**
 * Resource availability pattern types
 */
export type AvailabilityPatternType =
  | 'daily'
  | 'weekly'
  | 'monthly'
  | 'custom'
  | 'holiday'
  | 'maintenance'

/**
 * Time slot for resource availability
 */
export interface TimeSlot {
  id: string
  startTime: string // HH:mm format
  endTime: string // HH:mm format
  daysOfWeek: number[] // 0-6, Sunday = 0
  available: boolean
  maxConcurrentBookings?: number
  notes?: string
}

/**
 * Recurring availability pattern
 */
export interface AvailabilityPattern {
  id: string
  type: AvailabilityPatternType
  name: string
  timeSlots: TimeSlot[]
  startDate: Date
  endDate?: Date
  isActive: boolean
  exceptions: Date[] // Dates when pattern doesn't apply
}

/**
 * Resource metadata for different resource types
 */
export interface ResourceMetadata {
  // Common metadata
  brand?: string
  model?: string
  serialNumber?: string
  purchaseDate?: ISODateString
  warrantyExpiry?: ISODateString
  cost?: number
  currency?: string

  // Room specific
  capacity?: number
  floor?: number
  building?: string
  hasProjector?: boolean
  hasWhiteboard?: boolean
  hasComputer?: boolean
  accessibilityFeatures?: string[]

  // Equipment specific
  category?: string
  specifications?: Record<string, unknown>
  maintenanceInterval?: number // in days
  lastMaintenanceDate?: ISODateString
  nextMaintenanceDate?: ISODateString

  // Instructor specific
  specialties?: string[]
  certifications?: string[]
  maxConcurrentSessions?: number
  rating?: number
  hourlyRate?: number

  // Vehicle specific
  licensePlate?: string
  vehicleType?: string
  fuelType?: string
  mileage?: number
  seatingCapacity?: number

  // Material specific
  quantity?: number
  unit?: string
  reorderLevel?: number
  supplier?: string

  // Custom fields
  customFields?: Record<string, unknown>
}

/**
 * Resource availability information
 */
export interface ResourceAvailability {
  id: string
  resourceId: EntityId
  date: Date
  timeSlots: TimeSlot[]
  patterns: AvailabilityPattern[]
  isAvailable: boolean
  utilizationRate?: number
  notes?: string
}

/**
 * Resource booking information
 */
export interface ResourceBooking {
  id: EntityId
  resourceId: EntityId
  eventId?: EntityId
  title: string
  startTime: Date
  endTime: Date
  status: BookingStatus
  priority: BookingPriority
  assignedTo?: string
  requestedBy?: string
  approvedBy?: string
  notes?: string
  cost?: number
  currency?: string
  metadata?: Record<string, unknown>

  // Timestamps
  createdAt: ISODateString
  updatedAt: ISODateString
  requestedAt?: ISODateString
  approvedAt?: ISODateString
  cancelledAt?: ISODateString
}

/**
 * Resource conflict information
 */
export interface ResourceConflict {
  id: string
  resourceId: EntityId
  bookingIds: EntityId[]
  conflictType: 'overlap' | 'capacity_exceeded' | 'maintenance' | 'unavailable'
  severity: 'low' | 'medium' | 'high' | 'critical'
  description: string
  suggestedResolution?: string
  resolved: boolean
  resolvedAt?: ISODateString
  resolvedBy?: string
}

/**
 * Resource utilization metrics
 */
export interface UtilizationMetrics {
  resourceId: EntityId
  dateRange: {
    start: Date
    end: Date
  }
  totalBookings: number
  totalHours: number
  utilizationRate: number // percentage
  peakUtilizationTime?: TimeSlot
  averageBookingDuration: number // in minutes
  noShowRate: number // percentage
  revenue?: number
  costPerHour?: number

  // Breakdown by booking status
  bookingsByStatus: Record<BookingStatus, number>

  // Breakdown by time periods
  utilizationByDay: Record<string, number>
  utilizationByWeek: Record<string, number>
  utilizationByMonth: Record<string, number>
}

/**
 * Resource scheduling request
 */
export interface ResourceSchedulingRequest {
  resourceId: EntityId
  eventId?: EntityId
  title: string
  startTime: Date
  endTime: Date
  priority: BookingPriority
  requestedBy: string
  notes?: string
  metadata?: Record<string, unknown>
  flexibleDates?: boolean
  alternativeResources?: EntityId[]
}

/**
 * Resource scheduling response
 */
export interface ResourceSchedulingResponse {
  success: boolean
  booking?: ResourceBooking
  conflicts?: ResourceConflict[]
  alternatives?: ResourceSchedulingResponse[]
  message?: string
}

/**
 * Resource search filters
 */
export interface ResourceSearchFilter {
  type?: ResourceType[]
  status?: ResourceStatus[]
  location?: string
  capacity?: {
    min?: number
    max?: number
  }
  available?: boolean
  dateRange?: {
    start: Date
    end: Date
  }
  features?: string[]
  priceRange?: {
    min?: number
    max?: number
  }
  searchText?: string
}

/**
 * Resource analytics data
 */
export interface ResourceAnalytics {
  totalResources: number
  resourcesByType: Record<ResourceType, number>
  resourcesByStatus: Record<ResourceStatus, number>
  averageUtilizationRate: number
  totalRevenue: number
  topUtilizedResources: Array<{
    resourceId: EntityId
    name: string
    utilizationRate: number
    totalBookings: number
  }>
  conflictStats: {
    totalConflicts: number
    resolvedConflicts: number
    avgResolutionTime: number // in hours
  }
  trends: {
    utilization: Array<{
      date: string
      rate: number
    }>
    bookings: Array<{
      date: string
      count: number
    }>
    conflicts: Array<{
      date: string
      count: number
    }>
  }
}

/**
 * Resource notification settings
 */
export interface ResourceNotificationSettings {
  id: EntityId
  resourceId: EntityId
  bookingReminders: {
    enabled: boolean
    beforeMinutes: number[]
  }
  conflictAlerts: {
    enabled: boolean
    severity: ('low' | 'medium' | 'high' | 'critical')[]
  }
  utilizationReports: {
    enabled: boolean
    frequency: 'daily' | 'weekly' | 'monthly'
    recipients: string[]
  }
  maintenanceReminders: {
    enabled: boolean
    beforeDays: number
  }
}

/**
 * Resource maintenance schedule
 */
export interface MaintenanceSchedule {
  id: EntityId
  resourceId: EntityId
  title: string
  description?: string
  scheduledStart: Date
  scheduledEnd: Date
  actualStart?: Date
  actualEnd?: Date
  type: 'preventive' | 'corrective' | 'emergency'
  status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled'
  assignedTo?: string
  cost?: number
  parts?: string[]
  notes?: string
  createdAt: ISODateString
  updatedAt: ISODateString
}

/**
 * Main resource interface
 */
export interface Resource {
  id: EntityId
  name: string
  type: ResourceType
  description?: string
  location?: string
  status: ResourceStatus
  capacity?: number
  availability: ResourceAvailability[]
  bookings: ResourceBooking[]
  metadata: ResourceMetadata
  notificationSettings?: ResourceNotificationSettings
  maintenanceSchedule?: MaintenanceSchedule[]

  // Timestamps
  createdAt: ISODateString
  updatedAt: ISODateString
  createdBy?: EntityId
  updatedBy?: EntityId

  // Additional properties
  isActive: boolean
  public?: boolean // Whether resource is visible to all users
  tags?: string[]
  category?: string
  department?: string
}

/**
 * Resource calendar event extension
 * Extends the base CalendarEvent with resource-specific information
 */
export interface ResourceCalendarEvent extends CalendarEvent {
  resourceBookings: ResourceBooking[]
  resourceConflicts?: ResourceConflict[]
  resourceUtilization?: UtilizationMetrics[]
}

/**
 * Resource scheduling state
 */
export interface ResourceSchedulingState {
  resources: Resource[]
  selectedResource: Resource | null
  bookings: ResourceBooking[]
  conflicts: ResourceConflict[]
  loading: boolean
  error: string | null
  filters: ResourceSearchFilter
  analytics: ResourceAnalytics | null
  dateRange: {
    start: Date
    end: Date
  }
}

/**
 * Resource scheduling actions
 */
export type ResourceSchedulingAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'SET_RESOURCES'; payload: Resource[] }
  | { type: 'ADD_RESOURCE'; payload: Resource }
  | { type: 'UPDATE_RESOURCE'; payload: Resource }
  | { type: 'DELETE_RESOURCE'; payload: EntityId }
  | { type: 'SET_SELECTED_RESOURCE'; payload: Resource | null }
  | { type: 'SET_BOOKINGS'; payload: ResourceBooking[] }
  | { type: 'ADD_BOOKING'; payload: ResourceBooking }
  | { type: 'UPDATE_BOOKING'; payload: ResourceBooking }
  | { type: 'DELETE_BOOKING'; payload: EntityId }
  | { type: 'SET_CONFLICTS'; payload: ResourceConflict[] }
  | { type: 'ADD_CONFLICT'; payload: ResourceConflict }
  | { type: 'RESOLVE_CONFLICT'; payload: { conflictId: string; resolution: string } }
  | { type: 'SET_FILTERS'; payload: ResourceSearchFilter }
  | { type: 'SET_DATE_RANGE'; payload: { start: Date; end: Date } }
  | { type: 'SET_ANALYTICS'; payload: ResourceAnalytics }

/**
 * Utility types for resource operations
 */
export type ResourceFormData = Omit<Resource, 'id' | 'createdAt' | 'updatedAt' | 'bookings'> &
  Partial<Pick<Resource, 'id'>>
export type BookingFormData = Omit<ResourceBooking, 'id' | 'createdAt' | 'updatedAt'> &
  Partial<Pick<ResourceBooking, 'id'>>
export type ResourceModalMode = 'create' | 'edit' | 'view'
export type BookingModalMode = 'create' | 'edit' | 'view' | 'approve' | 'reject'

/**
 * Resource export/import types
 */
export interface ResourceExportOptions {
  format: 'json' | 'csv' | 'xlsx'
  includeBookings?: boolean
  includeAnalytics?: boolean
  dateRange?: {
    start: Date
    end: Date
  }
  filter?: ResourceSearchFilter
}

/**
 * Resource validation rules
 */
export interface ResourceValidation {
  name?: {
    required: boolean
    minLength: number
    maxLength: number
  }
  description?: {
    required: boolean
    maxLength: number
  }
  capacity?: {
    required: boolean
    min: number
    max: number
  }
  metadata?: {
    required: string[]
    validation: Record<
      string,
      {
        type: 'string' | 'number' | 'boolean' | 'date'
        required: boolean
        min?: number
        max?: number
      }
    >
  }
}
