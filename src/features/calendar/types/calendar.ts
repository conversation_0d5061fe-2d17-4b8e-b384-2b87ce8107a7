import type { DateInput, EventInput, EventContentArg } from '@fullcalendar/core'
import type { EntityId, ISODateString } from '../../../shared/types/common'

/**
 * Date selection info interface (FullCalendar compatible)
 */
export interface CalendarDateSelectInfo {
  start: Date
  end: Date
  startStr: string
  endStr: string
  allDay: boolean
  view: {
    calendar: {
      unselect: () => void
      addEvent: (event: EventInput) => void
    }
  }
}

/**
 * Event click info interface (FullCalendar compatible)
 */
export interface CalendarEventClickInfo {
  event: {
    id: string
    title: string
    start: Date | null
    end: Date | null
    remove: () => void
  }
  el: HTMLElement
}

/**
 * Calendar event types for different kinds of activities
 */
export type EventType =
  | 'training_session'
  | 'assessment'
  | 'meeting'
  | 'maintenance'
  | 'holiday'
  | 'deadline'
  | 'review'
  | 'other'

/**
 * Event status tracking
 */
export type EventStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled' | 'postponed'

/**
 * Event priority levels
 */
export type EventPriority = 'low' | 'medium' | 'high' | 'urgent'

/**
 * Calendar view types supported by the application
 */
export type CalendarViewType =
  | 'dayGridMonth'
  | 'timeGridWeek'
  | 'timeGridDay'
  | 'listWeek'
  | 'multiMonthYear'

/**
 * Resource allocation for calendar events
 * @deprecated Use Resource from resources.ts for comprehensive resource management
 */
export interface EventResource {
  id: string
  title: string
  type: 'instructor' | 'facility' | 'equipment' | 'room'
  capacity?: number
  available?: boolean
  metadata?: Record<string, unknown>
}

/**
 * Extended calendar event interface that extends FullCalendar's EventInput
 */
export interface CalendarEvent extends EventInput {
  id: EntityId
  title: string
  start: DateInput
  end?: DateInput
  allDay?: boolean

  // Extended properties for GT-EGA specific functionality
  description?: string
  type?: EventType
  status?: EventStatus
  priority?: EventPriority

  // Resource allocation
  resourceId?: string
  resources?: EventResource[]

  // Metadata
  location?: string
  instructor?: string
  traineeIds?: EntityId[]
  assessmentId?: EntityId
  trainingProgramId?: EntityId

  // Timestamps
  createdAt?: ISODateString
  updatedAt?: ISODateString
  createdBy?: EntityId

  // Additional metadata for extensibility
  metadata?: Record<string, unknown>
}

/**
 * Calendar state management interface
 */
export interface CalendarState {
  weekendsVisible: boolean
  currentEvents: CalendarEvent[]
  selectedEvent: CalendarEvent | null
  loading: boolean
  error: string | null
  currentView: CalendarViewType
  currentDate: Date
}

/**
 * Data transfer object for creating new events
 */
export interface CreateEventDto {
  title: string
  start: DateInput
  end?: DateInput
  allDay?: boolean
  description?: string
  type?: EventType
  priority?: EventPriority
  location?: string
  resourceId?: string
  traineeIds?: EntityId[]
  assessmentId?: EntityId
  trainingProgramId?: EntityId
  metadata?: Record<string, unknown>
}

/**
 * Data transfer object for updating existing events
 */
export interface UpdateEventDto {
  id: EntityId
  title?: string
  start?: DateInput
  end?: DateInput
  allDay?: boolean
  description?: string
  type?: EventType
  status?: EventStatus
  priority?: EventPriority
  location?: string
  resourceId?: string
  traineeIds?: EntityId[]
  assessmentId?: EntityId
  trainingProgramId?: EntityId
  metadata?: Record<string, unknown>
}

/**
 * Calendar configuration options
 */
export interface CalendarConfig {
  defaultView: CalendarViewType
  defaultDuration: string
  slotDuration: string
  scrollTime: string
  firstDay: number
  weekendsVisible: boolean
  editable: boolean
  selectable: boolean
  navLinks: boolean
  nowIndicator: boolean
  stickyHeaderDates: boolean
  dayMaxEvents: boolean
  selectMirror: boolean
  multiMonthMaxColumns: number
  height: 'auto' | string | number
}

/**
 * Event handler function types
 */
export type DateSelectHandler = (selectInfo: CalendarDateSelectInfo) => void
export type EventClickHandler = (clickInfo: CalendarEventClickInfo) => void
export type EventDropHandler = (dropInfo: {
  event: CalendarEvent
  oldEvent: CalendarEvent
  relatedEvents: CalendarEvent[]
}) => void
export type EventResizeHandler = (resizeInfo: {
  event: CalendarEvent
  oldEvent: CalendarEvent
  relatedEvents: CalendarEvent[]
}) => void
export type EventsSetHandler = (events: CalendarEvent[]) => void
export type EventContentRenderer = (eventInfo: EventContentArg) => React.ReactElement

/**
 * Calendar event filters
 */
export interface CalendarEventFilter {
  type?: EventType[]
  status?: EventStatus[]
  priority?: EventPriority[]
  dateRange?: {
    start: Date
    end: Date
  }
  instructor?: string
  location?: string
  traineeId?: EntityId
  assessmentId?: EntityId
  trainingProgramId?: EntityId
}

/**
 * Calendar statistics and analytics
 */
export interface CalendarStats {
  totalEvents: number
  eventsByType: Record<EventType, number>
  eventsByStatus: Record<EventStatus, number>
  eventsByPriority: Record<EventPriority, number>
  upcomingEvents: CalendarEvent[]
  overdueEvents: CalendarEvent[]
  eventsThisWeek: CalendarEvent[]
  eventsThisMonth: CalendarEvent[]
}

/**
 * Calendar export/import types
 */
export interface CalendarExportOptions {
  format: 'json' | 'ics' | 'csv'
  dateRange?: {
    start: Date
    end: Date
  }
  includeMetadata?: boolean
  filter?: CalendarEventFilter
}

/**
 * Validation rules for calendar events
 */
export interface CalendarEventValidation {
  title?: {
    required: boolean
    minLength: number
    maxLength: number
  }
  description?: {
    required: boolean
    maxLength: number
  }
  dateRange?: {
    minDuration: number // in minutes
    maxDuration: number // in minutes
    allowPast: boolean
  }
  resources?: {
    required: boolean
    maxCapacity: number
  }
}

/**
 * Calendar notification settings
 */
export interface CalendarNotificationSettings {
  enabled: boolean
  reminders: {
    beforeEvent: number // minutes before
    beforeDeadline: number // minutes before
  }
  emailNotifications: boolean
  inAppNotifications: boolean
}

/**
 * FullCalendar component props interface
 */
export interface FullCalendarProps {
  events: CalendarEvent[]
  config: Partial<CalendarConfig>
  onDateSelect?: DateSelectHandler
  onEventClick?: EventClickHandler
  onEventDrop?: EventDropHandler
  onEventResize?: EventResizeHandler
  onEventsSet?: EventsSetHandler
  renderEventContent?: EventContentRenderer
  loading?: boolean
  error?: string | null
}

/**
 * Demo application state interface (for backward compatibility)
 * @deprecated Use CalendarState instead
 */
export interface DemoAppState {
  weekendsVisible: boolean
  currentEvents: CalendarEvent[]
}

/**
 * Utility types for calendar operations
 */
export type EventFormData = Omit<CreateEventDto, 'id'> & Partial<Pick<CalendarEvent, 'id'>>
export type EventModalMode = 'create' | 'edit' | 'view'
export type CalendarAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'ADD_EVENT'; payload: CalendarEvent }
  | { type: 'UPDATE_EVENT'; payload: CalendarEvent }
  | { type: 'DELETE_EVENT'; payload: EntityId }
  | { type: 'SET_EVENTS'; payload: CalendarEvent[] }
  | { type: 'SET_SELECTED_EVENT'; payload: CalendarEvent | null }
  | { type: 'TOGGLE_WEEKENDS' }
  | { type: 'SET_CURRENT_VIEW'; payload: CalendarViewType }
  | { type: 'SET_CURRENT_DATE'; payload: Date }
