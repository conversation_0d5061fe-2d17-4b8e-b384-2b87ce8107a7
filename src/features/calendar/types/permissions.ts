import type { EntityId, ISODateString } from '../../../shared/types/common'
import type { EventType } from './calendar'
import type { UserRole } from '../../../shared/types/roles'

/**
 * Calendar-specific permission types
 */
export type CalendarPermissionType =
  | 'view_calendar'
  | 'create_events'
  | 'edit_events'
  | 'delete_events'
  | 'manage_permissions'
  | 'view_all_events'
  | 'manage_resources'
  | 'admin_calendar'
  | 'view_own_events'
  | 'edit_own_events'
  | 'delete_own_events'
  | 'view_team_events'
  | 'manage_team_events'
  | 'export_calendar'
  | 'import_calendar'
  | 'manage_calendar_settings'

/**
 * Extended user roles for calendar-specific access control
 */
export type CalendarUserRole = UserRole | 'manager' | 'instructor' | 'trainee' | 'viewer'

/**
 * Permission conditions for granular access control
 */
export interface PermissionConditions {
  eventTypes?: EventType[]
  dateRange?: {
    start: Date
    end: Date
  }
  resourceIds?: string[]
  userGroups?: string[]
  locations?: string[]
  timeRestrictions?: {
    allowedHours?: {
      start: string // HH:mm
      end: string // HH:mm
    }
    allowedDays?: number[] // 0-6 (Sunday-Saturday)
  }
}

/**
 * Calendar permission entity
 */
export interface CalendarPermission {
  id: EntityId
  userId: EntityId
  calendarId?: EntityId
  eventId?: EntityId
  permission: CalendarPermissionType
  grantedBy: EntityId
  grantedAt: ISODateString
  expiresAt?: ISODateString
  conditions?: PermissionConditions
  isActive: boolean
  metadata?: Record<string, unknown>
}

/**
 * Role-based permission mapping
 */
export interface RolePermissionMapping {
  role: CalendarUserRole
  permissions: CalendarPermissionType[]
  conditions?: PermissionConditions
}

/**
 * Permission check result
 */
export interface PermissionCheckResult {
  granted: boolean
  reason?: string
  conditions?: PermissionConditions
  expiresAt?: ISODateString
}

/**
 * Permission validation context
 */
export interface PermissionContext {
  userId: EntityId
  userRole: CalendarUserRole
  calendarId?: EntityId
  eventId?: EntityId
  action?: string
  resource?: string
  timestamp: Date
}

/**
 * Bulk permission operation
 */
export interface BulkPermissionOperation {
  userIds: EntityId[]
  permission: CalendarPermissionType
  grantedBy: EntityId
  conditions?: PermissionConditions
  expiresAt?: ISODateString
}

/**
 * Permission audit log entry
 */
export interface PermissionAuditLog {
  id: EntityId
  userId: EntityId
  targetUserId?: EntityId
  action: 'grant' | 'revoke' | 'modify' | 'check'
  permission: CalendarPermissionType
  grantedBy: EntityId
  timestamp: ISODateString
  context?: PermissionContext
  metadata?: Record<string, unknown>
}

/**
 * Permission summary for a user
 */
export interface UserPermissionSummary {
  userId: EntityId
  userRole: CalendarUserRole
  permissions: CalendarPermission[]
  effectivePermissions: Set<CalendarPermissionType>
  expiredPermissions: CalendarPermission[]
  grantedBy: Record<string, EntityId> // permission -> grantedBy
  lastUpdated: ISODateString
}

/**
 * Calendar access control configuration
 */
export interface CalendarAccessControl {
  defaultPermissions: CalendarPermissionType[]
  roleMappings: RolePermissionMapping[]
  permissionHierarchy: Record<CalendarPermissionType, CalendarPermissionType[]>
  timeBasedPermissions: {
    permission: CalendarPermissionType
    duration: number // in minutes
    autoRevoke: boolean
  }[]
  conditionalPermissions: {
    permission: CalendarPermissionType
    conditions: PermissionConditions
  }[]
}

/**
 * Permission validation error
 */
export interface PermissionValidationError {
  code: string
  message: string
  field?: string
  value?: unknown
}

/**
 * Permission service configuration
 */
export interface PermissionServiceConfig {
  enableCaching: boolean
  cacheTimeout: number // in minutes
  enableAuditLogging: boolean
  defaultPermissionDuration: number // in minutes
  maxPermissionDuration: number // in minutes
  enableConditionalPermissions: boolean
  enableTimeBasedPermissions: boolean
}

/**
 * Permission filter options
 */
export interface PermissionFilter {
  userId?: EntityId
  permission?: CalendarPermissionType
  calendarId?: EntityId
  eventId?: EntityId
  grantedBy?: EntityId
  isActive?: boolean
  expiresBefore?: ISODateString
  expiresAfter?: ISODateString
  grantedBefore?: ISODateString
  grantedAfter?: ISODateString
}

/**
 * Permission statistics
 */
export interface PermissionStatistics {
  totalPermissions: number
  activePermissions: number
  expiredPermissions: number
  permissionsByType: Record<CalendarPermissionType, number>
  permissionsByRole: Record<CalendarUserRole, number>
  recentGrants: CalendarPermission[]
  recentRevokes: CalendarPermission[]
  expiringSoon: CalendarPermission[]
}

/**
 * Permission export options
 */
export interface PermissionExportOptions {
  format: 'json' | 'csv' | 'xlsx'
  includeInactive?: boolean
  includeAuditLog?: boolean
  dateRange?: {
    start: Date
    end: Date
  }
  filter?: PermissionFilter
}

/**
 * Permission import data
 */
export interface PermissionImportData {
  permissions: Omit<CalendarPermission, 'id' | 'grantedAt'>[]
  validateOnly?: boolean
  overwriteExisting?: boolean
}

/**
 * Permission notification settings
 */
export interface PermissionNotificationSettings {
  notifyOnGrant: boolean
  notifyOnRevoke: boolean
  notifyOnExpiry: boolean
  expiryReminderDays: number
  notificationChannels: ('email' | 'in_app' | 'sms')[]
}

/**
 * Calendar permission hook return types
 */
export interface UseCalendarPermissionsReturn {
  permissions: CalendarPermission[]
  loading: boolean
  error: string | null
  canViewCalendar: () => Promise<boolean>
  canCreateEvents: () => Promise<boolean>
  canEditEvent: (eventId: string) => Promise<boolean>
  canDeleteEvent: (eventId: string) => Promise<boolean>
  canManagePermissions: () => Promise<boolean>
  canViewAllEvents: () => Promise<boolean>
  canManageResources: () => Promise<boolean>
  refreshPermissions: () => Promise<void>
  grantPermission: (
    userId: string,
    permission: CalendarPermissionType,
    conditions?: PermissionConditions
  ) => Promise<CalendarPermission>
  revokePermission: (permissionId: string) => Promise<void>
}

export interface UseEventPermissionsReturn {
  canView: (eventId: string) => Promise<boolean>
  canEdit: (eventId: string) => Promise<boolean>
  canDelete: (eventId: string) => Promise<boolean>
  canManageAttendees: (eventId: string) => Promise<boolean>
  canManageResources: (eventId: string) => Promise<boolean>
  isOwner: (eventId: string) => Promise<boolean>
  getEventPermissions: (eventId: string) => Promise<CalendarPermission[]>
}

export interface UseUserPermissionsReturn {
  userPermissions: CalendarPermission[]
  effectivePermissions: Set<CalendarPermissionType>
  permissionSummary: UserPermissionSummary | null
  loading: boolean
  error: string | null
  refreshUserPermissions: () => Promise<void>
  hasPermission: (permission: CalendarPermissionType) => boolean
  hasAnyPermission: (permissions: CalendarPermissionType[]) => boolean
  hasAllPermissions: (permissions: CalendarPermissionType[]) => boolean
}

export interface UsePermissionChecksReturn {
  checkPermission: (
    permission: CalendarPermissionType,
    context?: Partial<PermissionContext>
  ) => Promise<PermissionCheckResult>
  checkMultiplePermissions: (
    permissions: CalendarPermissionType[],
    context?: Partial<PermissionContext>
  ) => Promise<Record<CalendarPermissionType, PermissionCheckResult>>
  validatePermissionConditions: (
    conditions: PermissionConditions,
    context: PermissionContext
  ) => boolean
  getPermissionReason: (permission: CalendarPermissionType, result: PermissionCheckResult) => string
}
