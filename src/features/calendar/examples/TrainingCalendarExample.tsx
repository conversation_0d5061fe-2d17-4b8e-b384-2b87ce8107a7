import React, { useEffect } from 'react'
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '../../../shared/components/ui/card'
import { <PERSON><PERSON> } from '../../../shared/components/ui/button'
import { Badge } from '../../../shared/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from '../../../shared/components/ui/tabs'
import { CalendarDays, RefreshCw, Filter, Users, Target, Clock } from 'lucide-react'
import { useTrainingCalendar } from '../hooks/useTrainingCalendar'
import { TrainingEventsList } from '../components/TrainingEvents'
import { TrainingTimeline } from '../components/TrainingTimeline'
import FullCalendarScheduler from '../components/FullCalendarScheduler'
import type { CalendarEvent } from '../types'

/**
 * Example component demonstrating training calendar integration
 * This shows how to use the training calendar features in a real application
 */
export const TrainingCalendarExample: React.FC = () => {
  const {
    // Calendar state
    events,
    filteredEvents,
    selectedEvent,
    isLoading,

    // Training programs
    programs,
    programsLoading,
    loadPrograms,
    createProgram,
    updateProgram,

    // Training events
    trainingEvents,
    trainingEventsLoading,
    loadTrainingEvents,
    refreshTrainingEvents,

    // Training sync
    syncTrainingPrograms,
    syncing,
    lastSyncTime,

    // Training filters
    trainingFilters,
    updateTrainingFilters,
    clearTrainingFilters,

    // Training statistics
    trainingStatistics,
    loadStatistics,

    // Computed
    hasTrainingPrograms,
    hasTrainingEvents,
    filteredTrainingEvents,
  } = useTrainingCalendar()

  // Load data on component mount
  useEffect(() => {
    loadPrograms()
    loadTrainingEvents()
    loadStatistics()
  }, [loadPrograms, loadTrainingEvents, loadStatistics])

  const handleEventClick = (event: CalendarEvent) => {
    console.log('Event clicked:', event)
    // Here you could open a modal or navigate to event details
  }

  const handleProgramClick = (program: any) => {
    console.log('Program clicked:', program)
    // Here you could open a modal or navigate to program details
  }

  const handleSync = async () => {
    try {
      await syncTrainingPrograms()
      console.log('Training programs synced successfully')
    } catch (error) {
      console.error('Failed to sync training programs:', error)
    }
  }

  const handleRefresh = async () => {
    try {
      await refreshTrainingEvents()
      await loadStatistics()
      console.log('Training events refreshed successfully')
    } catch (error) {
      console.error('Failed to refresh training events:', error)
    }
  }

  // Filter training events from all events
  const calendarTrainingEvents = events.filter((event) => event.trainingProgramId)

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="flex items-center gap-2 text-3xl font-bold">
            <CalendarDays className="h-8 w-8" />
            Training Calendar
          </h1>
          <p className="mt-2 text-gray-600">
            Manage and visualize training programs, sessions, and milestones
          </p>
        </div>

        <div className="flex items-center gap-2">
          <Button onClick={handleRefresh} variant="outline" disabled={trainingEventsLoading}>
            <Clock className="mr-2 h-4 w-4" />
            Refresh
          </Button>
          <Button onClick={handleSync} disabled={syncing}>
            <RefreshCw className={`mr-2 h-4 w-4 ${syncing ? 'animate-spin' : ''}`} />
            {syncing ? 'Syncing...' : 'Sync Training'}
          </Button>
        </div>
      </div>

      {/* Statistics Overview */}
      {trainingStatistics && (
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Programs</CardTitle>
              <Target className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{trainingStatistics.totalPrograms}</div>
              <p className="text-muted-foreground text-xs">
                {trainingStatistics.activePrograms} active
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Training Sessions</CardTitle>
              <CalendarDays className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{trainingStatistics.totalSessions}</div>
              <p className="text-muted-foreground text-xs">
                {trainingStatistics.upcomingSessions} upcoming
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Milestones</CardTitle>
              <Target className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {trainingStatistics.completedMilestones + trainingStatistics.pendingMilestones}
              </div>
              <p className="text-muted-foreground text-xs">
                {trainingStatistics.completedMilestones} completed
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Last Sync</CardTitle>
              <RefreshCw className="text-muted-foreground h-4 w-4" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {lastSyncTime ? new Date(lastSyncTime).toLocaleTimeString() : 'Never'}
              </div>
              <p className="text-muted-foreground text-xs">
                {lastSyncTime ? new Date(lastSyncTime).toLocaleDateString() : 'No sync yet'}
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs defaultValue="calendar" className="space-y-4">
        <TabsList>
          <TabsTrigger value="calendar">Calendar View</TabsTrigger>
          <TabsTrigger value="timeline">Timeline View</TabsTrigger>
          <TabsTrigger value="events">Events List</TabsTrigger>
          <TabsTrigger value="programs">Programs</TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Training Calendar</CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{calendarTrainingEvents.length} training events</Badge>
                  <Button variant="outline" size="sm">
                    <Filter className="mr-2 h-4 w-4" />
                    Filter
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="py-8 text-center text-gray-500">
                <CalendarDays className="mx-auto mb-2 h-12 w-12 opacity-50" />
                <p>Calendar view integration</p>
                <p className="mt-2 text-sm">
                  The FullCalendarScheduler component manages its own state and events. Training
                  events are automatically integrated through the calendar store.
                </p>
                <div className="mt-4 rounded-lg bg-gray-100 p-4 text-left">
                  <p className="mb-2 font-medium">Training Events Found:</p>
                  <ul className="space-y-1 text-sm">
                    {calendarTrainingEvents.slice(0, 5).map((event) => (
                      <li key={event.id} className="flex items-center gap-2">
                        <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                        {event.title}
                      </li>
                    ))}
                    {calendarTrainingEvents.length > 5 && (
                      <li className="text-gray-500">
                        ... and {calendarTrainingEvents.length - 5} more
                      </li>
                    )}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="timeline" className="space-y-4">
          <TrainingTimeline
            events={calendarTrainingEvents}
            programs={programs}
            onEventClick={handleEventClick}
            onProgramClick={handleProgramClick}
          />
        </TabsContent>

        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Training Events</CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{filteredTrainingEvents.length} events</Badge>
                  {Object.keys(trainingFilters).length > 0 && (
                    <Button variant="outline" size="sm" onClick={clearTrainingFilters}>
                      Clear Filters
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <TrainingEventsList
                events={filteredTrainingEvents}
                onEventClick={handleEventClick}
                compact={false}
                groupByType={true}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="programs" className="space-y-4">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle>Training Programs</CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{programs.length} programs</Badge>
                  <Button>Create Program</Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {programs.length === 0 ? (
                <div className="py-8 text-center text-gray-500">
                  <Users className="mx-auto mb-2 h-12 w-12 opacity-50" />
                  <p>No training programs found</p>
                  <p className="text-sm">Create your first training program to get started</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {programs.map((program) => (
                    <div
                      key={program.id}
                      className="cursor-pointer rounded-lg border p-4 hover:bg-gray-50"
                      onClick={() => handleProgramClick(program)}
                    >
                      <div className="mb-2 flex items-center justify-between">
                        <h3 className="text-lg font-semibold">{program.name}</h3>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{program.status}</Badge>
                          <Badge variant="secondary">{program.durationMonths} months</Badge>
                        </div>
                      </div>

                      {program.description && (
                        <p className="mb-2 text-gray-600">{program.description}</p>
                      )}

                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        {program.instructor && (
                          <div className="flex items-center gap-1">
                            <Users className="h-4 w-4" />
                            <span>Instructor: {program.instructor}</span>
                          </div>
                        )}
                        {program.department && (
                          <div className="flex items-center gap-1">
                            <span>Department: {program.department}</span>
                          </div>
                        )}
                        {program.currentEnrollment !== undefined && program.maxCapacity && (
                          <div className="flex items-center gap-1">
                            <span>
                              Enrollment: {program.currentEnrollment}/{program.maxCapacity}
                            </span>
                          </div>
                        )}
                      </div>

                      {program.progress && (
                        <div className="mt-3">
                          <div className="mb-1 flex justify-between text-sm">
                            <span>Progress</span>
                            <span>{Math.round(program.progress.overallPercentage)}%</span>
                          </div>
                          <div className="h-2 w-full rounded-full bg-gray-200">
                            <div
                              className="h-2 rounded-full bg-blue-600"
                              style={{ width: `${program.progress.overallPercentage}%` }}
                            />
                          </div>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}

export default TrainingCalendarExample
