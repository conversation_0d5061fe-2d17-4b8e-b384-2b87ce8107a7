import React, { useState, useEffect } from 'react'
import { addDays, startOfWeek } from 'date-fns'
import { ResourceScheduling } from '../components/ResourceScheduling'
import { ResourceSchedulingService } from '../services/resourceSchedulingService'
import type {
  Resource,
  ResourceBooking,
  ResourceSchedulingRequest,
  ResourceAnalytics,
} from '../types/resources'

/**
 * Comprehensive Resource Scheduling Example
 * Demonstrates all features of the resource scheduling system
 */
export const ResourceSchedulingExample: React.FC = () => {
  const [resources, setResources] = useState<Resource[]>([])
  const [bookings, setBookings] = useState<ResourceBooking[]>([])
  const [analytics, setAnalytics] = useState<ResourceAnalytics | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Initialize demo data and load initial state
  useEffect(() => {
    const initializeExample = async () => {
      try {
        setLoading(true)
        setError(null)

        // Initialize demo data
        await ResourceSchedulingService.initializeDemoData()

        // Load resources
        const loadedResources = await ResourceSchedulingService.getResources()
        setResources(loadedResources)

        // Load bookings for all resources
        const allBookings: ResourceBooking[] = []
        for (const resource of loadedResources) {
          const resourceBookings = await ResourceSchedulingService.getResourceBookings(resource.id)
          allBookings.push(...resourceBookings)
        }
        setBookings(allBookings)

        // Load analytics
        const dateRange = {
          start: startOfWeek(new Date()),
          end: addDays(startOfWeek(new Date()), 30),
        }
        const loadedAnalytics = await ResourceSchedulingService.getResourceAnalytics(dateRange)
        setAnalytics(loadedAnalytics)

        // Create some example bookings
        await createExampleBookings(loadedResources)
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to initialize resource scheduling example'
        )
      } finally {
        setLoading(false)
      }
    }

    initializeExample()
  }, [])

  // Create example bookings to demonstrate the system
  const createExampleBookings = async (resources: Resource[]) => {
    try {
      const today = new Date()
      const tomorrow = addDays(today, 1)

      // Example booking requests
      const exampleRequests: ResourceSchedulingRequest[] = [
        {
          resourceId: resources[0]?.id || '',
          title: 'Team Meeting',
          startTime: new Date(today.setHours(10, 0, 0, 0)),
          endTime: new Date(today.setHours(12, 0, 0, 0)),
          priority: 'medium',
          requestedBy: '<EMAIL>',
          notes: 'Weekly team sync meeting',
        },
        {
          resourceId: resources[1]?.id || '',
          title: 'JavaScript Training Session',
          startTime: new Date(tomorrow.setHours(9, 0, 0, 0)),
          endTime: new Date(tomorrow.setHours(17, 0, 0, 0)),
          priority: 'high',
          requestedBy: '<EMAIL>',
          notes: 'Advanced JavaScript workshop',
        },
        {
          resourceId: resources[2]?.id || '',
          title: 'React Development',
          startTime: new Date(today.setHours(14, 0, 0, 0)),
          endTime: new Date(today.setHours(16, 0, 0, 0)),
          priority: 'high',
          requestedBy: '<EMAIL>',
          notes: 'React component development session',
        },
      ]

      // Create bookings
      for (const request of exampleRequests) {
        if (request.resourceId) {
          const result = await ResourceSchedulingService.bookResource(request)
          if (result.success && result.booking) {
            setBookings((prev) => [...prev, result.booking!])
          }
        }
      }
    } catch (err) {
      console.error('Failed to create example bookings:', err)
    }
  }

  if (loading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"></div>
          <p className="text-gray-600">Loading Resource Scheduling Example...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="max-w-md rounded-lg border border-red-200 bg-red-50 p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800">Error Loading Example</h3>
              <div className="mt-2 text-sm text-red-700">{error}</div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
          <div className="py-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">Resource Scheduling Example</h1>
                <p className="mt-2 text-sm text-gray-600">
                  Comprehensive demonstration of the GT-EGA resource scheduling system
                </p>
              </div>
              <div className="flex items-center space-x-4">
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">Resources</div>
                  <div className="text-2xl font-bold text-blue-600">{resources.length}</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">Bookings</div>
                  <div className="text-2xl font-bold text-green-600">{bookings.length}</div>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">Avg Utilization</div>
                  <div className="text-2xl font-bold text-purple-600">
                    {analytics?.averageUtilizationRate.toFixed(1) || 0}%
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        {/* Feature Overview */}
        <div className="mb-8 rounded-lg bg-white p-6 shadow">
          <h2 className="mb-4 text-xl font-semibold text-gray-900">Features Demonstrated</h2>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                  <svg className="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                    <path
                      fillRule="evenodd"
                      d="M4 5a2 2 0 012-2 1 1 0 000 2H6a2 2 0 100 4h2a2 2 0 100-4h-.5a1 1 0 000-2H8a2 2 0 012-2h2a2 2 0 012 2v9a2 2 0 01-2 2H6a2 2 0 01-2-2V5z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">Resource Management</h3>
                <p className="text-sm text-gray-600">
                  Create, update, and manage resources with metadata
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                  <svg className="h-4 w-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">Availability Checking</h3>
                <p className="text-sm text-gray-600">
                  Real-time resource availability verification
                </p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-yellow-100">
                  <svg className="h-4 w-4 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">Booking System</h3>
                <p className="text-sm text-gray-600">Resource booking with conflict detection</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                  <svg className="h-4 w-4 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">Conflict Resolution</h3>
                <p className="text-sm text-gray-600">Detect and resolve scheduling conflicts</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100">
                  <svg className="h-4 w-4 text-purple-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z" />
                  </svg>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">Utilization Analytics</h3>
                <p className="text-sm text-gray-600">Track resource usage and optimization</p>
              </div>
            </div>

            <div className="flex items-start space-x-3">
              <div className="flex-shrink-0">
                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-indigo-100">
                  <svg className="h-4 w-4 text-indigo-600" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
                  </svg>
                </div>
              </div>
              <div>
                <h3 className="text-sm font-medium text-gray-900">Multi-Resource Types</h3>
                <p className="text-sm text-gray-600">
                  Support for rooms, equipment, instructors, and more
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Resource Scheduling Component */}
        <div className="rounded-lg bg-white shadow">
          <div className="border-b border-gray-200 px-6 py-4">
            <h2 className="text-lg font-semibold text-gray-900">Interactive Resource Scheduling</h2>
            <p className="mt-1 text-sm text-gray-600">
              Try the full resource scheduling interface below. Click on different tabs to explore
              all features.
            </p>
          </div>
          <div className="p-6">
            <ResourceScheduling />
          </div>
        </div>

        {/* Example Data Summary */}
        <div className="mt-8 grid grid-cols-1 gap-8 lg:grid-cols-2">
          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="mb-4 text-lg font-semibold text-gray-900">Example Resources</h3>
            <div className="space-y-3">
              {resources.slice(0, 3).map((resource) => (
                <div
                  key={resource.id}
                  className="flex items-center justify-between rounded-lg bg-gray-50 p-3"
                >
                  <div>
                    <div className="font-medium text-gray-900">{resource.name}</div>
                    <div className="text-sm text-gray-600">
                      {resource.type} • {resource.location}
                    </div>
                  </div>
                  <span
                    className={`rounded-full px-2 py-1 text-xs font-medium ${
                      resource.status === 'available'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    {resource.status}
                  </span>
                </div>
              ))}
            </div>
          </div>

          <div className="rounded-lg bg-white p-6 shadow">
            <h3 className="mb-4 text-lg font-semibold text-gray-900">Recent Bookings</h3>
            <div className="space-y-3">
              {bookings.slice(0, 3).map((booking) => (
                <div
                  key={booking.id}
                  className="flex items-center justify-between rounded-lg bg-gray-50 p-3"
                >
                  <div>
                    <div className="font-medium text-gray-900">{booking.title}</div>
                    <div className="text-sm text-gray-600">
                      {new Date(booking.startTime).toLocaleDateString()} • {booking.priority}
                    </div>
                  </div>
                  <span
                    className={`rounded-full px-2 py-1 text-xs font-medium ${
                      booking.status === 'confirmed'
                        ? 'bg-green-100 text-green-800'
                        : 'bg-yellow-100 text-yellow-800'
                    }`}
                  >
                    {booking.status}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ResourceSchedulingExample
