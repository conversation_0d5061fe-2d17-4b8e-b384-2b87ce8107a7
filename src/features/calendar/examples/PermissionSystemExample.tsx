import React, { useState } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { But<PERSON> } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/shared/components/ui/tabs'
import {
  Calendar,
  Shield,
  Users,
  Settings,
  AlertCircle,
  CheckCircle,
  Lock,
  Unlock,
} from 'lucide-react'

// Import permission hooks and components
import {
  useCalendarPermissions,
  useEventPermissions,
  useUserPermissions,
  usePermissionChecks,
  usePermissionGuard,
} from '../hooks/usePermissions'
import {
  PermissionWrapper,
  PermissionIndicator,
  CalendarPermissionManager,
  UserPermissionSummaryComponent,
  PermissionChecker,
  RoleBasedAccessControl,
} from '../components/Permissions'
import { CalendarPermissionService } from '../services/permissionService'

import type {
  CalendarPermissionType,
  PermissionConditions,
  CalendarUserRole,
} from '../types/permissions'

/**
 * Comprehensive example demonstrating the calendar permission system
 */
export const PermissionSystemExample: React.FC = () => {
  const [selectedUserId, setSelectedUserId] = useState<string>('demo-user-123')
  const [selectedEventId, setSelectedEventId] = useState<string>('demo-event-456')

  return (
    <div className="space-y-6 p-6">
      <div className="text-center">
        <h1 className="mb-2 text-3xl font-bold">Calendar Permission System</h1>
        <p className="text-muted-foreground">
          Comprehensive permission-based access control for calendar features
        </p>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="hooks">Hooks</TabsTrigger>
          <TabsTrigger value="components">Components</TabsTrigger>
          <TabsTrigger value="service">Service</TabsTrigger>
          <TabsTrigger value="examples">Examples</TabsTrigger>
          <TabsTrigger value="management">Management</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <OverviewExample />
        </TabsContent>

        <TabsContent value="hooks" className="space-y-6">
          <HooksExample userId={selectedUserId} eventId={selectedEventId} />
        </TabsContent>

        <TabsContent value="components" className="space-y-6">
          <ComponentsExample />
        </TabsContent>

        <TabsContent value="service" className="space-y-6">
          <ServiceExample />
        </TabsContent>

        <TabsContent value="examples" className="space-y-6">
          <PracticalExamples />
        </TabsContent>

        <TabsContent value="management" className="space-y-6">
          <ManagementExample />
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * Overview example showing permission system concepts
 */
const OverviewExample: React.FC = () => {
  const { hasPermission, loading } = useUserPermissions()

  const permissionTypes: CalendarPermissionType[] = [
    'view_calendar',
    'create_events',
    'edit_events',
    'delete_events',
    'manage_permissions',
    'view_all_events',
    'manage_resources',
    'admin_calendar',
  ]

  const userRoles: CalendarUserRole[] = ['admin', 'manager', 'instructor', 'trainee', 'viewer']

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Permission Types
          </CardTitle>
          <CardDescription>Available permissions for calendar access control</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {permissionTypes.map((permission) => (
              <div key={permission} className="flex items-center justify-between">
                <span className="text-sm">
                  {permission.replace(/_/g, ' ').replace(/\b\w/g, (l) => l.toUpperCase())}
                </span>
                <PermissionIndicator
                  permission={permission}
                  granted={hasPermission(permission)}
                  size="sm"
                />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            User Roles
          </CardTitle>
          <CardDescription>Role-based permission hierarchy</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {userRoles.map((role) => (
              <div key={role} className="flex items-center justify-between">
                <span className="text-sm capitalize">{role}</span>
                <Badge variant={role === 'admin' ? 'default' : 'secondary'}>
                  {CalendarPermissionService.getDefaultRolePermissions()[role]?.length || 0}{' '}
                  permissions
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Hooks example demonstrating permission hooks usage
 */
const HooksExample: React.FC<{ userId: string; eventId: string }> = ({ userId, eventId }) => {
  // Calendar permissions hook
  const {
    canViewCalendar,
    canCreateEvents,
    canEditEvent,
    canDeleteEvent,
    canManagePermissions,
    loading: calendarLoading,
  } = useCalendarPermissions()

  // Event permissions hook
  const { canView, canEdit, canDelete, isOwner } = useEventPermissions()

  // User permissions hook
  const {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    permissionSummary,
    loading: userLoading,
  } = useUserPermissions(userId)

  // Permission checks hook
  const {
    checkPermission,
    checkMultiplePermissions,
    validatePermissionConditions,
    getPermissionReason,
  } = usePermissionChecks()

  // Permission guard hook
  const { hasPermission: canManageSettings } = usePermissionGuard('manage_calendar_settings')

  const handlePermissionCheck = async () => {
    const result = await checkPermission('create_events')
    console.log('Permission check result:', result)
  }

  const handleMultipleCheck = async () => {
    const results = await checkMultiplePermissions([
      'view_calendar',
      'create_events',
      'edit_events',
    ])
    console.log('Multiple permissions check:', results)
  }

  const handleValidateConditions = () => {
    const conditions: PermissionConditions = {
      eventTypes: ['training_session', 'assessment'],
      dateRange: {
        start: new Date(),
        end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
      },
    }

    const context = {
      userId,
      userRole: 'instructor' as CalendarUserRole,
      timestamp: new Date(),
    }

    const isValid = validatePermissionConditions(conditions, context)
    console.log('Conditions validation:', isValid)
  }

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Calendar Permissions Hook</CardTitle>
            <CardDescription>
              useCalendarPermissions() for calendar-wide permissions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button onClick={() => canViewCalendar()} size="sm">
                Check View Calendar
              </Button>
              <Button onClick={() => canCreateEvents()} size="sm" variant="outline">
                Check Create Events
              </Button>
              <Button onClick={() => canEditEvent(eventId)} size="sm" variant="outline">
                Check Edit Event
              </Button>
              <Button onClick={() => canDeleteEvent(eventId)} size="sm" variant="outline">
                Check Delete Event
              </Button>
              <Button onClick={() => canManagePermissions()} size="sm" variant="outline">
                Check Manage Permissions
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Event Permissions Hook</CardTitle>
            <CardDescription>useEventPermissions() for event-specific permissions</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button onClick={() => canView(eventId)} size="sm">
                Check View Event
              </Button>
              <Button onClick={() => canEdit(eventId)} size="sm" variant="outline">
                Check Edit Event
              </Button>
              <Button onClick={() => canDelete(eventId)} size="sm" variant="outline">
                Check Delete Event
              </Button>
              <Button onClick={() => isOwner(eventId)} size="sm" variant="outline">
                Check Is Owner
              </Button>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>User Permissions Hook</CardTitle>
            <CardDescription>useUserPermissions() for user permission management</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="text-sm">
                <strong>Effective Permissions:</strong>{' '}
                {permissionSummary?.effectivePermissions.size || 0}
              </div>
              <div className="text-sm">
                <strong>Has Create Events:</strong> {hasPermission('create_events') ? 'Yes' : 'No'}
              </div>
              <div className="text-sm">
                <strong>Has Any Admin Perm:</strong>{' '}
                {hasAnyPermission(['admin_calendar', 'manage_permissions']) ? 'Yes' : 'No'}
              </div>
              <div className="text-sm">
                <strong>Has Basic Perms:</strong>{' '}
                {hasAllPermissions(['view_calendar', 'view_own_events']) ? 'Yes' : 'No'}
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Permission Checks Hook</CardTitle>
            <CardDescription>
              usePermissionChecks() for advanced permission validation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Button onClick={handlePermissionCheck} size="sm">
                Check Single Permission
              </Button>
              <Button onClick={handleMultipleCheck} size="sm" variant="outline">
                Check Multiple Permissions
              </Button>
              <Button onClick={handleValidateConditions} size="sm" variant="outline">
                Validate Conditions
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Permission Guard Hook</CardTitle>
          <CardDescription>usePermissionGuard() for component-level protection</CardDescription>
        </CardHeader>
        <CardContent>
          <PermissionWrapper permission="manage_calendar_settings">
            <div className="rounded-lg border border-green-200 bg-green-50 p-4">
              <div className="flex items-center gap-2">
                <Unlock className="h-4 w-4 text-green-600" />
                <span className="text-green-800">Settings management is available</span>
              </div>
            </div>
          </PermissionWrapper>

          <PermissionWrapper
            permission="admin_calendar"
            fallback={
              <div className="rounded-lg border border-red-200 bg-red-50 p-4">
                <div className="flex items-center gap-2">
                  <Lock className="h-4 w-4 text-red-600" />
                  <span className="text-red-800">Admin access required</span>
                </div>
              </div>
            }
          >
            <div className="rounded-lg border border-green-200 bg-green-50 p-4">
              <div className="flex items-center gap-2">
                <Unlock className="h-4 w-4 text-green-600" />
                <span className="text-green-800">Admin access granted</span>
              </div>
            </div>
          </PermissionWrapper>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Components example showing permission components
 */
const ComponentsExample: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Permission Indicator Component</CardTitle>
          <CardDescription>Visual indicators for permission status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <PermissionIndicator
              permission="view_calendar"
              granted={true}
              reason="Role-based permission"
            />
            <PermissionIndicator
              permission="admin_calendar"
              granted={false}
              reason="Insufficient role"
            />
            <PermissionIndicator
              permission="create_events"
              granted={true}
              reason="Explicit permission granted"
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Role-Based Access Control</CardTitle>
          <CardDescription>Component protection based on user roles</CardDescription>
        </CardHeader>
        <CardContent>
          <RoleBasedAccessControl requiredRoles={['admin', 'manager']}>
            <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-blue-600" />
                <span className="text-blue-800">Admin/Manager content</span>
              </div>
            </div>
          </RoleBasedAccessControl>

          <RoleBasedAccessControl
            requiredRoles={['admin']}
            fallback={
              <div className="rounded-lg border border-gray-200 bg-gray-50 p-4">
                <div className="flex items-center gap-2">
                  <AlertCircle className="h-4 w-4 text-gray-600" />
                  <span className="text-gray-800">Admin only content - access denied</span>
                </div>
              </div>
            }
          >
            <div className="rounded-lg border border-blue-200 bg-blue-50 p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-blue-600" />
                <span className="text-blue-800">Admin only content - access granted</span>
              </div>
            </div>
          </RoleBasedAccessControl>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        <PermissionChecker />
        <UserPermissionSummaryComponent userId="demo-user-123" />
      </div>
    </div>
  )
}

/**
 * Service example showing direct service usage
 */
const ServiceExample: React.FC = () => {
  const [results, setResults] = useState<string[]>([])

  const addResult = (message: string) => {
    setResults((prev) => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const handleServiceExamples = async () => {
    try {
      // Check permission
      const checkResult = await CalendarPermissionService.checkPermission(
        'demo-user-123',
        'view_calendar'
      )
      addResult(
        `Permission check: ${checkResult.granted ? 'Granted' : 'Denied'} - ${checkResult.reason}`
      )

      // Get user permissions
      const permissions = await CalendarPermissionService.getUserPermissions('demo-user-123')
      addResult(`User permissions: ${permissions.length} permissions found`)

      // Get permission summary
      const summary = await CalendarPermissionService.getUserPermissionSummary('demo-user-123')
      addResult(`Permission summary: ${summary.effectivePermissions.size} effective permissions`)

      // Check event modification
      const canEdit = await CalendarPermissionService.canModifyEvent(
        'demo-user-123',
        'demo-event-456',
        'edit'
      )
      addResult(`Can edit event: ${canEdit}`)

      // Grant permission
      const newPermission = await CalendarPermissionService.grantPermission(
        'demo-user-123',
        'create_events',
        'admin-user-789'
      )
      addResult(`Granted permission: ${newPermission.id}`)

      // Validate conditions
      const conditions: PermissionConditions = {
        eventTypes: ['training_session'],
        dateRange: {
          start: new Date(),
          end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
        },
      }

      const context = {
        userId: 'demo-user-123',
        userRole: 'instructor' as CalendarUserRole,
        timestamp: new Date(),
      }

      const isValid = CalendarPermissionService.validatePermissionConditions(conditions, context)
      addResult(`Conditions valid: ${isValid}`)
    } catch (error) {
      addResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Direct Service Usage</CardTitle>
          <CardDescription>Using CalendarPermissionService directly</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Button onClick={handleServiceExamples}>Run Service Examples</Button>

            {results.length > 0 && (
              <div className="mt-4">
                <h4 className="mb-2 font-medium">Results:</h4>
                <div className="max-h-64 overflow-y-auto rounded-lg border bg-gray-50 p-3">
                  {results.map((result, index) => (
                    <div key={index} className="font-mono text-sm">
                      {result}
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Practical examples showing real-world usage scenarios
 */
const PracticalExamples: React.FC = () => {
  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Event Creation with Permission Check</CardTitle>
          <CardDescription>Example of protecting event creation functionality</CardDescription>
        </CardHeader>
        <CardContent>
          <EventCreationExample />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Event Editing with Ownership Check</CardTitle>
          <CardDescription>Example of protecting event editing based on ownership</CardDescription>
        </CardHeader>
        <CardContent>
          <EventEditingExample />
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Conditional Permissions</CardTitle>
          <CardDescription>Example of time-based and event-type restrictions</CardDescription>
        </CardHeader>
        <CardContent>
          <ConditionalPermissionsExample />
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Management example showing permission management interface
 */
const ManagementExample: React.FC = () => {
  return (
    <div className="space-y-6">
      <CalendarPermissionManager />
    </div>
  )
}

/**
 * Example component for event creation with permission check
 */
const EventCreationExample: React.FC = () => {
  const { canCreateEvents } = useCalendarPermissions()

  return (
    <PermissionWrapper permission="create_events">
      <div className="space-y-4">
        <h4 className="font-medium">Create New Event</h4>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <input placeholder="Event title" className="rounded-md border px-3 py-2" />
          <input placeholder="Event location" className="rounded-md border px-3 py-2" />
          <textarea
            placeholder="Event description"
            className="rounded-md border px-3 py-2 md:col-span-2"
            rows={3}
          />
        </div>
        <Button>Create Event</Button>
      </div>
    </PermissionWrapper>
  )
}

/**
 * Example component for event editing with ownership check
 */
const EventEditingExample: React.FC = () => {
  const { canEdit, canDelete, isOwner } = useEventPermissions()

  return (
    <div className="space-y-4">
      <h4 className="font-medium">Event Actions</h4>
      <div className="flex gap-2">
        <Button variant="outline" size="sm" onClick={() => canEdit('demo-event-456')}>
          Edit Event
        </Button>
        <Button variant="outline" size="sm" onClick={() => canDelete('demo-event-456')}>
          Delete Event
        </Button>
        <Button variant="outline" size="sm" onClick={() => isOwner('demo-event-456')}>
          Check Ownership
        </Button>
      </div>
    </div>
  )
}

/**
 * Example component for conditional permissions
 */
const ConditionalPermissionsExample: React.FC = () => {
  const { validatePermissionConditions } = usePermissionChecks()

  const handleCheckConditions = () => {
    const conditions: PermissionConditions = {
      eventTypes: ['training_session'],
      timeRestrictions: {
        allowedHours: { start: '09:00', end: '17:00' },
        allowedDays: [1, 2, 3, 4, 5], // Monday to Friday
      },
    }

    const context = {
      userId: 'demo-user-123',
      userRole: 'instructor' as CalendarUserRole,
      timestamp: new Date(),
    }

    const isValid = validatePermissionConditions(conditions, context)
    alert(`Conditions ${isValid ? 'met' : 'not met'}`)
  }

  return (
    <div className="space-y-4">
      <h4 className="font-medium">Conditional Permission Check</h4>
      <div className="text-muted-foreground mb-4 text-sm">
        Checks if user can create training sessions during business hours on weekdays
      </div>
      <Button onClick={handleCheckConditions}>Check Business Hours Condition</Button>
    </div>
  )
}

export default PermissionSystemExample
