import React, { useState, useEffect } from 'react'
import { Card } from '../../../shared/components/ui/card'
import { Button } from '../../../shared/components/ui/button'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '../../../shared/components/ui/tabs'
import { Badge } from '../../../shared/components/ui/badge'
import {
  Calendar,
  Clock,
  AlertTriangle,
  CheckCircle,
  Users,
  RefreshCw,
  Plus,
  Eye,
  Settings,
} from 'lucide-react'

// Import assessment calendar components
import {
  AssessmentEvent,
  AssessmentEventList,
  AssessmentEventCard,
} from '../components/AssessmentEvents'
import { AssessmentTimeline, AssessmentTimelineItem } from '../components/AssessmentTimeline'
import { AssessmentDeadlineTracker } from '../components/AssessmentDeadlineTracker'
import { AssessmentReviewScheduler } from '../components/AssessmentReviewScheduler'

// Import assessment calendar hooks
import { useAssessmentCalendar } from '../hooks/useAssessmentCalendar'

// Import assessment integration service
import { AssessmentIntegrationService } from '../services/assessmentIntegrationService'

// Import types
import type { CalendarEvent } from '../types'
import type { Assessment } from '../../../shared/types/assessment'

/**
 * Assessment Calendar Example Component
 * Demonstrates the complete integration of assessments with the calendar system
 */
export const AssessmentCalendarExample: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview')
  const [selectedEvent, setSelectedEvent] = useState<CalendarEvent | null>(null)
  const [isRefreshing, setIsRefreshing] = useState(false)

  // Use the assessment calendar hook for all assessment-related functionality
  const {
    // Assessment data
    assessments,
    assessmentsLoading,
    assessmentsError,

    // Assessment events
    assessmentEvents,
    eventsLoading,
    eventsError,

    // Sync functionality
    syncAssessmentEvents,
    syncing,
    lastSyncTime,

    // Statistics
    stats,
    statsLoading,

    // Actions
    loadAssessments,
    loadAssessmentEvents,
    loadStats,

    // Error handling
    clearAssessmentsError,
    clearEventsError,
    clearStatsError,
  } = useAssessmentCalendar()

  // Load data on component mount
  useEffect(() => {
    loadAssessments()
    loadAssessmentEvents()
    loadStats()
  }, [loadAssessments, loadAssessmentEvents, loadStats])

  // Handle refresh
  const handleRefresh = async () => {
    setIsRefreshing(true)
    try {
      await syncAssessmentEvents()
      await loadAssessments()
      await loadAssessmentEvents()
      await loadStats()
    } finally {
      setIsRefreshing(false)
    }
  }

  // Handle event interactions
  const handleEventClick = (event: CalendarEvent) => {
    setSelectedEvent(event)
    setActiveTab('details')
  }

  const handleEventEdit = (event: CalendarEvent) => {
    console.log('Edit event:', event)
    // In a real implementation, this would open an edit dialog
  }

  const handleEventDelete = (eventId: string) => {
    console.log('Delete event:', eventId)
    // In a real implementation, this would show a confirmation dialog
  }

  // Handle review scheduling
  const handleScheduleReview = (assessmentId: string, reviewData: any) => {
    console.log('Schedule review for assessment:', assessmentId, reviewData)
    // In a real implementation, this would call the assessment service
  }

  // Get deadline events for the tracker
  const deadlineEvents = assessmentEvents.filter((event) => {
    const metadata = event.metadata as any
    return metadata.type === 'deadline'
  })

  // Get review events for the scheduler
  const reviewEvents = assessmentEvents.filter((event) => {
    const metadata = event.metadata as any
    return metadata.type === 'review'
  })

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Assessment Calendar Integration</h1>
          <p className="mt-2 text-gray-600">
            Complete assessment management and scheduling system integrated with GT-EGA calendar
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleRefresh} disabled={isRefreshing || syncing}>
            <RefreshCw
              className={`mr-2 h-4 w-4 ${isRefreshing || syncing ? 'animate-spin' : ''}`}
            />
            Refresh
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            New Assessment
          </Button>
        </div>
      </div>

      {/* Status Bar */}
      <Card className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 rounded-full bg-blue-500" />
              <span className="text-sm">
                <strong>{assessments.length}</strong> Assessments
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 rounded-full bg-green-500" />
              <span className="text-sm">
                <strong>{assessmentEvents.length}</strong> Calendar Events
              </span>
            </div>
            {lastSyncTime && (
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-600">
                  Last sync: {new Date(lastSyncTime).toLocaleString()}
                </span>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-2">
            {syncing && (
              <Badge variant="secondary" className="animate-pulse">
                Syncing...
              </Badge>
            )}
            <Button variant="ghost" size="sm">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </Card>

      {/* Error Display */}
      {(assessmentsError || eventsError) && (
        <Card className="border-red-200 bg-red-50 p-4">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-500" />
            <div>
              <p className="font-medium text-red-800">Error loading data</p>
              <p className="text-sm text-red-600">{assessmentsError || eventsError}</p>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                clearAssessmentsError()
                clearEventsError()
              }}
            >
              Dismiss
            </Button>
          </div>
        </Card>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="deadlines">Deadlines</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          <TabsTrigger value="events">Events</TabsTrigger>
          <TabsTrigger value="details">Details</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Statistics Cards */}
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Assessments</p>
                  <p className="text-2xl font-bold">{assessments.length}</p>
                </div>
                <Users className="h-8 w-8 text-blue-500" />
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Pending Reviews</p>
                  <p className="text-2xl font-bold">
                    {assessments.filter((a) => a.status === 'in_progress').length}
                  </p>
                </div>
                <Clock className="h-8 w-8 text-yellow-500" />
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Completed</p>
                  <p className="text-2xl font-bold">
                    {assessments.filter((a) => a.status === 'completed').length}
                  </p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-500" />
              </div>
            </Card>

            <Card className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Calendar Events</p>
                  <p className="text-2xl font-bold">{assessmentEvents.length}</p>
                </div>
                <Calendar className="h-8 w-8 text-purple-500" />
              </div>
            </Card>
          </div>

          {/* Recent Assessment Events */}
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <Card className="p-6">
              <h3 className="mb-4 text-lg font-semibold">Upcoming Assessment Events</h3>
              <AssessmentEventList
                events={assessmentEvents.slice(0, 5)}
                onEventClick={handleEventClick}
                onEventEdit={handleEventEdit}
                compact={true}
                groupBy="date"
              />
            </Card>

            <Card className="p-6">
              <h3 className="mb-4 text-lg font-semibold">Assessment Status Breakdown</h3>
              <div className="space-y-3">
                {['pending', 'in_progress', 'completed', 'overdue'].map((status) => {
                  const count = assessments.filter((a) => a.status === status).length
                  const percentage = assessments.length > 0 ? (count / assessments.length) * 100 : 0

                  return (
                    <div key={status} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{status.replace('_', ' ')}</Badge>
                        <span className="text-sm text-gray-600">{count}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="h-2 w-20 rounded-full bg-gray-200">
                          <div
                            className="h-2 rounded-full bg-blue-500"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium">{percentage.toFixed(1)}%</span>
                      </div>
                    </div>
                  )
                })}
              </div>
            </Card>
          </div>
        </TabsContent>

        {/* Timeline Tab */}
        <TabsContent value="timeline">
          <AssessmentTimeline
            events={assessmentEvents}
            onEventClick={handleEventClick}
            onEventEdit={handleEventEdit}
            onEventDelete={handleEventDelete}
          />
        </TabsContent>

        {/* Deadlines Tab */}
        <TabsContent value="deadlines">
          <AssessmentDeadlineTracker
            events={deadlineEvents}
            onEventClick={handleEventClick}
            onEventEdit={handleEventEdit}
          />
        </TabsContent>

        {/* Reviews Tab */}
        <TabsContent value="reviews">
          <AssessmentReviewScheduler
            assessments={assessments}
            reviewEvents={reviewEvents}
            onScheduleReview={handleScheduleReview}
            onEditReview={handleEditReview}
            onDeleteReview={handleDeleteReview}
          />
        </TabsContent>

        {/* Events Tab */}
        <TabsContent value="events">
          <Card className="p-6">
            <div className="mb-4 flex items-center justify-between">
              <h3 className="text-lg font-semibold">All Assessment Events</h3>
              <div className="flex items-center space-x-2">
                <Badge variant="outline">{assessmentEvents.length} events</Badge>
                <Button variant="outline" size="sm">
                  <Eye className="mr-2 h-4 w-4" />
                  View Filters
                </Button>
              </div>
            </div>

            <AssessmentEventList
              events={assessmentEvents}
              onEventClick={handleEventClick}
              onEventEdit={handleEventEdit}
              onEventDelete={handleEventDelete}
              groupBy="type"
            />
          </Card>
        </TabsContent>

        {/* Details Tab */}
        <TabsContent value="details">
          <Card className="p-6">
            {selectedEvent ? (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold">Event Details</h3>
                  <Button variant="outline" size="sm" onClick={() => setSelectedEvent(null)}>
                    Close
                  </Button>
                </div>

                <AssessmentEvent
                  event={selectedEvent}
                  onEdit={handleEventEdit}
                  onDelete={handleEventDelete}
                />
              </div>
            ) : (
              <div className="py-8 text-center text-gray-500">
                <Eye className="mx-auto mb-4 h-12 w-12 opacity-50" />
                <p>Select an event to view details</p>
              </div>
            )}
          </Card>
        </TabsContent>
      </Tabs>

      {/* Loading Overlay */}
      {(assessmentsLoading || eventsLoading) && (
        <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
          <div className="rounded-lg bg-white p-6 shadow-lg">
            <div className="flex items-center space-x-3">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span>Loading assessment data...</span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

/**
 * Example usage component that shows how to integrate the assessment calendar
 */
export const AssessmentCalendarIntegrationExample: React.FC = () => {
  return (
    <div className="space-y-6">
      <div className="space-y-2 text-center">
        <h1 className="text-2xl font-bold">GT-EGA Assessment Calendar Integration</h1>
        <p className="text-gray-600">
          This example demonstrates the complete integration of GT-EGA's assessment system with the
          calendar module.
        </p>
      </div>

      <AssessmentCalendarExample />

      <div className="mt-8 rounded-lg bg-gray-50 p-6">
        <h3 className="mb-4 text-lg font-semibold">Integration Features Demonstrated:</h3>
        <ul className="space-y-2 text-sm">
          <li>
            • <strong>Assessment Integration Service:</strong> Transforms assessment data to
            calendar events
          </li>
          <li>
            • <strong>Assessment Calendar Hooks:</strong> Provides state management and data
            fetching
          </li>
          <li>
            • <strong>Assessment Event Components:</strong> Displays assessment-specific calendar
            events
          </li>
          <li>
            • <strong>Timeline View:</strong> Shows assessment events in chronological order
          </li>
          <li>
            • <strong>Deadline Tracking:</strong> Monitors upcoming and overdue assessment deadlines
          </li>
          <li>
            • <strong>Review Scheduling:</strong> Manages assessment review sessions
          </li>
          <li>
            • <strong>Real-time Sync:</strong> Keeps calendar events synchronized with assessment
            data
          </li>
          <li>
            • <strong>Status Visualization:</strong> Shows assessment status and progress
          </li>
        </ul>
      </div>
    </div>
  )
}

export default AssessmentCalendarExample
