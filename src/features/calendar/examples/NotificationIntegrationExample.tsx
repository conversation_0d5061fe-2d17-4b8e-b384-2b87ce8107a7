import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/shared/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { CalendarNotificationService } from '../services/notificationService'
import { CalendarNotificationIntegration } from '../services/calendarNotificationIntegration'
import {
  useCalendarNotifications,
  useEventReminders,
  useNotificationPreferences,
} from '../hooks/useNotifications'
import { Notifications } from '../components/Notifications'
import { Calendar, Bell, Clock, Mail, Settings } from 'lucide-react'
import type {
  CalendarEvent,
  CreateEventDto,
  CalendarNotification,
  EventReminder,
  ReminderType,
} from '../types'

/**
 * Notification Integration Example
 * Demonstrates the complete calendar notification system integration
 */
export function NotificationIntegrationExample() {
  const [events, setEvents] = useState<CalendarEvent[]>([])
  const [notifications, setNotifications] = useState<CalendarNotification[]>([])
  const [reminders, setReminders] = useState<EventReminder[]>([])
  const [activeDemo, setActiveDemo] = useState<
    'create' | 'reminders' | 'preferences' | 'integration'
  >('create')

  // Use notification hooks
  const { scheduleReminder, notifyEventChange, markAsRead, deleteNotification } =
    useCalendarNotifications()

  const { getEventReminders, scheduleMultipleReminders, cancelAllReminders } = useEventReminders()

  const { preferences, updatePreferences, toggleNotificationType } = useNotificationPreferences()

  // Initialize with sample data
  useEffect(() => {
    const sampleEvents: CalendarEvent[] = [
      {
        id: 'event-1',
        title: 'Team Meeting',
        start: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
        end: new Date(Date.now() + 3 * 60 * 60 * 1000), // 3 hours from now
        allDay: false,
        type: 'meeting',
        status: 'scheduled',
        priority: 'medium',
        description: 'Weekly team sync meeting',
        location: 'Conference Room A',
        instructor: 'John Doe',
        traineeIds: ['user-1', 'user-2'],
        metadata: {},
      },
      {
        id: 'event-2',
        title: 'Training Session',
        start: new Date(Date.now() + 24 * 60 * 60 * 1000), // 1 day from now
        end: new Date(Date.now() + 26 * 60 * 60 * 1000), // 1 day + 2 hours from now
        allDay: false,
        type: 'training_session',
        status: 'scheduled',
        priority: 'high',
        description: 'Advanced React training',
        location: 'Training Room',
        instructor: 'Jane Smith',
        traineeIds: ['user-3', 'user-4'],
        trainingProgramId: 'program-1',
        metadata: {},
      },
    ]

    const sampleNotifications: CalendarNotification[] = [
      {
        id: 'notif-1',
        type: 'event_created',
        title: 'New Event Created',
        message: 'Team Meeting has been scheduled for today at 2:00 PM',
        eventId: 'event-1',
        userId: 'current-user',
        scheduledFor: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
        status: 'sent',
        priority: 'medium',
        reminderType: 'in_app',
        metadata: {
          eventId: 'event-1',
          eventTitle: 'Team Meeting',
          eventStart: new Date(Date.now() + 2 * 60 * 60 * 1000),
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'notif-2',
        type: 'event_reminder',
        title: 'Event Reminder',
        message: 'Training Session starts in 1 hour',
        eventId: 'event-2',
        userId: 'current-user',
        scheduledFor: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
        status: 'pending',
        priority: 'high',
        reminderType: 'in_app',
        metadata: {
          eventId: 'event-2',
          eventTitle: 'Training Session',
          eventStart: new Date(Date.now() + 24 * 60 * 60 * 1000),
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ]

    const sampleReminders: EventReminder[] = [
      {
        id: 'reminder-1',
        eventId: 'event-1',
        userId: 'current-user',
        reminderType: 'in_app',
        scheduledFor: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
        sent: false,
        metadata: {
          eventId: 'event-1',
          eventTitle: 'Team Meeting',
          eventStart: new Date(Date.now() + 2 * 60 * 60 * 1000),
          reminderOffset: 90,
          reminderType: 'in_app',
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
      {
        id: 'reminder-2',
        eventId: 'event-2',
        userId: 'current-user',
        reminderType: 'email',
        scheduledFor: new Date(Date.now() + 23 * 60 * 60 * 1000), // 23 hours from now
        sent: false,
        metadata: {
          eventId: 'event-2',
          eventTitle: 'Training Session',
          eventStart: new Date(Date.now() + 24 * 60 * 60 * 1000),
          reminderOffset: 60,
          reminderType: 'email',
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      },
    ]

    setEvents(sampleEvents)
    setNotifications(sampleNotifications)
    setReminders(sampleReminders)
  }, [])

  // Demo functions
  const handleCreateEventWithNotifications = async () => {
    const eventData: CreateEventDto = {
      title: 'Demo Event with Notifications',
      start: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
      end: new Date(Date.now() + 5 * 60 * 60 * 1000), // 5 hours from now
      description: 'This is a demo event created with automatic notifications',
      type: 'meeting',
      priority: 'medium',
      location: 'Demo Room',
    }

    try {
      const event = await CalendarNotificationIntegration.createEventWithNotifications(eventData, {
        enableReminders: true,
        reminderTimes: [
          { time: new Date(Date.now() + 3.5 * 60 * 60 * 1000), type: 'in_app' }, // 30 minutes before
          { time: new Date(Date.now() + 3 * 60 * 60 * 1000), type: 'email' }, // 1 hour before
        ],
        notifyAttendees: true,
        attendeeIds: ['user-1', 'user-2'],
      })

      console.log('Event created with notifications:', event)
      alert('Event created successfully with notifications!')
    } catch (error) {
      console.error('Failed to create event:', error)
      alert('Failed to create event. Check console for details.')
    }
  }

  const handleScheduleCustomReminder = async () => {
    try {
      const reminder = await scheduleReminder(
        'event-1',
        new Date(Date.now() + 15 * 60 * 1000), // 15 minutes from now
        'in_app'
      )

      console.log('Reminder scheduled:', reminder)
      alert('Reminder scheduled successfully!')
    } catch (error) {
      console.error('Failed to schedule reminder:', error)
      alert('Failed to schedule reminder. Check console for details.')
    }
  }

  const handleNotifyEventChange = async () => {
    try {
      await notifyEventChange('event-1', 'updated')
      alert('Event change notification sent!')
    } catch (error) {
      console.error('Failed to notify event change:', error)
      alert('Failed to send notification. Check console for details.')
    }
  }

  const handleMarkNotificationAsRead = (notificationId: string) => {
    markAsRead(notificationId)
    setNotifications((prev) =>
      prev.map((notif) =>
        notif.id === notificationId
          ? { ...notif, status: 'read' as const, readAt: new Date() }
          : notif
      )
    )
  }

  const handleDeleteNotification = (notificationId: string) => {
    deleteNotification(notificationId)
    setNotifications((prev) => prev.filter((notif) => notif.id !== notificationId))
  }

  const handleUpdatePreferences = async () => {
    try {
      await updatePreferences({
        enabled: true,
        eventReminders: {
          enabled: true,
          defaultReminders: [
            { value: 15, unit: 'minutes', beforeEvent: true },
            { value: 60, unit: 'minutes', beforeEvent: true },
          ],
          customReminders: [],
        },
        channels: {
          email: true,
          push: true,
          inApp: true,
          sms: false,
        },
      })
      alert('Preferences updated successfully!')
    } catch (error) {
      console.error('Failed to update preferences:', error)
      alert('Failed to update preferences. Check console for details.')
    }
  }

  return (
    <div className="mx-auto w-full max-w-6xl space-y-6 p-6">
      <div className="text-center">
        <h1 className="mb-2 text-3xl font-bold">Calendar Notification Integration</h1>
        <p className="text-muted-foreground">
          Comprehensive notification system for GT-EGA calendar events
        </p>
      </div>

      {/* Demo Navigation */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification System Demo
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            <Button
              variant={activeDemo === 'create' ? 'default' : 'outline'}
              onClick={() => setActiveDemo('create')}
            >
              Create Event
            </Button>
            <Button
              variant={activeDemo === 'reminders' ? 'default' : 'outline'}
              onClick={() => setActiveDemo('reminders')}
            >
              Reminders
            </Button>
            <Button
              variant={activeDemo === 'preferences' ? 'default' : 'outline'}
              onClick={() => setActiveDemo('preferences')}
            >
              Preferences
            </Button>
            <Button
              variant={activeDemo === 'integration' ? 'default' : 'outline'}
              onClick={() => setActiveDemo('integration')}
            >
              Full Integration
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Demo Content */}
      {activeDemo === 'create' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Create Event with Notifications
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              Create an event with automatic notifications and reminders.
            </p>
            <div className="flex flex-wrap gap-2">
              <Button onClick={handleCreateEventWithNotifications}>
                Create Event with Notifications
              </Button>
              <Button variant="outline" onClick={handleNotifyEventChange}>
                Send Event Change Notification
              </Button>
            </div>

            <div className="mt-4">
              <h4 className="mb-2 font-medium">Current Events:</h4>
              <div className="space-y-2">
                {events.map((event) => (
                  <div key={event.id} className="rounded-lg border p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium">{event.title}</h5>
                        <p className="text-muted-foreground text-sm">
                          {event.start.toLocaleString()} - {event.end?.toLocaleString()}
                        </p>
                        <p className="text-muted-foreground text-sm">{event.location}</p>
                      </div>
                      <div className="flex gap-1">
                        <Badge variant="outline">{event.type}</Badge>
                        <Badge variant="outline">{event.priority}</Badge>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {activeDemo === 'reminders' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5" />
              Event Reminders
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">Manage event reminders and scheduling.</p>
            <div className="flex flex-wrap gap-2">
              <Button onClick={handleScheduleCustomReminder}>Schedule Custom Reminder</Button>
              <Button variant="outline" onClick={() => getEventReminders('event-1')}>
                Get Event Reminders
              </Button>
            </div>

            <div className="mt-4">
              <h4 className="mb-2 font-medium">Active Reminders:</h4>
              <div className="space-y-2">
                {reminders.map((reminder) => (
                  <div key={reminder.id} className="rounded-lg border p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h5 className="font-medium">{reminder.metadata.eventTitle}</h5>
                        <p className="text-muted-foreground text-sm">
                          Scheduled for: {reminder.scheduledFor.toLocaleString()}
                        </p>
                        <p className="text-muted-foreground text-sm">
                          Type: {reminder.reminderType}
                        </p>
                      </div>
                      <Badge variant={reminder.sent ? 'secondary' : 'outline'}>
                        {reminder.sent ? 'Sent' : 'Scheduled'}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {activeDemo === 'preferences' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Notification Preferences
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-muted-foreground">
              Configure your notification preferences and settings.
            </p>
            <Button onClick={handleUpdatePreferences}>Update Preferences</Button>

            <div className="mt-4">
              <h4 className="mb-2 font-medium">Current Preferences:</h4>
              <div className="rounded-lg border p-3">
                <pre className="text-sm">{JSON.stringify(preferences, null, 2)}</pre>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {activeDemo === 'integration' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5" />
              Full Notification Integration
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Complete notification system with all features integrated.
            </p>
            <Notifications
              notifications={notifications}
              reminders={reminders}
              onMarkAsRead={handleMarkNotificationAsRead}
              onDeleteNotification={handleDeleteNotification}
            />
          </CardContent>
        </Card>
      )}

      {/* Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle>System Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{notifications.length}</div>
              <div className="text-muted-foreground text-sm">Notifications</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{reminders.length}</div>
              <div className="text-muted-foreground text-sm">Active Reminders</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">{events.length}</div>
              <div className="text-muted-foreground text-sm">Calendar Events</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

export default NotificationIntegrationExample
