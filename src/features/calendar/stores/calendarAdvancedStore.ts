import { create } from 'zustand'
import { devtools, persist, subscribeWithSelector } from 'zustand/middleware'
import { immer } from 'zustand/middleware/immer'
import type { CalendarEvent } from '../types'
import type { DateInput } from '@fullcalendar/core'
import { useCalendarStore } from './calendarStore'
import { toDate } from '../utils/stateUtils'

/**
 * State snapshot for undo/redo functionality
 */
export interface StateSnapshot {
  id: string
  timestamp: Date
  state: Partial<ReturnType<typeof useCalendarStore.getState>>
  description: string
}

/**
 * Performance metrics for monitoring
 */
export interface PerformanceMetrics {
  renderCount: number
  lastRenderTime: number
  averageRenderTime: number
  operationCount: number
  lastOperationTime: number
  averageOperationTime: number
  memoryUsage: number
}

/**
 * Debug information for state debugging
 */
export interface DebugInfo {
  lastAction: string
  lastActionTimestamp: Date
  actionHistory: Array<{
    action: string
    timestamp: Date
    payload?: any
    stateSnapshot?: any
  }>
  performanceMetrics: PerformanceMetrics
  errors: Array<{
    error: string
    timestamp: Date
    context?: any
  }>
}

/**
 * Advanced calendar store state interface
 */
export interface CalendarAdvancedState {
  // Undo/Redo functionality
  pastSnapshots: StateSnapshot[]
  futureSnapshots: StateSnapshot[]
  maxSnapshots: number

  // Performance monitoring
  performanceMetrics: PerformanceMetrics

  // Debug information
  debugInfo: DebugInfo

  // State validation
  validationErrors: string[]
  isValidationEnabled: boolean

  // State migration
  migrationVersion: number
  pendingMigrations: Array<{
    version: number
    description: string
    migrate: () => Promise<void>
  }>
}

/**
 * Advanced calendar store actions interface
 */
export interface CalendarAdvancedActions {
  // Undo/Redo actions
  takeSnapshot: (description?: string) => void
  undo: () => boolean
  redo: () => boolean
  canUndo: () => boolean
  canRedo: () => boolean
  clearHistory: () => void

  // Performance monitoring
  recordRenderTime: (time: number) => void
  recordOperationTime: (time: number) => void
  resetPerformanceMetrics: () => void

  // Debug actions
  logAction: (action: string, payload?: any) => void
  logError: (error: string, context?: any) => void
  getDebugInfo: () => DebugInfo
  clearDebugInfo: () => void

  // State validation
  validateState: () => boolean
  enableValidation: () => void
  disableValidation: () => void

  // State migration
  addMigration: (version: number, description: string, migrate: () => Promise<void>) => void
  runMigrations: () => Promise<void>

  // Advanced state operations
  exportState: () => string
  importState: (stateJson: string) => boolean
  resetToSnapshot: (snapshotId: string) => boolean

  // Utilities
  compressHistory: () => void
  analyzePerformance: () => PerformanceMetrics
}

/**
 * Initial state for the advanced calendar store
 */
const initialAdvancedState: CalendarAdvancedState = {
  pastSnapshots: [],
  futureSnapshots: [],
  maxSnapshots: 50,
  performanceMetrics: {
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0,
    operationCount: 0,
    lastOperationTime: 0,
    averageOperationTime: 0,
    memoryUsage: 0,
  },
  debugInfo: {
    lastAction: '',
    lastActionTimestamp: new Date(),
    actionHistory: [],
    performanceMetrics: {
      renderCount: 0,
      lastRenderTime: 0,
      averageRenderTime: 0,
      operationCount: 0,
      lastOperationTime: 0,
      averageOperationTime: 0,
      memoryUsage: 0,
    },
    errors: [],
  },
  validationErrors: [],
  isValidationEnabled: true,
  migrationVersion: 1,
  pendingMigrations: [],
}

/**
 * Create the advanced calendar store with Zustand
 */
export const useCalendarAdvancedStore = create<CalendarAdvancedState & CalendarAdvancedActions>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          ...initialAdvancedState,

          // Undo/Redo actions
          takeSnapshot: (description = 'State snapshot') => {
            const currentState = useCalendarStore.getState()
            const snapshot: StateSnapshot = {
              id: `snapshot-${Date.now()}`,
              timestamp: new Date(),
              state: {
                events: { ...currentState.events },
                filters: { ...currentState.filters },
                searchQuery: currentState.searchQuery,
                selectedEventId: currentState.selectedEventId,
                currentView: currentState.currentView,
                currentDate: new Date(currentState.currentDate),
                weekendsVisible: currentState.weekendsVisible,
              },
              description,
            }

            set((state) => {
              state.pastSnapshots.push(snapshot)
              state.futureSnapshots = [] // Clear redo history when taking new snapshot

              // Limit snapshots to maxSnapshots
              if (state.pastSnapshots.length > state.maxSnapshots) {
                state.pastSnapshots.shift()
              }
            })
          },

          undo: () => {
            const { pastSnapshots, futureSnapshots } = get()

            if (pastSnapshots.length === 0) {
              return false
            }

            const currentSnapshot = pastSnapshots[pastSnapshots.length - 1]
            const currentState = useCalendarStore.getState()

            // Create snapshot of current state for redo
            const redoSnapshot: StateSnapshot = {
              id: `redo-${Date.now()}`,
              timestamp: new Date(),
              state: {
                events: { ...currentState.events },
                filters: { ...currentState.filters },
                searchQuery: currentState.searchQuery,
                selectedEventId: currentState.selectedEventId,
                currentView: currentState.currentView,
                currentDate: new Date(currentState.currentDate),
                weekendsVisible: currentState.weekendsVisible,
              },
              description: 'Before undo',
            }

            set((state) => {
              // Move current snapshot to future
              state.futureSnapshots.push(redoSnapshot)

              // Remove from past and apply
              state.pastSnapshots.pop()

              // Apply the previous state
              const targetSnapshot = state.pastSnapshots[state.pastSnapshots.length - 1]
              if (targetSnapshot) {
                const {
                  setEvents,
                  setFilters,
                  setSearchQuery,
                  setSelectedEvent,
                  setCurrentView,
                  setCurrentDate,
                  toggleWeekends,
                } = useCalendarStore.getState()

                if (targetSnapshot.state.events) {
                  setEvents(Object.values(targetSnapshot.state.events))
                }
                if (targetSnapshot.state.filters) {
                  setFilters(targetSnapshot.state.filters)
                }
                if (targetSnapshot.state.searchQuery !== undefined) {
                  setSearchQuery(targetSnapshot.state.searchQuery)
                }
                if (targetSnapshot.state.selectedEventId !== undefined) {
                  setSelectedEvent(targetSnapshot.state.selectedEventId)
                }
                if (targetSnapshot.state.currentView) {
                  setCurrentView(targetSnapshot.state.currentView)
                }
                if (targetSnapshot.state.currentDate) {
                  setCurrentDate(targetSnapshot.state.currentDate)
                }
                if (targetSnapshot.state.weekendsVisible !== undefined) {
                  if (targetSnapshot.state.weekendsVisible !== currentState.weekendsVisible) {
                    toggleWeekends()
                  }
                }
              }
            })

            return true
          },

          redo: () => {
            const { futureSnapshots } = get()

            if (futureSnapshots.length === 0) {
              return false
            }

            const snapshot = futureSnapshots[futureSnapshots.length - 1]

            set((state) => {
              // Remove from future and apply
              state.futureSnapshots.pop()

              // Apply the state
              const {
                setEvents,
                setFilters,
                setSearchQuery,
                setSelectedEvent,
                setCurrentView,
                setCurrentDate,
                toggleWeekends,
              } = useCalendarStore.getState()

              if (snapshot.state.events) {
                setEvents(Object.values(snapshot.state.events))
              }
              if (snapshot.state.filters) {
                setFilters(snapshot.state.filters)
              }
              if (snapshot.state.searchQuery !== undefined) {
                setSearchQuery(snapshot.state.searchQuery)
              }
              if (snapshot.state.selectedEventId !== undefined) {
                setSelectedEvent(snapshot.state.selectedEventId)
              }
              if (snapshot.state.currentView) {
                setCurrentView(snapshot.state.currentView)
              }
              if (snapshot.state.currentDate) {
                setCurrentDate(snapshot.state.currentDate)
              }
              if (snapshot.state.weekendsVisible !== undefined) {
                const currentState = useCalendarStore.getState()
                if (snapshot.state.weekendsVisible !== currentState.weekendsVisible) {
                  toggleWeekends()
                }
              }
            })

            return true
          },

          canUndo: () => {
            return get().pastSnapshots.length > 0
          },

          canRedo: () => {
            return get().futureSnapshots.length > 0
          },

          clearHistory: () => {
            set((state) => {
              state.pastSnapshots = []
              state.futureSnapshots = []
            })
          },

          // Performance monitoring
          recordRenderTime: (time: number) => {
            set((state) => {
              state.performanceMetrics.renderCount++
              state.performanceMetrics.lastRenderTime = time
              state.performanceMetrics.averageRenderTime =
                (state.performanceMetrics.averageRenderTime *
                  (state.performanceMetrics.renderCount - 1) +
                  time) /
                state.performanceMetrics.renderCount

              // Update debug info
              state.debugInfo.performanceMetrics = { ...state.performanceMetrics }
            })
          },

          recordOperationTime: (time: number) => {
            set((state) => {
              state.performanceMetrics.operationCount++
              state.performanceMetrics.lastOperationTime = time
              state.performanceMetrics.averageOperationTime =
                (state.performanceMetrics.averageOperationTime *
                  (state.performanceMetrics.operationCount - 1) +
                  time) /
                state.performanceMetrics.operationCount

              // Update debug info
              state.debugInfo.performanceMetrics = { ...state.performanceMetrics }
            })
          },

          resetPerformanceMetrics: () => {
            set((state) => {
              state.performanceMetrics = {
                renderCount: 0,
                lastRenderTime: 0,
                averageRenderTime: 0,
                operationCount: 0,
                lastOperationTime: 0,
                averageOperationTime: 0,
                memoryUsage: 0,
              }
              state.debugInfo.performanceMetrics = { ...state.performanceMetrics }
            })
          },

          // Debug actions
          logAction: (action: string, payload?: any) => {
            set((state) => {
              state.debugInfo.lastAction = action
              state.debugInfo.lastActionTimestamp = new Date()

              state.debugInfo.actionHistory.push({
                action,
                timestamp: new Date(),
                payload,
              })

              // Limit action history
              if (state.debugInfo.actionHistory.length > 100) {
                state.debugInfo.actionHistory.shift()
              }
            })
          },

          logError: (error: string, context?: any) => {
            set((state) => {
              state.debugInfo.errors.push({
                error,
                timestamp: new Date(),
                context,
              })

              // Limit error history
              if (state.debugInfo.errors.length > 50) {
                state.debugInfo.errors.shift()
              }
            })
          },

          getDebugInfo: () => {
            return get().debugInfo
          },

          clearDebugInfo: () => {
            set((state) => {
              state.debugInfo = {
                lastAction: '',
                lastActionTimestamp: new Date(),
                actionHistory: [],
                performanceMetrics: { ...state.performanceMetrics },
                errors: [],
              }
            })
          },

          // State validation
          validateState: () => {
            const currentState = useCalendarStore.getState()
            const errors: string[] = []

            // Validate events
            Object.values(currentState.events).forEach((event) => {
              if (!event.title || event.title.trim() === '') {
                errors.push(`Event ${event.id} has no title`)
              }
              if (!event.start) {
                errors.push(`Event ${event.id} has no start date`)
              }
              if (event.start && event.end && toDate(event.start) >= toDate(event.end)) {
                errors.push(`Event ${event.id} has end date before start date`)
              }
            })

            // Validate filters
            if (currentState.filters.dateRange) {
              if (currentState.filters.dateRange.start >= currentState.filters.dateRange.end) {
                errors.push('Date range filter has invalid dates')
              }
            }

            set((state) => {
              state.validationErrors = errors
            })

            return errors.length === 0
          },

          enableValidation: () => {
            set((state) => {
              state.isValidationEnabled = true
            })
          },

          disableValidation: () => {
            set((state) => {
              state.isValidationEnabled = false
            })
          },

          // State migration
          addMigration: (version: number, description: string, migrate: () => Promise<void>) => {
            set((state) => {
              state.pendingMigrations.push({ version, description, migrate })
              state.pendingMigrations.sort((a, b) => a.version - b.version)
            })
          },

          runMigrations: async () => {
            const { pendingMigrations, migrationVersion } = get()

            for (const migration of pendingMigrations) {
              if (migration.version > migrationVersion) {
                try {
                  await migration.migrate()
                  set((state) => {
                    state.migrationVersion = migration.version
                  })
                } catch (error) {
                  console.error(`Migration ${migration.version} failed:`, error)
                }
              }
            }
          },

          // Advanced state operations
          exportState: () => {
            const calendarState = useCalendarStore.getState()
            const advancedState = get()

            return JSON.stringify(
              {
                calendarState,
                advancedState,
                exportDate: new Date().toISOString(),
                version: '1.0.0',
              },
              null,
              2
            )
          },

          importState: (stateJson: string) => {
            try {
              const imported = JSON.parse(stateJson)

              if (imported.calendarState) {
                const {
                  setEvents,
                  setFilters,
                  setSearchQuery,
                  setSelectedEvent,
                  setCurrentView,
                  setCurrentDate,
                } = useCalendarStore.getState()

                if (imported.calendarState.events) {
                  setEvents(Object.values(imported.calendarState.events))
                }
                if (imported.calendarState.filters) {
                  setFilters(imported.calendarState.filters)
                }
                if (imported.calendarState.searchQuery !== undefined) {
                  setSearchQuery(imported.calendarState.searchQuery)
                }
                if (imported.calendarState.selectedEventId !== undefined) {
                  setSelectedEvent(imported.calendarState.selectedEventId)
                }
                if (imported.calendarState.currentView) {
                  setCurrentView(imported.calendarState.currentView)
                }
                if (imported.calendarState.currentDate) {
                  setCurrentDate(new Date(imported.calendarState.currentDate))
                }
              }

              return true
            } catch (error) {
              console.error('Failed to import state:', error)
              return false
            }
          },

          resetToSnapshot: (snapshotId: string) => {
            const { pastSnapshots } = get()
            const snapshot = pastSnapshots.find((s) => s.id === snapshotId)

            if (!snapshot) {
              return false
            }

            const {
              setEvents,
              setFilters,
              setSearchQuery,
              setSelectedEvent,
              setCurrentView,
              setCurrentDate,
              toggleWeekends,
            } = useCalendarStore.getState()

            if (snapshot.state.events) {
              setEvents(Object.values(snapshot.state.events))
            }
            if (snapshot.state.filters) {
              setFilters(snapshot.state.filters)
            }
            if (snapshot.state.searchQuery !== undefined) {
              setSearchQuery(snapshot.state.searchQuery)
            }
            if (snapshot.state.selectedEventId !== undefined) {
              setSelectedEvent(snapshot.state.selectedEventId)
            }
            if (snapshot.state.currentView) {
              setCurrentView(snapshot.state.currentView)
            }
            if (snapshot.state.currentDate) {
              setCurrentDate(snapshot.state.currentDate)
            }
            if (snapshot.state.weekendsVisible !== undefined) {
              const currentState = useCalendarStore.getState()
              if (snapshot.state.weekendsVisible !== currentState.weekendsVisible) {
                toggleWeekends()
              }
            }

            return true
          },

          // Utilities
          compressHistory: () => {
            set((state) => {
              // Keep only every 5th snapshot, but always keep the latest one
              const compressed = state.pastSnapshots.filter(
                (_, index) => index === state.pastSnapshots.length - 1 || index % 5 === 0
              )
              state.pastSnapshots = compressed
            })
          },

          analyzePerformance: () => {
            const metrics = get().performanceMetrics

            // Calculate performance score
            const renderScore =
              metrics.averageRenderTime < 16
                ? 100
                : Math.max(0, 100 - (metrics.averageRenderTime - 16))
            const operationScore =
              metrics.averageOperationTime < 100
                ? 100
                : Math.max(0, 100 - (metrics.averageOperationTime - 100) / 10)

            return {
              ...metrics,
              performanceScore: Math.round((renderScore + operationScore) / 2),
              recommendations: [
                metrics.averageRenderTime > 16 ? 'Consider optimizing render performance' : null,
                metrics.averageOperationTime > 100
                  ? 'Consider optimizing operation performance'
                  : null,
                metrics.renderCount > 1000 ? 'High render count detected' : null,
              ].filter(Boolean) as string[],
            }
          },
        }))
      ),
      {
        name: 'calendar-advanced-store',
        partialize: (state) => ({
          pastSnapshots: state.pastSnapshots.slice(-10), // Only keep last 10 snapshots
          futureSnapshots: state.futureSnapshots.slice(-5), // Only keep last 5 redo snapshots
          maxSnapshots: state.maxSnapshots,
          migrationVersion: state.migrationVersion,
          isValidationEnabled: state.isValidationEnabled,
        }),
      }
    ),
    {
      name: 'calendar-advanced-store',
    }
  )
)

/**
 * Hook for undo/redo functionality
 */
export const useCalendarUndoRedo = () => {
  const { takeSnapshot, undo, redo, canUndo, canRedo, clearHistory } = useCalendarAdvancedStore()

  return {
    takeSnapshot,
    undo,
    redo,
    canUndo,
    canRedo,
    clearHistory,
  }
}

/**
 * Hook for performance monitoring
 */
export const useCalendarPerformance = () => {
  const {
    performanceMetrics,
    recordRenderTime,
    recordOperationTime,
    resetPerformanceMetrics,
    analyzePerformance,
  } = useCalendarAdvancedStore()

  return {
    performanceMetrics,
    recordRenderTime,
    recordOperationTime,
    resetPerformanceMetrics,
    analyzePerformance,
  }
}

/**
 * Hook for debugging
 */
export const useCalendarDebug = () => {
  const {
    debugInfo,
    logAction,
    logError,
    getDebugInfo,
    clearDebugInfo,
    validationErrors,
    validateState,
    isValidationEnabled,
    enableValidation,
    disableValidation,
  } = useCalendarAdvancedStore()

  return {
    debugInfo,
    logAction,
    logError,
    getDebugInfo,
    clearDebugInfo,
    validationErrors,
    validateState,
    isValidationEnabled,
    enableValidation,
    disableValidation,
  }
}

/**
 * Hook for state import/export
 */
export const useCalendarStateManagement = () => {
  const {
    exportState,
    importState,
    resetToSnapshot,
    compressHistory,
    runMigrations,
    addMigration,
  } = useCalendarAdvancedStore()

  return {
    exportState,
    importState,
    resetToSnapshot,
    compressHistory,
    runMigrations,
    addMigration,
  }
}

export default useCalendarAdvancedStore
