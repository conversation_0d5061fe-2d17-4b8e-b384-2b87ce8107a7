import { AssessmentIntegrationService } from '../services/assessmentIntegrationService'
import type { Assessment, AssessmentReview } from '../../../shared/types/assessment'
import type { UserRole } from '../../../shared/types/roles'
import type { CalendarEvent } from '../types'

// Mock assessment data for testing
const mockAssessment: Assessment = {
  id: 'assessment-1',
  traineeId: 'trainee-1',
  quarter: 1,
  year: 2024,
  status: 'pending',
  scheduledDate: '2024-01-15T10:00:00Z',
  dueDate: '2024-01-20T17:00:00Z',
  createdAt: '2024-01-01T00:00:00Z',
  updatedAt: '2024-01-01T00:00:00Z',
  trainee: {
    id: 'trainee-1',
    userId: 'user-1',
    college: 'Test College',
    enrollmentDate: '2023-09-01T00:00:00Z',
    trainingProgramId: 'program-1',
    userName: '<PERSON>',
    userEmail: '<EMAIL>',
    createdAt: '2023-09-01T00:00:00Z',
  },
}

describe('AssessmentIntegrationService', () => {
  describe('transformAssessmentToEvents', () => {
    it('should transform assessment to calendar events correctly', () => {
      const events = AssessmentIntegrationService.transformAssessmentToEvents(mockAssessment)

      expect(events).toHaveLength(3) // deadline, session, reminder
      expect(events[0].id).toBe('assessment-deadline-assessment-1')
      expect(events[0].title).toContain('Assessment Deadline')
      expect(events[0].type).toBe('deadline')

      expect(events[1].id).toBe('assessment-session-assessment-1')
      expect(events[1].title).toContain('Assessment Session')
      expect(events[1].type).toBe('assessment')

      expect(events[2].id).toBe('assessment-reminder-assessment-1')
      expect(events[2].title).toContain('Assessment Reminder')
      expect(events[2].type).toBe('meeting')
    })

    it('should handle assessment with reviews', () => {
      const assessmentWithReviews: Assessment = {
        ...mockAssessment,
        reviews: [
          {
            id: 'review-1',
            assessmentId: 'assessment-1',
            reviewerId: 'reviewer-1',
            reviewerRole: 'admin' as UserRole,
            technicalScore: 85,
            softSkillsScore: 90,
            onJobScore: 88,
            comments: 'Good performance',
            createdAt: '2024-01-16T10:00:00Z',
            updatedAt: '2024-01-16T10:00:00Z',
          },
        ],
      }

      const events = AssessmentIntegrationService.transformAssessmentToEvents(assessmentWithReviews)

      expect(events).toHaveLength(4) // deadline, session, reminder, review
      const reviewEvent = events.find((e) => e.id === 'assessment-review-assessment-1-review-1')
      expect(reviewEvent).toBeDefined()
      expect(reviewEvent?.title).toContain('Review Session')
      expect(reviewEvent?.type).toBe('review')
    })

    it('should handle assessment without trainee name', () => {
      const assessmentWithoutTrainee: Assessment = {
        ...mockAssessment,
        trainee: undefined,
      }

      const events =
        AssessmentIntegrationService.transformAssessmentToEvents(assessmentWithoutTrainee)

      events.forEach((event) => {
        expect(event.title).toContain('Trainee')
      })
    })

    it('should create proper metadata for events', () => {
      const events = AssessmentIntegrationService.transformAssessmentToEvents(mockAssessment)

      events.forEach((event) => {
        const metadata = event.metadata as any
        expect(metadata.assessmentId).toBe('assessment-1')
        expect(metadata.status).toBe('pending')
        expect(metadata.quarter).toBe(1)
        expect(metadata.year).toBe(2024)
      })
    })

    it('should handle overdue assessments', () => {
      const overdueAssessment: Assessment = {
        ...mockAssessment,
        status: 'overdue',
        dueDate: '2023-12-01T17:00:00Z', // Past date
      }

      const events = AssessmentIntegrationService.transformAssessmentToEvents(overdueAssessment)
      const deadlineEvent = events.find((e) => e.type === 'deadline')

      expect(deadlineEvent?.priority).toBe('urgent')
      expect(deadlineEvent?.status).toBe('postponed')
    })

    it('should handle assessment with completed status', () => {
      const completedAssessment: Assessment = {
        ...mockAssessment,
        status: 'completed',
        overallScore: 85,
        completedAt: '2024-01-18T16:00:00Z',
      }

      const events = AssessmentIntegrationService.transformAssessmentToEvents(completedAssessment)
      const deadlineEvent = events.find((e) => e.type === 'deadline')

      expect(deadlineEvent?.status).toBe('completed')
    })
  })

  describe('safeToDate helper function', () => {
    it('should handle Date objects', () => {
      const date = new Date('2024-01-15T10:00:00Z')
      const result = AssessmentIntegrationService['safeToDate'](date)
      expect(result).toBe(date)
    })

    it('should handle string dates', () => {
      const dateString = '2024-01-15T10:00:00Z'
      const result = AssessmentIntegrationService['safeToDate'](dateString)
      expect(result).toBeInstanceOf(Date)
      expect(result.toISOString()).toBe(new Date(dateString).toISOString())
    })

    it('should handle number timestamps', () => {
      const timestamp = 1705310400000 // 2024-01-15T10:00:00Z
      const result = AssessmentIntegrationService['safeToDate'](timestamp)
      expect(result).toBeInstanceOf(Date)
      expect(result.getTime()).toBe(timestamp)
    })

    it('should handle array dates (FullCalendar format)', () => {
      const dateArray = [2024, 0, 15, 10, 0, 0] // 2024-01-15T10:00:00Z
      const result = AssessmentIntegrationService['safeToDate'](dateArray)
      expect(result).toBeInstanceOf(Date)
      expect(result.getFullYear()).toBe(2024)
      expect(result.getMonth()).toBe(0) // January
      expect(result.getDate()).toBe(15)
    })

    it('should handle invalid input with fallback', () => {
      const invalidInput = null as any
      const result = AssessmentIntegrationService['safeToDate'](invalidInput)
      expect(result).toBeInstanceOf(Date)
    })
  })

  describe('deadline priority calculation', () => {
    it('should return urgent for overdue assessments', () => {
      const overdueAssessment: Assessment = {
        ...mockAssessment,
        dueDate: '2023-12-01T17:00:00Z', // Past date
      }

      const events = AssessmentIntegrationService.transformAssessmentToEvents(overdueAssessment)
      const deadlineEvent = events.find((e) => e.type === 'deadline')

      expect(deadlineEvent?.priority).toBe('urgent')
    })

    it('should return high for assessments due within 3 days', () => {
      const urgentAssessment: Assessment = {
        ...mockAssessment,
        dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days from now
      }

      const events = AssessmentIntegrationService.transformAssessmentToEvents(urgentAssessment)
      const deadlineEvent = events.find((e) => e.type === 'deadline')

      expect(deadlineEvent?.priority).toBe('high')
    })

    it('should return medium for assessments due within 7 days', () => {
      const mediumAssessment: Assessment = {
        ...mockAssessment,
        dueDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
      }

      const events = AssessmentIntegrationService.transformAssessmentToEvents(mediumAssessment)
      const deadlineEvent = events.find((e) => e.type === 'deadline')

      expect(deadlineEvent?.priority).toBe('medium')
    })

    it('should return low for assessments due in more than 7 days', () => {
      const lowAssessment: Assessment = {
        ...mockAssessment,
        dueDate: new Date(Date.now() + 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days from now
      }

      const events = AssessmentIntegrationService.transformAssessmentToEvents(lowAssessment)
      const deadlineEvent = events.find((e) => e.type === 'deadline')

      expect(deadlineEvent?.priority).toBe('low')
    })
  })

  describe('event type mapping', () => {
    it('should map assessment types to calendar event types correctly', () => {
      const events = AssessmentIntegrationService.transformAssessmentToEvents(mockAssessment)

      const deadlineEvent = events.find((e) => e.id.includes('deadline'))
      const sessionEvent = events.find((e) => e.id.includes('session'))
      const reminderEvent = events.find((e) => e.id.includes('reminder'))

      expect(deadlineEvent?.type).toBe('deadline')
      expect(sessionEvent?.type).toBe('assessment')
      expect(reminderEvent?.type).toBe('meeting')
    })
  })

  describe('status mapping', () => {
    it('should map assessment status to calendar event status correctly', () => {
      const statusTests = [
        { assessmentStatus: 'pending' as const, expectedEventStatus: 'scheduled' as const },
        { assessmentStatus: 'in_progress' as const, expectedEventStatus: 'in_progress' as const },
        { assessmentStatus: 'completed' as const, expectedEventStatus: 'completed' as const },
        { assessmentStatus: 'overdue' as const, expectedEventStatus: 'postponed' as const },
      ]

      statusTests.forEach(({ assessmentStatus, expectedEventStatus }) => {
        const testAssessment: Assessment = {
          ...mockAssessment,
          status: assessmentStatus,
        }

        const events = AssessmentIntegrationService.transformAssessmentToEvents(testAssessment)
        const deadlineEvent = events.find((e) => e.type === 'deadline')

        expect(deadlineEvent?.status).toBe(expectedEventStatus)
      })
    })
  })

  describe('error handling', () => {
    it('should handle assessment with missing required fields', () => {
      const incompleteAssessment = {
        id: 'incomplete-1',
        traineeId: '',
        quarter: 0,
        year: 0,
        status: 'pending' as const,
        scheduledDate: '',
        dueDate: '',
        createdAt: '',
        updatedAt: '',
      }

      // Should not throw errors with incomplete data
      expect(() => {
        AssessmentIntegrationService.transformAssessmentToEvents(incompleteAssessment)
      }).not.toThrow()
    })

    it('should handle assessment with invalid dates', () => {
      const invalidDateAssessment: Assessment = {
        ...mockAssessment,
        scheduledDate: 'invalid-date',
        dueDate: 'invalid-date',
      }

      // Should not throw errors with invalid dates
      expect(() => {
        AssessmentIntegrationService.transformAssessmentToEvents(invalidDateAssessment)
      }).not.toThrow()
    })
  })
})

// Integration test example
describe('Assessment Calendar Integration', () => {
  it('should demonstrate complete assessment to calendar integration', () => {
    // Create a comprehensive assessment
    const comprehensiveAssessment: Assessment = {
      id: 'comprehensive-1',
      traineeId: 'trainee-1',
      quarter: 2,
      year: 2024,
      status: 'in_progress',
      scheduledDate: '2024-04-15T09:00:00Z',
      dueDate: '2024-04-20T17:00:00Z',
      overallScore: undefined,
      completedAt: undefined,
      createdAt: '2024-01-01T00:00:00Z',
      updatedAt: '2024-04-10T00:00:00Z',
      trainee: {
        id: 'trainee-1',
        userId: 'user-1',
        college: 'Engineering College',
        enrollmentDate: '2023-09-01T00:00:00Z',
        trainingProgramId: 'program-1',
        userName: 'Jane Smith',
        userEmail: '<EMAIL>',
        createdAt: '2023-09-01T00:00:00Z',
      },
      reviews: [
        {
          id: 'review-1',
          assessmentId: 'comprehensive-1',
          reviewerId: 'reviewer-1',
          reviewerRole: 'admin' as UserRole,
          technicalScore: 88,
          softSkillsScore: 92,
          onJobScore: 85,
          comments: 'Excellent technical skills',
          strengths: ['Problem solving', 'Team collaboration'],
          improvements: ['Documentation'],
          createdAt: '2024-04-16T14:00:00Z',
          updatedAt: '2024-04-16T14:00:00Z',
        },
      ],
    }

    // Transform to calendar events
    const events = AssessmentIntegrationService.transformAssessmentToEvents(comprehensiveAssessment)

    // Verify all expected events are created
    expect(events).toHaveLength(4)

    // Verify deadline event
    const deadlineEvent = events.find((e) => e.type === 'deadline')
    expect(deadlineEvent).toBeDefined()
    expect(deadlineEvent?.title).toBe('Jane Smith - Assessment Deadline')
    expect(deadlineEvent?.start).toBeInstanceOf(Date)
    expect(deadlineEvent?.allDay).toBe(true)
    expect(deadlineEvent?.priority).toBe('medium') // Based on due date
    expect(deadlineEvent?.status).toBe('in_progress')

    // Verify session event
    const sessionEvent = events.find((e) => e.type === 'assessment')
    expect(sessionEvent).toBeDefined()
    expect(sessionEvent?.title).toBe('Jane Smith - Assessment Session')
    expect(sessionEvent?.start).toBeInstanceOf(Date)
    expect(sessionEvent?.end).toBeInstanceOf(Date)
    expect(sessionEvent?.location).toBe('Assessment Room')

    // Verify review event
    const reviewEvent = events.find((e) => e.type === 'review')
    expect(reviewEvent).toBeDefined()
    expect(reviewEvent?.title).toBe('Jane Smith - Review Session')
    expect(reviewEvent?.status).toBe('completed')

    // Verify reminder event
    const reminderEvent = events.find((e) => e.type === 'meeting')
    expect(reminderEvent).toBeDefined()
    expect(reminderEvent?.title).toBe('Jane Smith - Assessment Reminder')
    expect(reminderEvent?.allDay).toBe(true)

    // Verify metadata is properly set
    events.forEach((event) => {
      const metadata = event.metadata as any
      expect(metadata.assessmentId).toBe('comprehensive-1')
      expect(metadata.traineeId).toBe('trainee-1')
      expect(metadata.quarter).toBe(2)
      expect(metadata.year).toBe(2024)
    })

    // Verify review-specific metadata
    const reviewMetadata = reviewEvent?.metadata as any
    expect(reviewMetadata.reviewerId).toBe('reviewer-1')
    expect(reviewMetadata.technicalScore).toBe(88)
    expect(reviewMetadata.softSkillsScore).toBe(92)
    expect(reviewMetadata.onJobScore).toBe(85)
  })
})
