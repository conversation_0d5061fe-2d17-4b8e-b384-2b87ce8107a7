import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest'
import { createEventId, getEventColor, getEventIcon, INITIAL_EVENTS } from '../services/eventUtils'
import { mockDateNow, restoreDateNow } from './utils/testUtils'

describe('eventUtils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockDateNow()
    // Reset the eventGuid counter by re-importing the module
    vi.resetModules()
  })

  afterEach(() => {
    restoreDateNow()
  })

  describe('createEventId', () => {
    it('should generate unique event IDs', () => {
      const id1 = createEventId()
      const id2 = createEventId()
      const id3 = createEventId()

      expect(typeof id1).toBe('string')
      expect(typeof id2).toBe('string')
      expect(typeof id3).toBe('string')
      expect(id1).not.toBe(id2)
      expect(id2).not.toBe(id3)
    })

    it('should return string IDs', () => {
      const id = createEventId()
      expect(typeof id).toBe('string')
    })

    it('should increment IDs sequentially', () => {
      const ids = Array.from({ length: 5 }, () => createEventId())
      expect(ids).toHaveLength(5)
      // Check that all IDs are unique and numeric strings
      const uniqueIds = [...new Set(ids)]
      expect(uniqueIds).toHaveLength(5)
      ids.forEach((id) => {
        expect(typeof id).toBe('string')
        expect(/^\d+$/.test(id)).toBe(true)
      })
    })
  })

  describe('getEventColor', () => {
    it('should return correct colors for each event type', () => {
      expect(getEventColor('training_session')).toBe('hsl(var(--primary))')
      expect(getEventColor('assessment')).toBe('hsl(142 76% 36%)')
      expect(getEventColor('meeting')).toBe('hsl(217 91% 60%)')
      expect(getEventColor('maintenance')).toBe('hsl(43 96% 56%)')
      expect(getEventColor('holiday')).toBe('hsl(346 87% 43%)')
      expect(getEventColor('deadline')).toBe('hsl(var(--destructive))')
      expect(getEventColor('review')).toBe('hsl(262 83% 58%)')
      expect(getEventColor('other')).toBe('hsl(var(--secondary))')
    })

    it('should return default color for unknown event types', () => {
      expect(getEventColor('unknown_type')).toBe('hsl(var(--secondary))')
      expect(getEventColor('')).toBe('hsl(var(--secondary))')
    })

    it('should handle case sensitivity', () => {
      expect(getEventColor('TRAINING_SESSION')).toBe('hsl(var(--secondary))')
      expect(getEventColor('Training_Session')).toBe('hsl(var(--secondary))')
    })
  })

  describe('getEventIcon', () => {
    it('should return correct icons for each event type', () => {
      expect(getEventIcon('training_session')).toBe('📚')
      expect(getEventIcon('assessment')).toBe('📝')
      expect(getEventIcon('meeting')).toBe('👥')
      expect(getEventIcon('maintenance')).toBe('🔧')
      expect(getEventIcon('holiday')).toBe('🎉')
      expect(getEventIcon('deadline')).toBe('⏰')
      expect(getEventIcon('review')).toBe('👀')
      expect(getEventIcon('other')).toBe('📌')
    })

    it('should return default icon for unknown event types', () => {
      expect(getEventIcon('unknown_type')).toBe('📌')
      expect(getEventIcon('')).toBe('📌')
    })

    it('should handle case sensitivity', () => {
      expect(getEventIcon('TRAINING_SESSION')).toBe('📌')
      expect(getEventIcon('Training_Session')).toBe('📌')
    })
  })

  describe('INITIAL_EVENTS', () => {
    it('should be an array of events', () => {
      expect(Array.isArray(INITIAL_EVENTS)).toBe(true)
      expect(INITIAL_EVENTS.length).toBeGreaterThan(0)
    })

    it('should contain events with required properties', () => {
      INITIAL_EVENTS.forEach((event, index) => {
        expect(event).toHaveProperty('id')
        expect(event).toHaveProperty('title')
        expect(event).toHaveProperty('start')
        expect(event).toHaveProperty('type')
        expect(event).toHaveProperty('description')

        expect(typeof event.id).toBe('string')
        expect(typeof event.title).toBe('string')
        expect(typeof event.start).toBe('string')
        expect(typeof event.type).toBe('string')
        expect(typeof event.description).toBe('string')
      })
    })

    it('should contain different event types', () => {
      const types = INITIAL_EVENTS.map((event) => event.type)
      const uniqueTypes = [...new Set(types)]

      expect(uniqueTypes).toContain('training_session')
      expect(uniqueTypes).toContain('assessment')
      expect(uniqueTypes).toContain('meeting')
      expect(uniqueTypes).toContain('deadline')
      expect(uniqueTypes).toContain('review')
      expect(uniqueTypes).toContain('holiday')
      expect(uniqueTypes).toContain('maintenance')
      expect(uniqueTypes).toContain('other')
    })

    it('should have valid date formats', () => {
      INITIAL_EVENTS.forEach((event) => {
        const startDate = new Date(event.start as string)
        expect(startDate.toString()).not.toBe('Invalid Date')

        if (event.end) {
          const endDate = new Date(event.end as string)
          expect(endDate.toString()).not.toBe('Invalid Date')
        }
      })
    })

    it('should have unique IDs', () => {
      const ids = INITIAL_EVENTS.map((event) => event.id)
      const uniqueIds = [...new Set(ids)]
      expect(ids.length).toBe(uniqueIds.length)
    })

    it('should have meaningful titles', () => {
      INITIAL_EVENTS.forEach((event) => {
        expect(event.title.length).toBeGreaterThan(0)
        expect(event.title.trim()).toBe(event.title)
      })
    })

    it('should have descriptions for all events', () => {
      INITIAL_EVENTS.forEach((event) => {
        expect(event.description).toBeDefined()
        expect(event.description!.length).toBeGreaterThan(0)
      })
    })

    it('should handle all-day events correctly', () => {
      const allDayEvents = INITIAL_EVENTS.filter((event) => event.allDay)
      const timedEvents = INITIAL_EVENTS.filter((event) => !event.allDay)

      allDayEvents.forEach((event) => {
        expect(event.start).toMatch(/^\d{4}-\d{2}-\d{2}$/) // YYYY-MM-DD format
      })

      timedEvents.forEach((event) => {
        expect(event.start).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}$/) // ISO format
        // Check that events that should have end times do have them
        const eventsWithEnd = [
          'Assessment: JavaScript Skills',
          'Team Meeting',
          'System Maintenance',
          'Code Review',
          'General Task',
        ]
        if (eventsWithEnd.includes(event.title || '')) {
          expect(event.end).toBeDefined()
        }
      })
    })

    it('should have events for different time periods', () => {
      const today = new Date()
      const todayStr = today.toISOString().replace(/T.*$/, '')
      const tomorrowStr = new Date(today.getTime() + 24 * 60 * 60 * 1000)
        .toISOString()
        .replace(/T.*$/, '')
      const nextWeekStr = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000)
        .toISOString()
        .replace(/T.*$/, '')

      const startDates = INITIAL_EVENTS.map((event) => (event.start as string).replace(/T.*$/, ''))

      expect(startDates).toContain(todayStr)
      expect(startDates).toContain(tomorrowStr)
      expect(startDates).toContain(nextWeekStr)
    })
  })

  describe('Integration tests', () => {
    it('should work together createEventId and INITIAL_EVENTS', () => {
      const newId = createEventId()
      expect(typeof newId).toBe('string')

      const initialIds = INITIAL_EVENTS.map((event) => event.id)
      expect(initialIds).not.toContain(newId)
    })

    it('should get color and icon for each initial event', () => {
      INITIAL_EVENTS.forEach((event) => {
        const color = getEventColor(event.type!)
        const icon = getEventIcon(event.type!)

        expect(typeof color).toBe('string')
        expect(typeof icon).toBe('string')
        expect(color.length).toBeGreaterThan(0)
        expect(icon.length).toBeGreaterThan(0)
      })
    })
  })
})
