import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import FullCalendarScheduler from '../components/FullCalendarScheduler'
import { mockDateNow, restoreDateNow, mockPrompt, mockConfirm } from './utils/testUtils'
import { mockCalendarEvents } from './utils/mocks'
import {
  mockFullCalendarModule,
  mockDateSelectInfo,
  mockEventClickInfo,
  resetFullCalendarMocks,
} from './utils/fullCalendarMocks'

// Mock FullCalendar before tests
mockFullCalendarModule()

describe('FullCalendarScheduler', () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockDateNow()
    resetFullCalendarMocks()
  })

  afterEach(() => {
    restoreDateNow()
  })

  it('should render FullCalendarScheduler component', () => {
    render(<FullCalendarScheduler />)

    expect(screen.getByTestId('calendar-test-wrapper')).toBeInTheDocument()
    expect(screen.getByText('GT-EGA Calendar')).toBeInTheDocument()
  })

  it('should render calendar with initial events', () => {
    render(<FullCalendarScheduler />)

    // Check that the calendar container is rendered
    expect(screen.getByTestId('calendar-container')).toBeInTheDocument()
    expect(screen.getByText('GT-EGA Calendar')).toBeInTheDocument()
  })

  it('should display current date and event count', () => {
    render(<FullCalendarScheduler />)

    expect(screen.getByText('January 2024 • 0 events')).toBeInTheDocument()
  })

  it('should handle Today button click', () => {
    render(<FullCalendarScheduler />)

    const todayButton = screen.getByText('Today')
    fireEvent.click(todayButton)

    // The button should be present and clickable
    expect(todayButton).toBeInTheDocument()
  })

  it('should handle Create Event button click', async () => {
    const promptSpy = mockPrompt('New Test Event')

    render(<FullCalendarScheduler />)

    const createButton = screen.getByText('Create Event')
    fireEvent.click(createButton)

    // Should show creating state
    expect(screen.getByText('Creating...')).toBeInTheDocument()

    // Wait for the prompt to be called
    await waitFor(() => {
      expect(promptSpy).toHaveBeenCalledWith('Please enter a new title for your event')
    })
  })

  it('should handle date selection', () => {
    const promptSpy = mockPrompt('Selected Event')

    render(<FullCalendarScheduler />)

    // Simulate date selection by triggering the select handler
    const mockCalendar = screen.getByTestId('fullcalendar-mock')

    // The select prop should be passed to FullCalendar
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle event click with confirmation', () => {
    const confirmSpy = mockConfirm(true)

    render(<FullCalendarScheduler />)

    // The event click handler should be set up
    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle event click with cancellation', () => {
    const confirmSpy = mockConfirm(false)

    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should render with correct initial view', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle view changes', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle date range changes', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should render event content correctly', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle events set callback', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should apply event type data attributes', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle calendar API interactions', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should render with proper configuration', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle weekend visibility', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle event creation flow', async () => {
    const promptSpy = mockPrompt('Test Event Creation')

    render(<FullCalendarScheduler />)

    const createButton = screen.getByText('Create Event')
    fireEvent.click(createButton)

    await waitFor(() => {
      expect(promptSpy).toHaveBeenCalled()
    })
  })

  it('should handle event deletion flow', () => {
    const confirmSpy = mockConfirm(true)

    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should display event statistics', () => {
    render(<FullCalendarScheduler />)

    // Should show event count in the header
    expect(screen.getByText('January 2024 • 0 events')).toBeInTheDocument()
  })

  it('should handle responsive design', () => {
    render(<FullCalendarScheduler />)

    // Should render both desktop and mobile Today buttons
    const todayButtons = screen.getAllByText('Today')
    expect(todayButtons).toHaveLength(2)
  })

  it('should handle calendar navigation', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle event resizing and dragging', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle multiple event types', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })

  it('should handle calendar height and layout', () => {
    render(<FullCalendarScheduler />)

    const mockCalendar = screen.getByTestId('fullcalendar-mock')
    expect(mockCalendar).toBeInTheDocument()
  })
})
