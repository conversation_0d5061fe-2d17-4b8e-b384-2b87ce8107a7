import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import { TrainingIntegrationService } from '../services/trainingIntegrationService'
import { useTrainingCalendar } from '../hooks/useTrainingCalendar'
import { TrainingEventsList } from '../components/TrainingEvents'
import { TrainingTimeline } from '../components/TrainingTimeline'
import type { TrainingProgram } from '../../../shared/types/trainingProgram'
import type { CalendarEvent } from '../types'

// Mock the training program service
jest.mock('../../../features/training-programs/services/trainingProgramService', () => ({
  TrainingProgramService: {
    getTrainingPrograms: jest.fn(),
    getTrainingProgramById: jest.fn(),
    createTrainingProgram: jest.fn(),
    updateTrainingProgram: jest.fn(),
    deleteTrainingProgram: jest.fn(),
  },
}))

// Mock the calendar store
jest.mock('../stores/calendarStore', () => ({
  useCalendarStore: jest.fn(),
  useCalendarActions: jest.fn(() => ({
    addEvent: jest.fn(),
    updateEvent: jest.fn(),
    deleteEvent: jest.fn(),
    bulkDeleteEvents: jest.fn(),
    setEvents: jest.fn(),
  })),
}))

// Sample training program data for testing
const sampleTrainingProgram: TrainingProgram = {
  id: 'program-1',
  name: 'Advanced JavaScript Development',
  durationMonths: 3,
  description: 'Comprehensive JavaScript training program',
  status: 'active',
  instructor: 'John Doe',
  department: 'Engineering',
  maxCapacity: 20,
  currentEnrollment: 15,
  schedule: {
    id: 'schedule-1',
    programId: 'program-1',
    startDate: '2024-01-01T00:00:00.000Z',
    endDate: '2024-03-31T23:59:59.999Z',
    sessions: [
      {
        id: 'session-1',
        title: 'JavaScript Fundamentals',
        description: 'Introduction to JavaScript basics',
        startDate: '2024-01-15T09:00:00.000Z',
        endDate: '2024-01-15T17:00:00.000Z',
        instructor: 'John Doe',
        location: 'Room 101',
        capacity: 20,
        enrolledCount: 15,
        type: 'lecture',
        status: 'scheduled',
      },
      {
        id: 'session-2',
        title: 'Advanced JavaScript Concepts',
        description: 'Deep dive into advanced JavaScript',
        startDate: '2024-01-22T09:00:00.000Z',
        endDate: '2024-01-22T17:00:00.000Z',
        instructor: 'Jane Smith',
        location: 'Lab 201',
        capacity: 15,
        enrolledCount: 12,
        type: 'workshop',
        status: 'scheduled',
      },
    ],
    milestones: [
      {
        id: 'milestone-1',
        title: 'Mid-term Assessment',
        description: 'Comprehensive assessment of first half',
        dueDate: '2024-02-15T23:59:59.999Z',
        type: 'assessment',
        status: 'pending',
        weight: 30,
      },
      {
        id: 'milestone-2',
        title: 'Final Project',
        description: 'Complete development project',
        dueDate: '2024-03-25T23:59:59.999Z',
        type: 'project',
        status: 'pending',
        weight: 40,
      },
    ],
  },
  progress: {
    completedSessions: 0,
    totalSessions: 2,
    completedMilestones: 0,
    totalMilestones: 2,
    overallPercentage: 0,
  },
}

describe('Training Integration Service', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('transformProgramToEvents', () => {
    it('should transform training program to calendar events', () => {
      const events = TrainingIntegrationService.transformProgramToEvents(sampleTrainingProgram)

      expect(events).toHaveLength(5) // 2 sessions + 2 milestones + 1 program start + 1 program end

      // Check program start event
      const programStart = events.find((e) => e.metadata?.eventType === 'program_start')
      expect(programStart).toBeDefined()
      expect(programStart?.title).toBe('Advanced JavaScript Development - Program Start')
      expect(programStart?.start).toBe('2024-01-01T00:00:00.000Z')

      // Check training session events
      const sessionEvents = events.filter((e) => e.metadata?.eventType === 'training_session')
      expect(sessionEvents).toHaveLength(2)

      const firstSession = sessionEvents.find((e) => e.metadata?.sessionId === 'session-1')
      expect(firstSession).toBeDefined()
      expect(firstSession?.title).toBe('Advanced JavaScript Development - JavaScript Fundamentals')
      expect(firstSession?.instructor).toBe('John Doe')
      expect(firstSession?.location).toBe('Room 101')

      // Check milestone events
      const milestoneEvents = events.filter((e) => e.metadata?.eventType === 'milestone')
      expect(milestoneEvents).toHaveLength(2)

      const firstMilestone = milestoneEvents.find((e) => e.metadata?.milestoneId === 'milestone-1')
      expect(firstMilestone).toBeDefined()
      expect(firstMilestone?.title).toBe('Advanced JavaScript Development - Mid-term Assessment')
      expect(firstMilestone?.type).toBe('assessment')
    })

    it('should handle program without schedule', () => {
      const programWithoutSchedule = { ...sampleTrainingProgram, schedule: undefined }
      const events = TrainingIntegrationService.transformProgramToEvents(programWithoutSchedule)

      expect(events).toHaveLength(0)
    })
  })

  describe('transformSessionToEvent', () => {
    it('should transform training session to calendar event', () => {
      const session = sampleTrainingProgram.schedule!.sessions[0]
      const event = TrainingIntegrationService.transformSessionToEvent(
        session,
        sampleTrainingProgram
      )

      expect(event.id).toBe('training-program-1-session-1')
      expect(event.title).toBe('Advanced JavaScript Development - JavaScript Fundamentals')
      expect(event.type).toBe('training_session')
      expect(event.instructor).toBe('John Doe')
      expect(event.location).toBe('Room 101')
      expect(event.metadata?.sessionType).toBe('lecture')
      expect(event.metadata?.capacity).toBe(20)
    })
  })

  describe('transformMilestoneToEvent', () => {
    it('should transform milestone to calendar event', () => {
      const milestone = sampleTrainingProgram.schedule!.milestones[0]
      const event = TrainingIntegrationService.transformMilestoneToEvent(
        milestone,
        sampleTrainingProgram
      )

      expect(event.id).toBe('milestone-program-1-milestone-1')
      expect(event.title).toBe('Advanced JavaScript Development - Mid-term Assessment')
      expect(event.type).toBe('assessment')
      expect(event.allDay).toBe(true)
      expect(event.metadata?.milestoneType).toBe('assessment')
      expect(event.metadata?.weight).toBe(30)
    })
  })
})

describe('Training Events Components', () => {
  const queryClient = new QueryClient()

  const renderWithQueryClient = (component: React.ReactElement) => {
    return render(<QueryClientProvider client={queryClient}>{component}</QueryClientProvider>)
  }

  describe('TrainingEventsList', () => {
    it('should render training events list', () => {
      const events = TrainingIntegrationService.transformProgramToEvents(sampleTrainingProgram)

      renderWithQueryClient(
        <TrainingEventsList events={events} onEventClick={jest.fn()} compact={false} />
      )

      expect(
        screen.getByText('Advanced JavaScript Development - Program Start')
      ).toBeInTheDocument()
      expect(
        screen.getByText('Advanced JavaScript Development - JavaScript Fundamentals')
      ).toBeInTheDocument()
      expect(
        screen.getByText('Advanced JavaScript Development - Mid-term Assessment')
      ).toBeInTheDocument()
    })

    it('should render empty state when no events', () => {
      renderWithQueryClient(<TrainingEventsList events={[]} onEventClick={jest.fn()} />)

      expect(screen.getByText('No training events found')).toBeInTheDocument()
    })
  })

  describe('TrainingTimeline', () => {
    it('should render training timeline', () => {
      const events = TrainingIntegrationService.transformProgramToEvents(sampleTrainingProgram)

      renderWithQueryClient(
        <TrainingTimeline
          events={events}
          programs={[sampleTrainingProgram]}
          onEventClick={jest.fn()}
          onProgramClick={jest.fn()}
        />
      )

      expect(screen.getByText('Training Timeline')).toBeInTheDocument()
      expect(screen.getByText('Program Progress')).toBeInTheDocument()
      expect(screen.getByText('Advanced JavaScript Development')).toBeInTheDocument()
      expect(screen.getByText('Training Events')).toBeInTheDocument()
    })
  })
})

describe('useTrainingCalendar Hook', () => {
  it('should provide training calendar functionality', async () => {
    const TestComponent = () => {
      const trainingCalendar = useTrainingCalendar()

      return (
        <div>
          <div data-testid="has-programs">{trainingCalendar.hasTrainingPrograms.toString()}</div>
          <div data-testid="has-events">{trainingCalendar.hasTrainingEvents.toString()}</div>
          <div data-testid="loading">{trainingCalendar.programsLoading.toString()}</div>
        </div>
      )
    }

    renderWithQueryClient(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('has-programs')).toHaveTextContent('false')
      expect(screen.getByTestId('has-events')).toHaveTextContent('false')
      expect(screen.getByTestId('loading')).toHaveTextContent('true')
    })
  })
})

describe('Integration Tests', () => {
  it('should integrate training programs with calendar seamlessly', async () => {
    // Mock the training program service to return sample data
    const {
      TrainingProgramService,
    } = require('../../../features/training-programs/services/trainingProgramService')
    TrainingProgramService.getTrainingPrograms.mockResolvedValue([sampleTrainingProgram])

    const TestComponent = () => {
      const { programs, trainingEvents, loadTrainingEvents, syncTrainingPrograms } =
        useTrainingCalendar()

      React.useEffect(() => {
        loadTrainingEvents()
      }, [loadTrainingEvents])

      const handleSync = async () => {
        await syncTrainingPrograms()
      }

      return (
        <div>
          <div data-testid="programs-count">{programs.length}</div>
          <div data-testid="events-count">{trainingEvents.length}</div>
          <button onClick={handleSync} data-testid="sync-button">
            Sync Training Programs
          </button>
        </div>
      )
    }

    renderWithQueryClient(<TestComponent />)

    await waitFor(() => {
      expect(screen.getByTestId('programs-count')).toHaveTextContent('0')
      expect(screen.getByTestId('events-count')).toHaveTextContent('0')
    })

    // Test sync functionality
    const syncButton = screen.getByTestId('sync-button')
    syncButton.click()

    await waitFor(() => {
      expect(TrainingProgramService.getTrainingPrograms).toHaveBeenCalled()
    })
  })
})

// Sample data for manual testing
export const sampleTrainingPrograms: TrainingProgram[] = [
  sampleTrainingProgram,
  {
    ...sampleTrainingProgram,
    id: 'program-2',
    name: 'React Development',
    instructor: 'Jane Smith',
    schedule: {
      ...sampleTrainingProgram.schedule!,
      id: 'schedule-2',
      programId: 'program-2',
      sessions: [
        {
          id: 'session-3',
          title: 'React Basics',
          description: 'Introduction to React',
          startDate: '2024-02-01T09:00:00.000Z',
          endDate: '2024-02-01T17:00:00.000Z',
          instructor: 'Jane Smith',
          location: 'Room 102',
          capacity: 15,
          enrolledCount: 12,
          type: 'workshop',
          status: 'scheduled',
        },
      ],
      milestones: [
        {
          id: 'milestone-3',
          title: 'React Project',
          description: 'Build a React application',
          dueDate: '2024-02-28T23:59:59.999Z',
          type: 'project',
          status: 'pending',
          weight: 50,
        },
      ],
    },
  },
]

export const sampleCalendarEvents: CalendarEvent[] = [
  ...TrainingIntegrationService.transformProgramToEvents(sampleTrainingProgram),
  ...TrainingIntegrationService.transformProgramToEvents(sampleTrainingPrograms[1]),
]
