import { vi } from 'vitest'
import React from 'react'
import type {
  CalendarApi,
  EventApi,
  EventInput,
  DateSelectArg,
  EventClickArg,
} from '@fullcalendar/core'

// Mock FullCalendar component
export const MockFullCalendar = vi.fn().mockImplementation(({ children, ...props }: any) => {
  return React.createElement(
    'div',
    {
      'data-testid': 'fullcalendar-mock',
      ...props,
    },
    children
  )
})

// Mock FullCalendar API
export const mockCalendarApi = {
  getDate: vi.fn(() => new Date('2024-01-15')),
  gotoDate: vi.fn(),
  prev: vi.fn(),
  next: vi.fn(),
  today: vi.fn(),
  changeView: vi.fn(),
  select: vi.fn(),
  unselect: vi.fn(),
  addEvent: vi.fn(() => mockEventApi),
  getEventById: vi.fn(() => mockEventApi),
  removeEventById: vi.fn(),
  removeAllEvents: vi.fn(),
  refetchEvents: vi.fn(),
  updateEvent: vi.fn(),
  render: vi.fn(),
  destroy: vi.fn(),
  batchRendering: vi.fn(),
  getCurrentData: vi.fn(() => ({
    currentViewType: 'dayGridMonth',
    currentDate: new Date('2024-01-15'),
    events: [],
  })),
  getOption: vi.fn(),
  setOption: vi.fn(),
  on: vi.fn(),
  off: vi.fn(),
  trigger: vi.fn(),
} as any

// Mock FullCalendar Event API
export const mockEventApi = {
  id: 'test-event-1',
  title: 'Test Event',
  start: new Date('2024-01-15T10:00:00.000Z'),
  end: new Date('2024-01-15T11:00:00.000Z'),
  allDay: false,
  extendedProps: {
    type: 'other',
    description: 'Test event description',
  },
  source: null,
  groupId: '',
  def: {} as any,
  ui: {} as any,
  instance: {} as any,
  range: { start: new Date(), end: new Date() },
  remove: vi.fn(),
  setProp: vi.fn(),
  setExtendedProp: vi.fn(),
  setDates: vi.fn(),
  moveStart: vi.fn(),
  moveEnd: vi.fn(),
  moveDates: vi.fn(),
  isAllDay: vi.fn(() => false),
  isStart: vi.fn(() => true),
  isEnd: vi.fn(() => true),
  isPast: vi.fn(() => false),
  isFuture: vi.fn(() => true),
  isToday: vi.fn(() => true),
  intersectsRange: vi.fn(() => true),
  containsRange: vi.fn(() => false),
  toPlainObject: vi.fn(() => ({})),
  toJSON: vi.fn(() => ({})),
} as any

// Mock date select info
export const mockDateSelectInfo = {
  start: new Date('2024-01-15T10:00:00.000Z'),
  end: new Date('2024-01-15T11:00:00.000Z'),
  startStr: '2024-01-15T10:00:00.000Z',
  endStr: '2024-01-15T11:00:00.000Z',
  allDay: false,
  view: {
    calendar: mockCalendarApi,
    type: 'dayGridMonth',
    title: 'January 2024',
    activeStart: new Date('2024-01-01'),
    activeEnd: new Date('2024-01-31'),
    currentStart: new Date('2024-01-01'),
    currentEnd: new Date('2024-01-31'),
  },
  jsEvent: new MouseEvent('click'),
}

// Mock event click info
export const mockEventClickInfo = {
  event: mockEventApi,
  el: document.createElement('div'),
  jsEvent: new MouseEvent('click'),
  view: {
    calendar: mockCalendarApi,
    type: 'dayGridMonth',
    title: 'January 2024',
    activeStart: new Date('2024-01-01'),
    activeEnd: new Date('2024-01-31'),
    currentStart: new Date('2024-01-01'),
    currentEnd: new Date('2024-01-31'),
  },
}

// Mock FullCalendar plugins
export const mockAdaptivePlugin = {
  name: 'adaptive',
}

export const mockDayGridPlugin = {
  name: 'dayGrid',
}

export const mockTimeGridPlugin = {
  name: 'timeGrid',
}

export const mockInteractionPlugin = {
  name: 'interaction',
}

export const mockListPlugin = {
  name: 'list',
}

export const mockMultiMonthPlugin = {
  name: 'multiMonth',
}

// Mock FullCalendar React component ref
export const mockFullCalendarRef = {
  current: {
    getApi: vi.fn(() => mockCalendarApi),
  },
}

// Helper to mock FullCalendar module
export const mockFullCalendarModule = () => {
  vi.mock('@fullcalendar/react', () => ({
    default: MockFullCalendar,
  }))

  vi.mock('@fullcalendar/adaptive', () => ({
    default: mockAdaptivePlugin,
  }))

  vi.mock('@fullcalendar/daygrid', () => ({
    default: mockDayGridPlugin,
  }))

  vi.mock('@fullcalendar/timegrid', () => ({
    default: mockTimeGridPlugin,
  }))

  vi.mock('@fullcalendar/interaction', () => ({
    default: mockInteractionPlugin,
  }))

  vi.mock('@fullcalendar/list', () => ({
    default: mockListPlugin,
  }))

  vi.mock('@fullcalendar/multimonth', () => ({
    default: mockMultiMonthPlugin,
  }))
}

// Helper to create mock event input
export const createMockEventInput = (overrides: Partial<EventInput> = {}): EventInput => ({
  id: 'test-event-1',
  title: 'Test Event',
  start: '2024-01-15T10:00:00.000Z',
  end: '2024-01-15T11:00:00.000Z',
  allDay: false,
  extendedProps: {
    type: 'other',
    description: 'Test event description',
  },
  ...overrides,
})

// Helper to create mock date select info with custom dates
export const createMockDateSelectInfo = (
  start: Date = new Date('2024-01-15T10:00:00.000Z'),
  end: Date = new Date('2024-01-15T11:00:00.000Z'),
  allDay: boolean = false
) => ({
  start,
  end,
  startStr: start.toISOString(),
  endStr: end.toISOString(),
  allDay,
  view: {
    calendar: mockCalendarApi,
    type: 'dayGridMonth',
    title: 'January 2024',
    activeStart: new Date('2024-01-01'),
    activeEnd: new Date('2024-01-31'),
    currentStart: new Date('2024-01-01'),
    currentEnd: new Date('2024-01-31'),
  },
  jsEvent: new MouseEvent('click'),
})

// Helper to create mock event click info with custom event
export const createMockEventClickInfo = (event: any = mockEventApi) => ({
  event,
  el: document.createElement('div'),
  jsEvent: new MouseEvent('click'),
  view: {
    calendar: mockCalendarApi,
    type: 'dayGridMonth',
    title: 'January 2024',
    activeStart: new Date('2024-01-01'),
    activeEnd: new Date('2024-01-31'),
    currentStart: new Date('2024-01-01'),
    currentEnd: new Date('2024-01-31'),
  },
})

// Helper to reset all FullCalendar mocks
export const resetFullCalendarMocks = () => {
  vi.clearAllMocks()
  MockFullCalendar.mockClear()
}
