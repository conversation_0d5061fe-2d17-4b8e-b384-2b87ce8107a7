import type { CalendarEvent, EventType, EventStatus, EventPriority } from '../../types'

// Mock calendar event data
export const mockCalendarEvent: CalendarEvent = {
  id: 'test-event-1',
  title: 'Test Event',
  start: new Date('2024-01-15T10:00:00.000Z'),
  end: new Date('2024-01-15T11:00:00.000Z'),
  allDay: false,
  type: 'other' as EventType,
  description: 'Test event description',
  status: 'scheduled' as EventStatus,
  priority: 'medium' as EventPriority,
  location: 'Test Location',
  createdAt: '2024-01-15T10:00:00.000Z',
  updatedAt: '2024-01-15T10:00:00.000Z',
}

export const mockTrainingSessionEvent: CalendarEvent = {
  id: 'training-1',
  title: 'Training Session: React Fundamentals',
  start: new Date('2024-01-15T09:00:00.000Z'),
  end: new Date('2024-01-15T17:00:00.000Z'),
  allDay: true,
  type: 'training_session' as EventType,
  description: 'Introduction to React components and hooks',
  status: 'scheduled' as EventStatus,
  priority: 'high' as EventPriority,
  location: 'Training Room A',
  createdAt: '2024-01-15T10:00:00.000Z',
  updatedAt: '2024-01-15T10:00:00.000Z',
}

export const mockAssessmentEvent: CalendarEvent = {
  id: 'assessment-1',
  title: 'Assessment: JavaScript Skills',
  start: new Date('2024-01-15T14:00:00.000Z'),
  end: new Date('2024-01-15T16:00:00.000Z'),
  allDay: false,
  type: 'assessment' as EventType,
  description: 'JavaScript proficiency assessment',
  status: 'scheduled' as EventStatus,
  priority: 'high' as EventPriority,
  location: 'Assessment Center',
  createdAt: '2024-01-15T10:00:00.000Z',
  updatedAt: '2024-01-15T10:00:00.000Z',
}

export const mockMeetingEvent: CalendarEvent = {
  id: 'meeting-1',
  title: 'Team Meeting',
  start: new Date('2024-01-16T10:00:00.000Z'),
  end: new Date('2024-01-16T11:00:00.000Z'),
  allDay: false,
  type: 'meeting' as EventType,
  description: 'Weekly team sync',
  status: 'scheduled' as EventStatus,
  priority: 'medium' as EventPriority,
  location: 'Conference Room B',
  createdAt: '2024-01-15T10:00:00.000Z',
  updatedAt: '2024-01-15T10:00:00.000Z',
}

export const mockDeadlineEvent: CalendarEvent = {
  id: 'deadline-1',
  title: 'Project Deadline',
  start: new Date('2024-01-22T17:00:00.000Z'),
  allDay: false,
  type: 'deadline' as EventType,
  description: 'Q4 project submission deadline',
  status: 'scheduled' as EventStatus,
  priority: 'urgent' as EventPriority,
  location: 'Online',
  createdAt: '2024-01-15T10:00:00.000Z',
  updatedAt: '2024-01-15T10:00:00.000Z',
}

// Array of mock events for testing
export const mockCalendarEvents: CalendarEvent[] = [
  mockCalendarEvent,
  mockTrainingSessionEvent,
  mockAssessmentEvent,
  mockMeetingEvent,
  mockDeadlineEvent,
]

// Mock event types
export const mockEventTypes: EventType[] = [
  'training_session',
  'assessment',
  'meeting',
  'maintenance',
  'holiday',
  'deadline',
  'review',
  'other',
]

// Mock event statuses
export const mockEventStatuses: EventStatus[] = [
  'scheduled',
  'in_progress',
  'completed',
  'cancelled',
  'postponed',
]

// Mock event priorities
export const mockEventPriorities: EventPriority[] = ['low', 'medium', 'high', 'urgent']

// Mock calendar view types
export const mockCalendarViewTypes = [
  'dayGridMonth',
  'timeGridWeek',
  'timeGridDay',
  'listWeek',
  'multiMonthYear',
]

// Mock calendar statistics
export const mockCalendarStats = {
  totalEvents: 5,
  eventsByType: {
    training_session: 1,
    assessment: 1,
    meeting: 1,
    deadline: 1,
    other: 1,
  } as Record<EventType, number>,
  eventsByStatus: {
    scheduled: 5,
  } as Record<EventStatus, number>,
  eventsByPriority: {
    low: 0,
    medium: 2,
    high: 2,
    urgent: 1,
  } as Record<EventPriority, number>,
  upcomingEvents: [mockTrainingSessionEvent, mockAssessmentEvent],
  overdueEvents: [],
  eventsThisWeek: [mockTrainingSessionEvent, mockAssessmentEvent, mockMeetingEvent],
  eventsThisMonth: mockCalendarEvents,
}

// Mock calendar filter
export const mockCalendarFilter = {
  type: ['training_session' as const, 'assessment' as const],
  status: ['scheduled' as const],
  priority: ['high' as const, 'urgent' as const],
  dateRange: {
    start: new Date('2024-01-01'),
    end: new Date('2024-01-31'),
  },
  location: 'Training Room A',
}

// Mock create event DTO
export const mockCreateEventDto = {
  title: 'New Test Event',
  start: new Date('2024-01-20T10:00:00.000Z'),
  end: new Date('2024-01-20T11:00:00.000Z'),
  allDay: false,
  description: 'New test event description',
  type: 'other' as EventType,
  priority: 'medium' as EventPriority,
  location: 'Test Location',
}

// Mock update event DTO
export const mockUpdateEventDto = {
  id: 'test-event-1',
  title: 'Updated Test Event',
  description: 'Updated test event description',
  priority: 'high' as EventPriority,
}

// Helper function to create mock event with overrides
export const createMockEvent = (overrides: Partial<CalendarEvent> = {}): CalendarEvent => ({
  ...mockCalendarEvent,
  ...overrides,
})

// Helper function to create multiple mock events
export const createMockEvents = (
  count: number,
  baseEvent?: Partial<CalendarEvent>
): CalendarEvent[] => {
  return Array.from({ length: count }, (_, index) =>
    createMockEvent({
      id: `mock-event-${index + 1}`,
      title: `Mock Event ${index + 1}`,
      start: new Date(`2024-01-${String(index + 1).padStart(2, '0')}T10:00:00.000Z`),
      end: new Date(`2024-01-${String(index + 1).padStart(2, '0')}T11:00:00.000Z`),
      ...baseEvent,
    })
  )
}
