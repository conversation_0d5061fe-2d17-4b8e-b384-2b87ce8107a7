import { render } from '@testing-library/react'
import type { RenderOptions } from '@testing-library/react'
import { vi } from 'vitest'
import React from 'react'

// Calendar-specific testing wrapper
interface CalendarWrapperProps {
  children: React.ReactNode
}

const CalendarWrapper: React.FC<CalendarWrapperProps> = ({ children }) => (
  <div data-testid="calendar-test-wrapper">{children}</div>
)

// Custom render function for calendar components
export const renderCalendar = (ui: React.ReactElement, options?: RenderOptions) => {
  return render(ui, {
    wrapper: CalendarWrapper,
    ...options,
  })
}

// Mock date utilities for consistent testing
export const mockDate = new Date('2024-01-15T10:00:00.000Z')

// Helper to mock Date.now()
export const mockDateNow = () => {
  vi.spyOn(Date, 'now').mockReturnValue(mockDate.getTime())
}

// Helper to restore Date.now()
export const restoreDateNow = () => {
  vi.restoreAllMocks()
}

// Mock window.prompt for calendar interactions
export const mockPrompt = (returnValue: string | null = null) => {
  const promptSpy = vi.spyOn(window, 'prompt')
  promptSpy.mockReturnValue(returnValue)
  return promptSpy
}

// Mock window.confirm for calendar interactions
export const mockConfirm = (returnValue: boolean = true) => {
  const confirmSpy = vi.spyOn(window, 'confirm')
  confirmSpy.mockReturnValue(returnValue)
  return confirmSpy
}

// Helper to wait for async operations
export const waitForAsync = () => new Promise((resolve) => setTimeout(resolve, 0))

// Helper to create mock calendar events
export const createMockCalendarEvent = (overrides: Partial<any> = {}) => ({
  id: 'test-event-1',
  title: 'Test Event',
  start: new Date('2024-01-15T10:00:00.000Z'),
  end: new Date('2024-01-15T11:00:00.000Z'),
  allDay: false,
  type: 'other' as const,
  description: 'Test event description',
  ...overrides,
})

// Helper to create mock calendar container props
export const createMockCalendarContainerProps = (overrides: Partial<any> = {}) => ({
  children: <div data-testid="calendar-content">Calendar Content</div>,
  title: 'Test Calendar',
  currentEvents: [],
  currentDate: mockDate,
  onTodayClick: vi.fn(),
  onCreateEventClick: vi.fn(),
  onViewChange: vi.fn(),
  className: '',
  ...overrides,
})

// Helper to create mock FullCalendar props
export const createMockFullCalendarProps = (overrides: Partial<any> = {}) => ({
  events: [],
  config: {
    defaultView: 'dayGridMonth' as const,
    editable: true,
    selectable: true,
  },
  onDateSelect: vi.fn(),
  onEventClick: vi.fn(),
  onEventsSet: vi.fn(),
  renderEventContent: vi.fn(),
  loading: false,
  error: null,
  ...overrides,
})
