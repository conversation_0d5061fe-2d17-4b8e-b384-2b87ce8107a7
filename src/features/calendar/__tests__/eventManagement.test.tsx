import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { EventDetails } from '../components/EventDetails'
import { EventFilters } from '../components/EventFilters'
import { EventContextMenu } from '../components/EventContextMenu'
import { EventDialog } from '../components/EventDialog'
import { useKeyboardShortcuts } from '../hooks/useKeyboardShortcuts'
import type { CalendarEvent, EventStatus } from '../types'

// Mock calendar event for testing
const mockEvent: CalendarEvent = {
  id: 'test-event-1',
  title: 'Test Training Session',
  start: '2024-01-15T10:00:00',
  end: '2024-01-15T12:00:00',
  allDay: false,
  type: 'training_session',
  status: 'scheduled',
  priority: 'medium',
  description: 'This is a test training session',
  location: 'Conference Room A',
  instructor: '<PERSON>',
  createdAt: '2024-01-10T09:00:00',
  updatedAt: '2024-01-10T09:00:00',
}

describe('Event Management Components', () => {
  describe('EventDetails', () => {
    const mockHandlers = {
      onEdit: vi.fn(),
      onDuplicate: vi.fn(),
      onDelete: vi.fn(),
      onStatusChange: vi.fn(),
      onClose: vi.fn(),
    }

    it('renders event information correctly', () => {
      render(<EventDetails event={mockEvent} {...mockHandlers} />)

      expect(screen.getByText('Test Training Session')).toBeInTheDocument()
      expect(screen.getByText('Training Session')).toBeInTheDocument()
      expect(screen.getByText('Medium')).toBeInTheDocument()
      expect(screen.getByText('Conference Room A')).toBeInTheDocument()
      expect(screen.getByText('John Doe')).toBeInTheDocument()
      expect(screen.getByText('This is a test training session')).toBeInTheDocument()
    })

    it('calls edit handler when edit button is clicked', () => {
      render(<EventDetails event={mockEvent} {...mockHandlers} />)

      fireEvent.click(screen.getByText('Edit'))
      expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockEvent)
    })

    it('calls duplicate handler when duplicate button is clicked', () => {
      render(<EventDetails event={mockEvent} {...mockHandlers} />)

      fireEvent.click(screen.getByText('Duplicate'))
      expect(mockHandlers.onDuplicate).toHaveBeenCalledWith(mockEvent)
    })

    it('calls status change handler when status is changed', async () => {
      render(<EventDetails event={mockEvent} {...mockHandlers} />)

      // Open the more options menu
      fireEvent.click(screen.getByRole('button', { name: /more/i }))

      // Click on "Mark as Completed"
      await waitFor(() => {
        fireEvent.click(screen.getByText('Mark as Completed'))
      })

      expect(mockHandlers.onStatusChange).toHaveBeenCalledWith(mockEvent, 'completed')
    })
  })

  describe('EventFilters', () => {
    const mockFilter = {
      type: ['training_session'],
      status: ['scheduled'],
      priority: ['medium'],
    }

    const mockOnFilterChange = vi.fn()

    it('renders filter controls correctly', () => {
      render(
        <EventFilters
          filter={mockFilter}
          onFilterChange={mockOnFilterChange}
          events={[mockEvent]}
        />
      )

      expect(screen.getByPlaceholderText(/Search events/i)).toBeInTheDocument()
      expect(screen.getByText('Training Session')).toBeInTheDocument()
      expect(screen.getByText('Scheduled')).toBeInTheDocument()
      expect(screen.getByText('Medium')).toBeInTheDocument()
    })

    it('calls filter change handler when search is typed', () => {
      render(<EventFilters filter={{}} onFilterChange={mockOnFilterChange} events={[mockEvent]} />)

      const searchInput = screen.getByPlaceholderText(/Search events/i)
      fireEvent.change(searchInput, { target: { value: 'test search' } })

      expect(mockOnFilterChange).toHaveBeenCalledWith({
        instructor: 'test search',
        location: 'test search',
      })
    })

    it('shows active filters when filters are applied', () => {
      render(
        <EventFilters
          filter={mockFilter}
          onFilterChange={mockOnFilterChange}
          events={[mockEvent]}
        />
      )

      expect(screen.getByText(/Type: Training Session/i)).toBeInTheDocument()
      expect(screen.getByText(/Status: Scheduled/i)).toBeInTheDocument()
      expect(screen.getByText(/Priority: Medium/i)).toBeInTheDocument()
    })
  })

  describe('EventContextMenu', () => {
    const mockHandlers = {
      onEdit: vi.fn(),
      onDuplicate: vi.fn(),
      onDelete: vi.fn(),
      onStatusChange: vi.fn(),
      onMove: vi.fn(),
    }

    it('renders context menu items correctly', () => {
      render(
        <EventContextMenu event={mockEvent} {...mockHandlers}>
          <button>Open Menu</button>
        </EventContextMenu>
      )

      fireEvent.click(screen.getByText('Open Menu'))

      expect(screen.getByText('Edit Event')).toBeInTheDocument()
      expect(screen.getByText('Duplicate')).toBeInTheDocument()
      expect(screen.getByText('Move to')).toBeInTheDocument()
      expect(screen.getByText('Change Status')).toBeInTheDocument()
      expect(screen.getByText('Delete Event')).toBeInTheDocument()
    })

    it('calls edit handler when edit is clicked', async () => {
      render(
        <EventContextMenu event={mockEvent} {...mockHandlers}>
          <button>Open Menu</button>
        </EventContextMenu>
      )

      fireEvent.click(screen.getByText('Open Menu'))
      await waitFor(() => {
        fireEvent.click(screen.getByText('Edit Event'))
      })

      expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockEvent)
    })

    it('calls duplicate handler with offset when duplicate option is selected', async () => {
      render(
        <EventContextMenu event={mockEvent} {...mockHandlers}>
          <button>Open Menu</button>
        </EventContextMenu>
      )

      fireEvent.click(screen.getByText('Open Menu'))

      // Open duplicate submenu
      await waitFor(() => {
        fireEvent.click(screen.getByText('Duplicate'))
      })

      // Click on "Tomorrow" option
      await waitFor(() => {
        fireEvent.click(screen.getByText('Tomorrow'))
      })

      expect(mockHandlers.onDuplicate).toHaveBeenCalledWith(mockEvent, 1)
    })
  })

  describe('EventDialog', () => {
    const mockHandlers = {
      onOpenChange: vi.fn(),
      onSubmit: vi.fn(),
    }

    it('renders create dialog correctly', () => {
      render(
        <EventDialog
          open={true}
          onOpenChange={mockHandlers.onOpenChange}
          mode="create"
          onSubmit={mockHandlers.onSubmit}
        />
      )

      expect(screen.getByText('Create New Event')).toBeInTheDocument()
      expect(screen.getByPlaceholderText(/Enter event title/i)).toBeInTheDocument()
    })

    it('renders edit dialog correctly with pre-filled data', () => {
      render(
        <EventDialog
          open={true}
          onOpenChange={mockHandlers.onOpenChange}
          mode="edit"
          event={mockEvent}
          onSubmit={mockHandlers.onSubmit}
        />
      )

      expect(screen.getByText('Edit Event')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Test Training Session')).toBeInTheDocument()
      expect(screen.getByDisplayValue('This is a test training session')).toBeInTheDocument()
      expect(screen.getByDisplayValue('Conference Room A')).toBeInTheDocument()
      expect(screen.getByDisplayValue('John Doe')).toBeInTheDocument()
    })

    it('calls submit handler when form is submitted', async () => {
      const mockSubmitData = {
        title: 'New Event Title',
        description: 'New Description',
        type: 'meeting' as const,
        location: 'New Location',
        instructor: 'New Instructor',
        priority: 'high' as const,
      }

      render(
        <EventDialog
          open={true}
          onOpenChange={mockHandlers.onOpenChange}
          mode="create"
          onSubmit={mockHandlers.onSubmit}
        />
      )

      fireEvent.change(screen.getByPlaceholderText(/Enter event title/i), {
        target: { value: mockSubmitData.title },
      })

      fireEvent.click(screen.getByText('Create Event'))

      await waitFor(() => {
        expect(mockHandlers.onSubmit).toHaveBeenCalledWith(mockSubmitData)
      })
    })
  })

  describe('useKeyboardShortcuts', () => {
    it('provides keyboard shortcuts functionality', () => {
      const mockHandlers = {
        onCreateEvent: vi.fn(),
        onEditEvent: vi.fn(),
        onDeleteEvent: vi.fn(),
        onDuplicateEvent: vi.fn(),
        onToggleWeekends: vi.fn(),
        onGoToToday: vi.fn(),
        onChangeView: vi.fn(),
        onSearch: vi.fn(),
      }

      let result: any
      const TestComponent = () => {
        result = useKeyboardShortcuts({
          ...mockHandlers,
          selectedEvent: mockEvent,
          enabled: true,
        })
        return null
      }

      render(<TestComponent />)

      expect(result).toBeDefined()
      expect(typeof result.getAvailableShortcuts).toBe('function')
    })
  })
})

describe('Event Operations Integration', () => {
  it('handles complete event lifecycle', async () => {
    const mockHandlers = {
      onEdit: vi.fn(),
      onDuplicate: vi.fn(),
      onDelete: vi.fn(),
      onStatusChange: vi.fn(),
      onClose: vi.fn(),
    }

    // Start with event details
    const { rerender } = render(<EventDetails event={mockEvent} {...mockHandlers} />)

    // Edit the event
    fireEvent.click(screen.getByText('Edit'))
    expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockEvent)

    // Simulate status change
    mockHandlers.onStatusChange(mockEvent, 'in_progress')
    expect(mockHandlers.onStatusChange).toHaveBeenCalledWith(mockEvent, 'in_progress')

    // Complete the event
    mockHandlers.onStatusChange(mockEvent, 'completed')
    expect(mockHandlers.onStatusChange).toHaveBeenCalledWith(mockEvent, 'completed')

    // Duplicate the event
    fireEvent.click(screen.getByText('Duplicate'))
    expect(mockHandlers.onDuplicate).toHaveBeenCalledWith(mockEvent)
  })

  it('handles filtering and search operations', () => {
    const mockOnFilterChange = vi.fn()
    const events = [mockEvent]

    render(<EventFilters filter={{}} onFilterChange={mockOnFilterChange} events={events} />)

    // Test search
    const searchInput = screen.getByPlaceholderText(/Search events/i)
    fireEvent.change(searchInput, { target: { value: 'training' } })
    expect(mockOnFilterChange).toHaveBeenCalledWith({
      instructor: 'training',
      location: 'training',
    })

    // Test type filter
    fireEvent.click(screen.getByText('Training Session'))
    expect(mockOnFilterChange).toHaveBeenCalled()
  })
})
