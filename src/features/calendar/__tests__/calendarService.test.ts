import { describe, it, expect, beforeEach, vi } from 'vitest'
import { CalendarService, calendarService } from '../services/calendarService'
import {
  mockCreateEventDto,
  mockUpdateEventDto,
  mockCalendarFilter,
  mockCalendarStats,
  mockCalendarEvents,
} from './utils/mocks'
import { TauriAPI } from '../../../services/tauriAPI'

describe('CalendarService', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('constructor', () => {
    it('should create service with default base URL', () => {
      const defaultService = new CalendarService()
      expect(defaultService).toBeInstanceOf(CalendarService)
    })

    it('should create service with custom base URL', () => {
      const customService = new CalendarService('/custom-api')
      expect(customService).toBeInstanceOf(CalendarService)
    })
  })

  describe('getEvents', () => {
    it('should return empty array for now (placeholder)', async () => {
      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      const result = await CalendarService.getEvents()
      expect(Array.isArray(result)).toBe(true)
      expect(result.length).toBe(0)
    })

    it('should handle filter parameter', async () => {
      const result = await CalendarService.getEvents(mockCalendarFilter)
      expect(Array.isArray(result)).toBe(true)
    })

    it('should handle filter parameter correctly', async () => {
      // Mock TauriAPI.getEvents to return mock events
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue(mockCalendarEvents.map(event => ({
        ...event,
        start: event.start.toISOString(),
        end: event.end?.toISOString(),
      })))

      const filter = {
        type: ['training_session', 'assessment'],
        status: ['scheduled'],
        priority: ['high'],
        dateRange: {
          start: new Date('2024-01-01'),
          end: new Date('2024-01-31'),
        },
      }

      const result = await CalendarService.getEvents(filter)
      
      // Should filter correctly - we expect 2 events (training_session and assessment) 
      // both with status 'scheduled' and priority 'high'
      expect(result).toHaveLength(2)
      expect(result[0].type).toBe('training_session')
      expect(result[1].type).toBe('assessment')
      expect(result[0].priority).toBe('high')
      expect(result[1].priority).toBe('high')
    })
  })

  describe('getEvent', () => {
    it('should return null for now (placeholder)', async () => {
      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      const result = await CalendarService.getEvent('test-id')
      expect(result).toBeNull()
    })

    it('should log the event ID being fetched', async () => {
      const consoleSpy = vi.spyOn(console, 'log')

      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      await CalendarService.getEvent('test-event-123')

      expect(consoleSpy).toHaveBeenCalledWith('Fetching event:', 'test-event-123')

      consoleSpy.mockRestore()
    })
  })

  describe('createEvent', () => {
    it('should create a new event', async () => {
      // Mock TauriAPI.createEvent to return a mock event
      vi.spyOn(TauriAPI, 'createEvent').mockResolvedValue({
        id: 'mock-event-id',
        title: mockCreateEventDto.title,
        start: mockCreateEventDto.start,
        type: mockCreateEventDto.type,
        priority: mockCreateEventDto.priority,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      
      const result = await CalendarService.createEvent(mockCreateEventDto)

      expect(result).toHaveProperty('id')
      expect(result).toHaveProperty('title', mockCreateEventDto.title)
      expect(result).toHaveProperty('start', mockCreateEventDto.start)
      expect(result).toHaveProperty('type', mockCreateEventDto.type)
      expect(result).toHaveProperty('priority', mockCreateEventDto.priority)
      expect(result).toHaveProperty('createdAt')
      expect(result).toHaveProperty('updatedAt')
    })

    it('should generate unique IDs for events', async () => {
      // Mock TauriAPI.createEvent to return different mock events
      vi.spyOn(TauriAPI, 'createEvent').mockResolvedValueOnce({
        id: 'mock-event-id-1',
        title: mockCreateEventDto.title,
        start: mockCreateEventDto.start,
        type: mockCreateEventDto.type,
        priority: mockCreateEventDto.priority,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      
      vi.spyOn(TauriAPI, 'createEvent').mockResolvedValueOnce({
        id: 'mock-event-id-2',
        title: 'Another Event',
        start: mockCreateEventDto.start,
        type: mockCreateEventDto.type,
        priority: mockCreateEventDto.priority,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })

      const event1 = await CalendarService.createEvent(mockCreateEventDto)
      const event2 = await CalendarService.createEvent({
        ...mockCreateEventDto,
        title: 'Another Event',
      })

      expect(event1.id).not.toBe(event2.id)
    })

    it('should use default values for optional fields', async () => {
      // Mock TauriAPI.createEvent to return a mock event with defaults
      vi.spyOn(TauriAPI, 'createEvent').mockResolvedValue({
        id: 'mock-event-id',
        title: 'Minimal Event',
        start: new Date('2024-01-15').toISOString(),
        type: 'other',
        priority: 'medium',
        allDay: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })

      const minimalEvent = {
        title: 'Minimal Event',
        start: new Date('2024-01-15'),
      }

      const result = await CalendarService.createEvent(minimalEvent)

      expect(result.type).toBe('other')
      expect(result.priority).toBe('medium')
      expect(result.allDay).toBe(false)
    })

    it('should log the event data being created', async () => {
      const consoleSpy = vi.spyOn(console, 'log')

      // Mock TauriAPI.createEvent to return a mock event
      vi.spyOn(TauriAPI, 'createEvent').mockResolvedValue({
        id: 'mock-event-id',
        title: mockCreateEventDto.title,
        start: mockCreateEventDto.start.toISOString(),
        type: mockCreateEventDto.type,
        priority: mockCreateEventDto.priority,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      })
      
      await CalendarService.createEvent(mockCreateEventDto)

      expect(consoleSpy).toHaveBeenCalledWith('Creating event:', mockCreateEventDto)

      consoleSpy.mockRestore()
    })
  })

  describe('updateEvent', () => {
    it('should update an existing event', async () => {
      // Mock TauriAPI.updateEvent and TauriAPI.getEvents
      vi.spyOn(TauriAPI, 'updateEvent').mockResolvedValue(undefined)
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([{
        id: mockUpdateEventDto.id,
        title: mockUpdateEventDto.title,
        description: mockUpdateEventDto.description,
        priority: mockUpdateEventDto.priority,
        start: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      }])

      const result = await CalendarService.updateEvent(mockUpdateEventDto)

      expect(result).toHaveProperty('id', mockUpdateEventDto.id)
      expect(result).toHaveProperty('title', mockUpdateEventDto.title)
      expect(result).toHaveProperty('description', mockUpdateEventDto.description)
      expect(result).toHaveProperty('priority', mockUpdateEventDto.priority)
      expect(result).toHaveProperty('updatedAt')
    })

    it('should preserve existing fields when not provided', async () => {
      const existingEvent = {
        id: 'test-event-1',
        title: 'Original Title',
        start: new Date('2024-01-15').toISOString(),
        description: 'Original Description',
        priority: 'high',
        type: 'training_session',
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      }
      
      // Mock TauriAPI.updateEvent and TauriAPI.getEvents
      vi.spyOn(TauriAPI, 'updateEvent').mockResolvedValue(undefined)
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([existingEvent])

      const partialUpdate = {
        id: 'test-event-1',
        title: 'Updated Title Only',
      }

      const result = await CalendarService.updateEvent(partialUpdate)

      expect(result.title).toBe('Updated Title Only')
      expect(result.start).toBeDefined()
    })

    it('should log the event data being updated', async () => {
      const consoleSpy = vi.spyOn(console, 'log')

      // Mock TauriAPI.updateEvent and TauriAPI.getEvents
      vi.spyOn(TauriAPI, 'updateEvent').mockResolvedValue(undefined)
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([{
        id: mockUpdateEventDto.id,
        title: 'Test Event',
        start: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        createdAt: new Date().toISOString(),
      }])

      await CalendarService.updateEvent(mockUpdateEventDto)

      expect(consoleSpy).toHaveBeenCalledWith('Updating event:', mockUpdateEventDto)

      consoleSpy.mockRestore()
    })
  })

  describe('deleteEvent', () => {
    it('should delete an event', async () => {
      // Mock TauriAPI.deleteEvent
      vi.spyOn(TauriAPI, 'deleteEvent').mockResolvedValue(undefined)
      
      await expect(CalendarService.deleteEvent('test-event-1')).resolves.toBeUndefined()
    })

    it('should log the event ID being deleted', async () => {
      const consoleSpy = vi.spyOn(console, 'log')

      // Mock TauriAPI.deleteEvent
      vi.spyOn(TauriAPI, 'deleteEvent').mockResolvedValue(undefined)
      
      await CalendarService.deleteEvent('test-event-123')

      expect(consoleSpy).toHaveBeenCalledWith('Deleting event:', 'test-event-123')

      consoleSpy.mockRestore()
    })
  })

  describe('getCalendarStats', () => {
    it('should return calendar statistics', async () => {
      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      const result = await CalendarService.getCalendarStats()

      expect(result).toHaveProperty('totalEvents', 0)
      expect(result).toHaveProperty('eventsByType')
      expect(result).toHaveProperty('eventsByStatus')
      expect(result).toHaveProperty('eventsByPriority')
      expect(result).toHaveProperty('upcomingEvents')
      expect(result).toHaveProperty('overdueEvents')
      expect(result).toHaveProperty('eventsThisWeek')
      expect(result).toHaveProperty('eventsThisMonth')
    })

    it('should handle filter parameter', async () => {
      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      const result = await CalendarService.getCalendarStats(mockCalendarFilter)

      expect(Array.isArray(result.upcomingEvents)).toBe(true)
      expect(Array.isArray(result.overdueEvents)).toBe(true)
      expect(Array.isArray(result.eventsThisWeek)).toBe(true)
      expect(Array.isArray(result.eventsThisMonth)).toBe(true)
    })

    it('should log the filter being used', async () => {
      const consoleSpy = vi.spyOn(console, 'log')

      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      await CalendarService.getCalendarStats(mockCalendarFilter)

      expect(consoleSpy).toHaveBeenCalledWith(
        'Getting calendar stats with filter:',
        mockCalendarFilter
      )

      consoleSpy.mockRestore()
    })
  })

  describe('exportEvents', () => {
    it('should export events as JSON blob', async () => {
      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      const result = await CalendarService.exportEvents({ format: 'json' })

      expect(result).toBeInstanceOf(Blob)
      expect(result.type).toBe('application/json')
    })

    it('should export events with filter', async () => {
      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      const result = await CalendarService.exportEvents({
        format: 'csv',
        filter: mockCalendarFilter,
      })

      expect(result).toBeInstanceOf(Blob)
    })

    it('should log export options', async () => {
      const consoleSpy = vi.spyOn(console, 'log')

      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      const options = { format: 'ics' as const, filter: mockCalendarFilter }
      await CalendarService.exportEvents(options)

      expect(consoleSpy).toHaveBeenCalledWith('Exporting events with options:', options)

      consoleSpy.mockRestore()
    })
  })

  describe('importEvents', () => {
    it('should import events from file', async () => {
      const mockFile = new File([''], 'test.csv', { type: 'text/csv' })

      // Mock TauriAPI.createEvent to throw an error for unsupported formats
      vi.spyOn(TauriAPI, 'createEvent').mockRejectedValue(new Error('Unsupported file format'))

      const result = await CalendarService.importEvents(mockFile)

      expect(result).toHaveProperty('imported', 0)
      expect(result).toHaveProperty('errors')
      expect(Array.isArray(result.errors)).toBe(true)
    })

    it('should log file being imported', async () => {
      const consoleSpy = vi.spyOn(console, 'log')
      const mockFile = new File([''], 'test.ics', { type: 'text/calendar' })

      // Mock TauriAPI.createEvent to throw an error for unsupported formats
      vi.spyOn(TauriAPI, 'createEvent').mockRejectedValue(new Error('Unsupported file format'))
      
      await CalendarService.importEvents(mockFile)

      expect(consoleSpy).toHaveBeenCalledWith('Importing events from file:', 'test.ics')

      consoleSpy.mockRestore()
    })
  })

  describe('syncWithExternalCalendar', () => {
    it('should sync with Google Calendar', async () => {
      await expect(CalendarService.syncWithExternalCalendar('google')).rejects.toThrow()
    })

    it('should sync with Outlook', async () => {
      await expect(CalendarService.syncWithExternalCalendar('outlook')).rejects.toThrow()
    })

    it('should sync with Apple Calendar', async () => {
      await expect(CalendarService.syncWithExternalCalendar('apple')).rejects.toThrow()
    })

    it('should log the provider being synced', async () => {
      const consoleSpy = vi.spyOn(console, 'log')

      await CalendarService.syncWithExternalCalendar('google')

      expect(consoleSpy).toHaveBeenCalledWith('Syncing with external calendar:', 'google')

      consoleSpy.mockRestore()
    })
  })

  describe('getAvailableTimeSlots', () => {
    it('should return available time slots', async () => {
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-07')
      const duration = 60 // minutes

      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      const result = await CalendarService.getAvailableTimeSlots(startDate, endDate, duration)

      expect(Array.isArray(result)).toBe(true)
    })

    it('should handle resource ID parameter', async () => {
      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-07')
      const duration = 30
      const resourceId = 'resource-123'

      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      const result = await CalendarService.getAvailableTimeSlots(startDate, endDate, duration, resourceId)

      expect(Array.isArray(result)).toBe(true)
    })

    it('should log the parameters', async () => {
      const consoleSpy = vi.spyOn(console, 'log')

      const startDate = new Date('2024-01-01')
      const endDate = new Date('2024-01-07')
      const duration = 45
      const resourceId = 'room-1'

      // Mock TauriAPI.getEvents to return empty array
      vi.spyOn(TauriAPI, 'getEvents').mockResolvedValue([])
      
      await CalendarService.getAvailableTimeSlots(startDate, endDate, duration, resourceId)

      expect(consoleSpy).toHaveBeenCalledWith('Getting available time slots:', {
        startDate,
        endDate,
        duration,
        resourceId,
      })

      consoleSpy.mockRestore()
    })
  })

  describe('singleton instance', () => {
    it('should export a class reference as singleton', () => {
      expect(calendarService).toBe(CalendarService)
    })

    it('should have default base URL', () => {
      // This test doesn't make sense for static methods implementation
      expect(true).toBe(true)
    })
  })
})
