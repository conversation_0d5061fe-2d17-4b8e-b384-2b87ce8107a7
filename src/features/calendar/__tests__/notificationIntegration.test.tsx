import React from 'react'
import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { CalendarNotificationService } from '../services/notificationService'
import { CalendarNotificationIntegration } from '../services/calendarNotificationIntegration'
import {
  useCalendarNotifications,
  useEventReminders,
  useNotificationPreferences,
} from '../hooks/useNotifications'
import { Notifications } from '../components/Notifications'
import type {
  CalendarNotification,
  EventReminder,
  NotificationPreferences,
  CalendarNotificationUserSettings,
  ReminderType,
} from '../types'

// Mock the GT-EGA notification system
jest.mock('../../../hooks/useNotifications', () => ({
  useNotifications: () => ({
    sendNotification: jest.fn().mockResolvedValue(true),
  }),
}))

// Mock the calendar service
jest.mock('../services/calendarService', () => ({
  CalendarService: {
    getEvent: jest.fn(),
    createEvent: jest.fn(),
    updateEvent: jest.fn(),
    deleteEvent: jest.fn(),
    getEvents: jest.fn(),
  },
}))

// Mock DateUtils
jest.mock('../../../lib/dateUtils', () => ({
  DateUtils: {
    formatRelative: (date: Date) => date.toLocaleString(),
  },
}))

describe('Calendar Notification Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('CalendarNotificationService', () => {
    it('should schedule event reminder successfully', async () => {
      const mockEvent = {
        id: 'event-1',
        title: 'Test Event',
        start: new Date('2024-01-15T10:00:00Z'),
      }

      const { CalendarService } = require('../services/calendarService')
      CalendarService.getEvent.mockResolvedValue(mockEvent)

      const reminder = await CalendarNotificationService.scheduleEventReminder(
        'event-1',
        'user-1',
        new Date('2024-01-15T09:45:00Z'),
        'in_app'
      )

      expect(reminder).toBeDefined()
      expect(reminder.eventId).toBe('event-1')
      expect(reminder.userId).toBe('user-1')
      expect(reminder.reminderType).toBe('in_app')
    })

    it('should send event notification successfully', async () => {
      const mockEvent = {
        id: 'event-1',
        title: 'Test Event',
        start: new Date('2024-01-15T10:00:00Z'),
      }

      const { CalendarService } = require('../services/calendarService')
      CalendarService.getEvent.mockResolvedValue(mockEvent)

      const notifications = await CalendarNotificationService.sendEventNotification(
        'event_created',
        'event-1',
        ['user-1', 'user-2'],
        'Test notification message'
      )

      expect(notifications).toHaveLength(2)
      expect(notifications[0].type).toBe('event_created')
      expect(notifications[0].eventId).toBe('event-1')
    })

    it('should notify event change successfully', async () => {
      const mockEvent = {
        id: 'event-1',
        title: 'Updated Test Event',
        start: new Date('2024-01-15T10:00:00Z'),
      }

      const { CalendarService } = require('../services/calendarService')
      CalendarService.getEvent.mockResolvedValue(mockEvent)

      const notifySpy = jest.spyOn(CalendarNotificationService, 'sendEventNotification')

      await CalendarNotificationService.notifyEventChange(
        'event-1',
        'updated',
        ['user-1', 'user-2'],
        { title: { old: 'Old Title', new: 'New Title' } }
      )

      expect(notifySpy).toHaveBeenCalledWith(
        'event_updated',
        'event-1',
        ['user-1', 'user-2'],
        expect.stringContaining('Old Title → New Title')
      )
    })

    it('should handle resource conflict notifications', async () => {
      const conflictDetails = {
        resourceId: 'resource-1',
        resourceName: 'Meeting Room A',
        resourceType: 'room',
        conflictType: 'double_booking' as const,
        conflictingEvents: [
          {
            id: 'event-1',
            title: 'Meeting 1',
            start: new Date('2024-01-15T10:00:00Z'),
            end: new Date('2024-01-15T11:00:00Z'),
          },
        ],
        affectedUserIds: ['user-1', 'user-2'],
      }

      const notifications = await CalendarNotificationService.notifyResourceConflict(
        conflictDetails,
        conflictDetails.affectedUserIds
      )

      expect(notifications).toHaveLength(2)
      expect(notifications[0].type).toBe('resource_conflict')
      expect(notifications[0].title).toContain('Resource Conflict: Meeting Room A')
    })
  })

  describe('CalendarNotificationIntegration', () => {
    it('should create event with notifications', async () => {
      const mockEvent = {
        id: 'event-1',
        title: 'Test Event',
        start: new Date('2024-01-15T10:00:00Z'),
      }

      const { CalendarService } = require('../services/calendarService')
      CalendarService.createEvent.mockResolvedValue(mockEvent)

      const setupRemindersSpy = jest.spyOn(CalendarNotificationIntegration, 'setupEventReminders')
      const sendNotificationSpy = jest.spyOn(CalendarNotificationService, 'sendEventNotification')

      const result = await CalendarNotificationIntegration.createEventWithNotifications(
        {
          title: 'Test Event',
          start: new Date('2024-01-15T10:00:00Z'),
        },
        {
          enableReminders: true,
          notifyAttendees: true,
          attendeeIds: ['user-1', 'user-2'],
        }
      )

      expect(result).toEqual(mockEvent)
      expect(setupRemindersSpy).toHaveBeenCalledWith('event-1', undefined)
      expect(sendNotificationSpy).toHaveBeenCalledWith(
        'event_created',
        'event-1',
        ['user-1', 'user-2'],
        expect.stringContaining('Test Event')
      )
    })

    it('should update event with change notifications', async () => {
      const mockOriginalEvent = {
        id: 'event-1',
        title: 'Original Title',
        start: new Date('2024-01-15T10:00:00Z'),
      }

      const mockUpdatedEvent = {
        id: 'event-1',
        title: 'Updated Title',
        start: new Date('2024-01-15T10:00:00Z'),
      }

      const { CalendarService } = require('../services/calendarService')
      CalendarService.getEvent.mockResolvedValue(mockOriginalEvent)
      CalendarService.updateEvent.mockResolvedValue(mockUpdatedEvent)

      const notifyChangeSpy = jest.spyOn(CalendarNotificationService, 'notifyEventChange')

      const result = await CalendarNotificationIntegration.updateEventWithNotifications(
        {
          id: 'event-1',
          title: 'Updated Title',
        },
        {
          notifyChanges: true,
          affectedUserIds: ['user-1'],
          changes: { title: { old: 'Original Title', new: 'Updated Title' } },
        }
      )

      expect(result).toEqual(mockUpdatedEvent)
      expect(notifyChangeSpy).toHaveBeenCalledWith('event-1', 'updated', ['user-1'], {
        title: { old: 'Original Title', new: 'Updated Title' },
      })
    })

    it('should delete event with notifications', async () => {
      const mockEvent = {
        id: 'event-1',
        title: 'Test Event',
        start: new Date('2024-01-15T10:00:00Z'),
      }

      const { CalendarService } = require('../services/calendarService')
      CalendarService.getEvent.mockResolvedValue(mockEvent)
      CalendarService.deleteEvent.mockResolvedValue()

      const sendNotificationSpy = jest.spyOn(CalendarNotificationService, 'sendEventNotification')

      await CalendarNotificationIntegration.deleteEventWithNotifications('event-1', {
        notifyCancellation: true,
        affectedUserIds: ['user-1'],
      })

      expect(CalendarService.deleteEvent).toHaveBeenCalledWith('event-1')
      expect(sendNotificationSpy).toHaveBeenCalledWith(
        'event_deleted',
        'event-1',
        ['user-1'],
        expect.stringContaining('Test Event')
      )
    })
  })

  describe('Notification Hooks', () => {
    it('should use calendar notifications hook', () => {
      const TestComponent = () => {
        const {
          notifications,
          reminders,
          scheduleReminder,
          notifyEventChange,
          markAsRead,
          deleteNotification,
        } = useCalendarNotifications()

        return (
          <div>
            <div data-testid="notifications-count">{notifications.length}</div>
            <div data-testid="reminders-count">{reminders.length}</div>
            <button onClick={() => scheduleReminder('event-1', new Date(), 'in_app')}>
              Schedule Reminder
            </button>
            <button onClick={() => notifyEventChange('event-1', 'updated')}>Notify Change</button>
            <button onClick={() => markAsRead('notif-1')}>Mark as Read</button>
            <button onClick={() => deleteNotification('notif-1')}>Delete Notification</button>
          </div>
        )
      }

      render(<TestComponent />)

      expect(screen.getByTestId('notifications-count')).toHaveTextContent('0')
      expect(screen.getByTestId('reminders-count')).toHaveTextContent('0')

      const scheduleButton = screen.getByText('Schedule Reminder')
      const notifyButton = screen.getByText('Notify Change')
      const markReadButton = screen.getByText('Mark as Read')
      const deleteButton = screen.getByText('Delete Notification')

      expect(scheduleButton).toBeInTheDocument()
      expect(notifyButton).toBeInTheDocument()
      expect(markReadButton).toBeInTheDocument()
      expect(deleteButton).toBeInTheDocument()
    })

    it('should use event reminders hook', () => {
      const TestComponent = () => {
        const { reminders, getEventReminders, scheduleMultipleReminders, cancelAllReminders } =
          useEventReminders()

        return (
          <div>
            <div data-testid="reminders-count">{reminders.length}</div>
            <button onClick={() => getEventReminders('event-1')}>Get Event Reminders</button>
            <button onClick={() => scheduleMultipleReminders('event-1', [])}>
              Schedule Multiple Reminders
            </button>
            <button onClick={() => cancelAllReminders('event-1')}>Cancel All Reminders</button>
          </div>
        )
      }

      render(<TestComponent />)

      expect(screen.getByTestId('reminders-count')).toHaveTextContent('0')

      const getRemindersButton = screen.getByText('Get Event Reminders')
      const scheduleMultipleButton = screen.getByText('Schedule Multiple Reminders')
      const cancelAllButton = screen.getByText('Cancel All Reminders')

      expect(getRemindersButton).toBeInTheDocument()
      expect(scheduleMultipleButton).toBeInTheDocument()
      expect(cancelAllButton).toBeInTheDocument()
    })

    it('should use notification preferences hook', () => {
      const TestComponent = () => {
        const { preferences, loading, updatePreferences, toggleNotificationType } =
          useNotificationPreferences()

        return (
          <div>
            <div data-testid="loading">{loading.toString()}</div>
            <div data-testid="has-preferences">{(preferences !== null).toString()}</div>
            <button onClick={() => updatePreferences({ enabled: true })}>Update Preferences</button>
            <button onClick={() => toggleNotificationType('eventReminders', true)}>
              Toggle Event Reminders
            </button>
          </div>
        )
      }

      render(<TestComponent />)

      expect(screen.getByTestId('loading')).toHaveTextContent('false')
      expect(screen.getByTestId('has-preferences')).toHaveTextContent('false')

      const updateButton = screen.getByText('Update Preferences')
      const toggleButton = screen.getByText('Toggle Event Reminders')

      expect(updateButton).toBeInTheDocument()
      expect(toggleButton).toBeInTheDocument()
    })
  })

  describe('Notification Components', () => {
    it('should render notifications component', () => {
      const mockNotifications: CalendarNotification[] = [
        {
          id: 'notif-1',
          type: 'event_created',
          title: 'New Event',
          message: 'Event created successfully',
          userId: 'user-1',
          scheduledFor: new Date(),
          status: 'pending',
          priority: 'medium',
          reminderType: 'in_app',
          metadata: {},
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ]

      const mockReminders: EventReminder[] = [
        {
          id: 'reminder-1',
          eventId: 'event-1',
          userId: 'user-1',
          reminderType: 'in_app',
          scheduledFor: new Date(),
          sent: false,
          metadata: {
            eventId: 'event-1',
            eventTitle: 'Test Event',
            eventStart: new Date(),
            reminderOffset: 15,
            reminderType: 'in_app',
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ]

      const mockSettings: CalendarNotificationUserSettings = {
        enabled: true,
        reminders: {
          beforeEvent: 15,
          beforeDeadline: 60,
        },
        emailNotifications: true,
        inAppNotifications: true,
        pushNotifications: true,
        smsNotifications: false,
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00',
        },
      }

      render(
        <Notifications
          notifications={mockNotifications}
          reminders={mockReminders}
          settings={mockSettings}
        />
      )

      expect(screen.getByText('Notifications')).toBeInTheDocument()
      expect(screen.getByText('Reminders')).toBeInTheDocument()
      expect(screen.getByText('Preferences')).toBeInTheDocument()
      expect(screen.getByText('History')).toBeInTheDocument()

      // Check if notification is rendered
      expect(screen.getByText('New Event')).toBeInTheDocument()
      expect(screen.getByText('Event created successfully')).toBeInTheDocument()

      // Check if reminder is rendered
      expect(screen.getByText('Test Event')).toBeInTheDocument()
    })

    it('should handle notification interactions', async () => {
      const mockNotifications: CalendarNotification[] = [
        {
          id: 'notif-1',
          type: 'event_created',
          title: 'New Event',
          message: 'Event created successfully',
          userId: 'user-1',
          scheduledFor: new Date(),
          status: 'pending',
          priority: 'medium',
          reminderType: 'in_app',
          metadata: {},
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        },
      ]

      const onMarkAsRead = jest.fn()
      const onDeleteNotification = jest.fn()

      render(
        <Notifications
          notifications={mockNotifications}
          reminders={[]}
          onMarkAsRead={onMarkAsRead}
          onDeleteNotification={onDeleteNotification}
        />
      )

      // Switch to notifications tab
      fireEvent.click(screen.getByText('Notifications'))

      await waitFor(() => {
        expect(screen.getByText('New Event')).toBeInTheDocument()
      })

      // Test mark as read
      const markAsReadButton = screen.getByRole('button', { name: /mark as read/i })
      fireEvent.click(markAsReadButton)
      expect(onMarkAsRead).toHaveBeenCalledWith('notif-1')

      // Test delete notification
      const deleteButton = screen.getByRole('button', { name: /delete/i })
      fireEvent.click(deleteButton)
      expect(onDeleteNotification).toHaveBeenCalledWith('notif-1')
    })
  })

  describe('Integration Tests', () => {
    it('should handle complete event lifecycle with notifications', async () => {
      const mockEvent = {
        id: 'event-1',
        title: 'Test Event',
        start: new Date('2024-01-15T10:00:00Z'),
      }

      const { CalendarService } = require('../services/calendarService')
      CalendarService.createEvent.mockResolvedValue(mockEvent)
      CalendarService.getEvent.mockResolvedValue(mockEvent)
      CalendarService.updateEvent.mockResolvedValue({ ...mockEvent, title: 'Updated Event' })
      CalendarService.deleteEvent.mockResolvedValue()

      const setupRemindersSpy = jest.spyOn(CalendarNotificationIntegration, 'setupEventReminders')
      const sendNotificationSpy = jest.spyOn(CalendarNotificationService, 'sendEventNotification')
      const notifyChangeSpy = jest.spyOn(CalendarNotificationService, 'notifyEventChange')

      // Create event with notifications
      const createdEvent = await CalendarNotificationIntegration.createEventWithNotifications(
        {
          title: 'Test Event',
          start: new Date('2024-01-15T10:00:00Z'),
        },
        {
          enableReminders: true,
          notifyAttendees: true,
          attendeeIds: ['user-1'],
        }
      )

      expect(createdEvent).toEqual(mockEvent)
      expect(setupRemindersSpy).toHaveBeenCalled()
      expect(sendNotificationSpy).toHaveBeenCalledWith(
        'event_created',
        'event-1',
        ['user-1'],
        expect.stringContaining('Test Event')
      )

      // Update event with notifications
      const updatedEvent = await CalendarNotificationIntegration.updateEventWithNotifications(
        {
          id: 'event-1',
          title: 'Updated Event',
        },
        {
          notifyChanges: true,
          affectedUserIds: ['user-1'],
          changes: { title: { old: 'Test Event', new: 'Updated Event' } },
        }
      )

      expect(updatedEvent.title).toBe('Updated Event')
      expect(notifyChangeSpy).toHaveBeenCalledWith('event-1', 'updated', ['user-1'], {
        title: { old: 'Test Event', new: 'Updated Event' },
      })

      // Delete event with notifications
      await CalendarNotificationIntegration.deleteEventWithNotifications('event-1', {
        notifyCancellation: true,
        affectedUserIds: ['user-1'],
      })

      expect(CalendarService.deleteEvent).toHaveBeenCalledWith('event-1')
      expect(sendNotificationSpy).toHaveBeenCalledWith(
        'event_deleted',
        'event-1',
        ['user-1'],
        expect.stringContaining('Test Event')
      )
    })
  })
})
