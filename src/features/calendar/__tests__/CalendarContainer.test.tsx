import { describe, it, expect, beforeEach, vi } from 'vitest'
import { render, screen, fireEvent } from '@testing-library/react'
import CalendarContainer from '../components/CalendarContainer'
import { renderCalendar, createMockCalendarContainerProps, mockDate } from './utils/testUtils'
import { mockCalendarEvents, mockTrainingSessionEvent, mockAssessmentEvent } from './utils/mocks'

describe('CalendarContainer', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should render calendar container with default props', () => {
    const mockProps = createMockCalendarContainerProps()

    renderCalendar(<CalendarContainer {...mockProps} />)

    expect(screen.getByTestId('calendar-container')).toBeInTheDocument()
    expect(screen.getByText('Test Calendar')).toBeInTheDocument()
    expect(screen.getByText('January 2024 • 0 events')).toBeInTheDocument()
  })

  it('should render with custom title', () => {
    const mockProps = createMockCalendarContainerProps({
      title: 'Custom Calendar Title',
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    expect(screen.getByText('Custom Calendar Title')).toBeInTheDocument()
  })

  it('should display event count correctly', () => {
    const mockProps = createMockCalendarContainerProps({
      currentEvents: mockCalendarEvents,
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    expect(screen.getByText('January 2024 • 5 events')).toBeInTheDocument()
  })

  it('should display event type summary when events exist', () => {
    const mockProps = createMockCalendarContainerProps({
      currentEvents: [mockTrainingSessionEvent, mockAssessmentEvent],
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    expect(screen.getByText('Event Types:')).toBeInTheDocument()
    expect(screen.getByText('Training Session: 1')).toBeInTheDocument()
    expect(screen.getByText('Assessment: 1')).toBeInTheDocument()
  })

  it('should not display event type summary when no events exist', () => {
    const mockProps = createMockCalendarContainerProps({
      currentEvents: [],
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    expect(screen.queryByText('Event Types:')).not.toBeInTheDocument()
  })

  it('should render Today button on desktop', () => {
    const mockProps = createMockCalendarContainerProps()

    renderCalendar(<CalendarContainer {...mockProps} />)

    const todayButton = screen.getByText('Today')
    expect(todayButton).toBeInTheDocument()
    expect(todayButton).toHaveClass('hidden', 'sm:flex')
  })

  it('should render Create Event button', () => {
    const mockProps = createMockCalendarContainerProps()

    renderCalendar(<CalendarContainer {...mockProps} />)

    const createButton = screen.getByText('Create Event')
    expect(createButton).toBeInTheDocument()
  })

  it('should call onTodayClick when Today button is clicked', () => {
    const mockOnTodayClick = vi.fn()
    const mockProps = createMockCalendarContainerProps({
      onTodayClick: mockOnTodayClick,
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    const todayButton = screen.getByText('Today')
    fireEvent.click(todayButton)

    expect(mockOnTodayClick).toHaveBeenCalledTimes(1)
  })

  it('should call onCreateEventClick when Create Event button is clicked', () => {
    const mockOnCreateEventClick = vi.fn()
    const mockProps = createMockCalendarContainerProps({
      onCreateEventClick: mockOnCreateEventClick,
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    const createButton = screen.getByText('Create Event')
    fireEvent.click(createButton)

    expect(mockOnCreateEventClick).toHaveBeenCalledTimes(1)
  })

  it('should show "Creating..." state when Create Event is clicked', () => {
    const mockProps = createMockCalendarContainerProps()

    renderCalendar(<CalendarContainer {...mockProps} />)

    const createButton = screen.getByText('Create Event')
    fireEvent.click(createButton)

    expect(screen.getByText('Creating...')).toBeInTheDocument()
    expect(createButton).toBeDisabled()
  })

  it('should render calendar content', () => {
    const mockProps = createMockCalendarContainerProps({
      children: <div data-testid="custom-calendar-content">Custom Calendar</div>,
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    expect(screen.getByTestId('custom-calendar-content')).toBeInTheDocument()
    expect(screen.getByText('Custom Calendar')).toBeInTheDocument()
  })

  it('should render mobile Today button', () => {
    const mockProps = createMockCalendarContainerProps()

    renderCalendar(<CalendarContainer {...mockProps} />)

    const mobileTodayButton = screen.getByText('Today')
    expect(mobileTodayButton.parentElement).toHaveClass('flex', 'sm:hidden')
  })

  it('should format date correctly', () => {
    const mockProps = createMockCalendarContainerProps({
      currentDate: new Date('2024-02-15'),
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    expect(screen.getByText('February 2024 • 0 events')).toBeInTheDocument()
  })

  it('should handle multiple events of the same type', () => {
    const events = [
      mockTrainingSessionEvent,
      { ...mockTrainingSessionEvent, id: 'training-2', title: 'Another Training' },
    ]
    const mockProps = createMockCalendarContainerProps({
      currentEvents: events,
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    expect(screen.getByText('Training Session: 2')).toBeInTheDocument()
  })

  it('should apply custom className', () => {
    const mockProps = createMockCalendarContainerProps({
      className: 'custom-calendar-class',
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    const container = screen.getByTestId('calendar-container')
    expect(container).toHaveClass('custom-calendar-class')
  })

  it('should handle events without type', () => {
    const eventWithoutType = {
      ...mockCalendarEvents[0],
      type: undefined,
    }
    const mockProps = createMockCalendarContainerProps({
      currentEvents: [eventWithoutType],
    })

    renderCalendar(<CalendarContainer {...mockProps} />)

    expect(screen.getByText('Other: 1')).toBeInTheDocument()
  })
})
