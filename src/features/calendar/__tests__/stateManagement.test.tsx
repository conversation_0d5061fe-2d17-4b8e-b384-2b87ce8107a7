import { renderHook, act } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import { useCalendarStore, useCalendarActions, useFilteredEvents } from '../stores/calendarStore'
import useCalendarStateManagement from '../hooks/useCalendarState'
import {
  useCalendarUndoRedo,
  useCalendarPerformance,
  useCalendarDebug,
} from '../stores/calendarAdvancedStore'
import type { CalendarEvent, CreateEventDto } from '../types'

// Mock the CalendarService
vi.mock('../services/calendarService', () => ({
  CalendarService: {
    getEvents: vi.fn(),
    createEvent: vi.fn(),
    updateEvent: vi.fn(),
    deleteEvent: vi.fn(),
  },
}))

describe('Calendar State Management', () => {
  beforeEach(() => {
    // Reset the store before each test
    useCalendarStore.getState().reset()
    useCalendarStore.getState().clearPendingOperations()
  })

  describe('Basic Store Operations', () => {
    it('should initialize with empty state', () => {
      const { result } = renderHook(() => useCalendarStore())

      expect(result.current.events).toEqual({})
      expect(result.current.eventsLoading).toBe(false)
      expect(result.current.eventsError).toBe(null)
      expect(result.current.filters).toEqual({})
      expect(result.current.searchQuery).toBe('')
      expect(result.current.selectedEventId).toBe(null)
      expect(result.current.currentView).toBe('dayGridMonth')
      expect(result.current.weekendsVisible).toBe(true)
    })

    it('should add events correctly', () => {
      const { result } = renderHook(() => useCalendarActions())
      const mockEvent: CalendarEvent = {
        id: '1',
        title: 'Test Event',
        start: new Date(),
        allDay: false,
      }

      act(() => {
        result.current.addEvent(mockEvent)
      })

      const store = useCalendarStore.getState()
      expect(store.events['1']).toEqual(mockEvent)
    })

    it('should update events correctly', () => {
      const { result } = renderHook(() => useCalendarActions())
      const mockEvent: CalendarEvent = {
        id: '1',
        title: 'Test Event',
        start: new Date(),
        allDay: false,
      }

      act(() => {
        result.current.addEvent(mockEvent)
        result.current.updateEvent('1', { title: 'Updated Event' })
      })

      const store = useCalendarStore.getState()
      expect(store.events['1'].title).toBe('Updated Event')
    })

    it('should delete events correctly', () => {
      const { result } = renderHook(() => useCalendarActions())
      const mockEvent: CalendarEvent = {
        id: '1',
        title: 'Test Event',
        start: new Date(),
        allDay: false,
      }

      act(() => {
        result.current.addEvent(mockEvent)
        result.current.deleteEvent('1')
      })

      const store = useCalendarStore.getState()
      expect(store.events['1']).toBeUndefined()
    })

    it('should set filters correctly', () => {
      const { result } = renderHook(() => useCalendarActions())
      const filters = { type: ['meeting' as const] }

      act(() => {
        result.current.setFilters(filters)
      })

      const store = useCalendarStore.getState()
      expect(store.filters).toEqual(filters)
    })

    it('should set search query correctly', () => {
      const { result } = renderHook(() => useCalendarActions())

      act(() => {
        result.current.setSearchQuery('test query')
      })

      const store = useCalendarStore.getState()
      expect(store.searchQuery).toBe('test query')
    })
  })

  describe('Filtered Events', () => {
    it('should filter events by type', () => {
      const { result } = renderHook(() => useCalendarActions())
      const events: CalendarEvent[] = [
        {
          id: '1',
          title: 'Meeting',
          start: new Date(),
          type: 'meeting',
          allDay: false,
        },
        {
          id: '2',
          title: 'Training',
          start: new Date(),
          type: 'training_session',
          allDay: false,
        },
      ]

      act(() => {
        events.forEach((event) => result.current.addEvent(event))
        result.current.setFilters({ type: ['meeting' as const] })
      })

      const { result: filterResult } = renderHook(() => useFilteredEvents())
      expect(filterResult.current).toHaveLength(1)
      expect(filterResult.current[0].title).toBe('Meeting')
    })

    it('should filter events by search query', () => {
      const { result } = renderHook(() => useCalendarActions())
      const events: CalendarEvent[] = [
        {
          id: '1',
          title: 'Important Meeting',
          start: new Date(),
          allDay: false,
        },
        {
          id: '2',
          title: 'Regular Training',
          start: new Date(),
          allDay: false,
        },
      ]

      act(() => {
        events.forEach((event) => result.current.addEvent(event))
        result.current.setSearchQuery('Important')
      })

      const { result: filterResult } = renderHook(() => useFilteredEvents())
      expect(filterResult.current).toHaveLength(1)
      expect(filterResult.current[0].title).toBe('Important Meeting')
    })
  })

  describe('UI State Management', () => {
    it('should change calendar view', () => {
      const { result } = renderHook(() => useCalendarActions())

      act(() => {
        result.current.setCurrentView('timeGridWeek')
      })

      const store = useCalendarStore.getState()
      expect(store.currentView).toBe('timeGridWeek')
    })

    it('should toggle weekends visibility', () => {
      const { result } = renderHook(() => useCalendarActions())

      act(() => {
        result.current.toggleWeekends()
      })

      const store = useCalendarStore.getState()
      expect(store.weekendsVisible).toBe(false)
    })

    it('should set selected event', () => {
      const { result } = renderHook(() => useCalendarActions())

      act(() => {
        result.current.setSelectedEvent('1')
      })

      const store = useCalendarStore.getState()
      expect(store.selectedEventId).toBe('1')
    })
  })

  describe('Combined Hook', () => {
    it('should provide access to all state and actions', () => {
      const { result } = renderHook(() => useCalendarStateManagement())

      expect(result.current.events).toEqual([])
      expect(result.current.filteredEvents).toEqual([])
      expect(result.current.selectedEvent).toBe(null)
      expect(result.current.isLoading).toBe(false)
      expect(result.current.hasError).toBe(false)
      expect(result.current.isEmpty).toBe(true)

      // Actions should be available
      expect(typeof result.current.mutations.createEvent).toBe('function')
      expect(typeof result.current.updateFilters).toBe('function')
      expect(typeof result.current.changeView).toBe('function')
      expect(typeof result.current.selectEvent).toBe('function')
    })
  })

  describe('Advanced Features', () => {
    beforeEach(() => {
      // Reset advanced store
      vi.doUnmock('../stores/calendarAdvancedStore')
      vi.resetModules()
    })

    it('should support undo/redo functionality', () => {
      const { result } = renderHook(() => useCalendarUndoRedo())
      const { result: actionsResult } = renderHook(() => useCalendarActions())

      const mockEvent: CalendarEvent = {
        id: '1',
        title: 'Test Event',
        start: new Date(),
        allDay: false,
      }

      // Take initial snapshot
      act(() => {
        result.current.takeSnapshot('Initial state')
      })

      // Add event
      act(() => {
        actionsResult.current.addEvent(mockEvent)
      })

      // Check that event was added
      let store = useCalendarStore.getState()
      expect(store.events['1']).toBeDefined()

      // Undo
      act(() => {
        result.current.undo()
      })

      // Check that event was removed
      store = useCalendarStore.getState()
      expect(store.events['1']).toBeUndefined()

      // Redo
      act(() => {
        result.current.redo()
      })

      // Check that event was restored
      store = useCalendarStore.getState()
      expect(store.events['1']).toBeDefined()
    })

    it('should track performance metrics', () => {
      const { result } = renderHook(() => useCalendarPerformance())

      act(() => {
        result.current.recordRenderTime(16)
        result.current.recordOperationTime(50)
      })

      expect(result.current.performanceMetrics.renderCount).toBe(1)
      expect(result.current.performanceMetrics.lastRenderTime).toBe(16)
      expect(result.current.performanceMetrics.operationCount).toBe(1)
      expect(result.current.performanceMetrics.lastOperationTime).toBe(50)
    })

    it('should log debug information', () => {
      const { result } = renderHook(() => useCalendarDebug())

      act(() => {
        result.current.logAction('Test action', { data: 'test' })
        result.current.logError('Test error', { context: 'test' })
      })

      const debugInfo = result.current.getDebugInfo()
      expect(debugInfo.lastAction).toBe('Test action')
      expect(debugInfo.actionHistory).toHaveLength(1)
      expect(debugInfo.errors).toHaveLength(1)
    })

    it('should validate state', () => {
      const { result } = renderHook(() => useCalendarDebug())
      const { result: actionsResult } = renderHook(() => useCalendarActions())

      // Add invalid event (no title)
      const invalidEvent: CalendarEvent = {
        id: '1',
        title: '',
        start: new Date(),
        allDay: false,
      }

      act(() => {
        actionsResult.current.addEvent(invalidEvent)
        const isValid = result.current.validateState()
      })

      expect(result.current.validationErrors.length).toBeGreaterThan(0)
      expect(result.current.validationErrors[0]).toContain('has no title')
    })
  })

  describe('Error Handling', () => {
    it('should handle loading and error states', () => {
      const { result } = renderHook(() => useCalendarActions())

      act(() => {
        result.current.setEventsLoading(true)
        result.current.setEventsError('Test error')
      })

      const store = useCalendarStore.getState()
      expect(store.eventsLoading).toBe(true)
      expect(store.eventsError).toBe('Test error')
    })

    it('should clear errors when resolved', () => {
      const { result } = renderHook(() => useCalendarActions())

      act(() => {
        result.current.setEventsError('Test error')
        result.current.setEventsError(null)
      })

      const store = useCalendarStore.getState()
      expect(store.eventsError).toBe(null)
    })
  })

  describe('Performance Optimization', () => {
    it('should cache filter results', () => {
      const { result } = renderHook(() => useCalendarActions())
      const events: CalendarEvent[] = [
        {
          id: '1',
          title: 'Test Event',
          start: new Date(),
          type: 'meeting',
          allDay: false,
        },
      ]

      act(() => {
        events.forEach((event) => result.current.addEvent(event))
        result.current.setFilters({ type: ['meeting'] })
      })

      const { result: filterResult } = renderHook(() => useFilteredEvents())
      const firstResult = filterResult.current

      // Re-render with same filters - should use cache
      const { result: filterResult2 } = renderHook(() => useFilteredEvents())
      expect(filterResult2.current).toBe(firstResult)
    })
  })
})
