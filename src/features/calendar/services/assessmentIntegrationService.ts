import { AssessmentService } from '../../assessments/services/assessmentService'
import type { CalendarEvent, EventType, EventPriority, EventStatus } from '../types'
import type {
  Assessment,
  AssessmentReview,
  AssessmentStatus,
} from '../../../shared/types/assessment'
import type { EntityId } from '../../../shared/types/common'

/**
 * Assessment-specific metadata for calendar events
 */
export interface AssessmentEventMetadata extends Record<string, unknown> {
  assessmentId: EntityId
  type: 'deadline' | 'session' | 'review' | 'reminder'
  status: AssessmentStatus
  traineeId?: EntityId
  reviewerId?: EntityId
  quarter?: number
  year?: number
  overallScore?: number
  technicalScore?: number
  softSkillsScore?: number
  onJobScore?: number
  location?: string
  duration?: number
  instructor?: string
}

/**
 * Assessment calendar integration service
 * Transforms assessment data into calendar events and manages synchronization
 */
export class AssessmentIntegrationService {
  /**
   * Get all assessment events from assessments data
   */
  static async getAssessmentEvents(): Promise<CalendarEvent[]> {
    try {
      const assessments = await AssessmentService.getAssessments()
      const events: CalendarEvent[] = []

      for (const assessment of assessments) {
        const assessmentEvents = this.transformAssessmentToEvents(assessment)
        events.push(...assessmentEvents)
      }

      return events
    } catch (error) {
      console.error('Failed to fetch assessment events:', error)
      throw new Error(
        `Failed to fetch assessment events: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Get assessment events for a specific trainee
   */
  static async getAssessmentEventsByTrainee(traineeId: EntityId): Promise<CalendarEvent[]> {
    try {
      const assessments = await AssessmentService.getAssessmentsByTrainee(traineeId)
      const events: CalendarEvent[] = []

      for (const assessment of assessments) {
        const assessmentEvents = this.transformAssessmentToEvents(assessment)
        events.push(...assessmentEvents)
      }

      return events
    } catch (error) {
      console.error('Failed to fetch assessment events for trainee:', error)
      throw new Error(
        `Failed to fetch assessment events for trainee: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Get assessment events for a specific quarter and year
   */
  static async getAssessmentEventsByQuarter(
    quarter: number,
    year: number
  ): Promise<CalendarEvent[]> {
    try {
      const assessments = await AssessmentService.getAssessmentsByQuarter(quarter, year)
      const events: CalendarEvent[] = []

      for (const assessment of assessments) {
        const assessmentEvents = this.transformAssessmentToEvents(assessment)
        events.push(...assessmentEvents)
      }

      return events
    } catch (error) {
      console.error('Failed to fetch assessment events for quarter:', error)
      throw new Error(
        `Failed to fetch assessment events for quarter: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Get assessment events by status
   */
  static async getAssessmentEventsByStatus(status: AssessmentStatus): Promise<CalendarEvent[]> {
    try {
      const assessments = await AssessmentService.getAssessmentsByStatus(status)
      const events: CalendarEvent[] = []

      for (const assessment of assessments) {
        const assessmentEvents = this.transformAssessmentToEvents(assessment)
        events.push(...assessmentEvents)
      }

      return events
    } catch (error) {
      console.error('Failed to fetch assessment events by status:', error)
      throw new Error(
        `Failed to fetch assessment events by status: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Transform a single assessment into calendar events
   */
  static transformAssessmentToEvents(assessment: Assessment): CalendarEvent[] {
    const events: CalendarEvent[] = []

    // Add assessment deadline event
    if (assessment.dueDate) {
      events.push(this.createDeadlineEvent(assessment))
    }

    // Add assessment session event
    if (assessment.scheduledDate) {
      events.push(this.createSessionEvent(assessment))
    }

    // Add review session events
    if (assessment.reviews && assessment.reviews.length > 0) {
      assessment.reviews.forEach((review) => {
        if (review.createdAt) {
          events.push(this.createReviewEvent(assessment, review))
        }
      })
    }

    // Add reminder events for upcoming assessments
    if (assessment.status === 'pending' || assessment.status === 'in_progress') {
      events.push(this.createReminderEvent(assessment))
    }

    return events
  }

  /**
   * Create a deadline event for an assessment
   */
  private static createDeadlineEvent(assessment: Assessment): CalendarEvent {
    const metadata: AssessmentEventMetadata = {
      assessmentId: assessment.id,
      type: 'deadline',
      status: assessment.status,
      traineeId: assessment.traineeId,
      quarter: assessment.quarter,
      year: assessment.year,
      overallScore: assessment.overallScore,
    }

    return {
      id: `assessment-deadline-${assessment.id}`,
      title: `${assessment.trainee?.userName || 'Trainee'} - Assessment Deadline`,
      start: new Date(assessment.dueDate),
      allDay: true,
      type: 'deadline' as EventType,
      status: this.mapAssessmentStatusToEventStatus(assessment.status),
      priority: this.getDeadlinePriority(assessment),
      description: `Q${assessment.quarter} ${assessment.year} Assessment Deadline`,
      assessmentId: assessment.id,
      traineeIds: [assessment.traineeId],
      metadata,
      createdAt: assessment.createdAt,
      updatedAt: assessment.updatedAt,
    }
  }

  /**
   * Create a session event for an assessment
   */
  private static createSessionEvent(assessment: Assessment): CalendarEvent {
    const metadata: AssessmentEventMetadata = {
      assessmentId: assessment.id,
      type: 'session',
      status: assessment.status,
      traineeId: assessment.traineeId,
      quarter: assessment.quarter,
      year: assessment.year,
      location: 'Assessment Room', // Default location
      duration: 120, // Default 2 hours
    }

    const startDate = new Date(assessment.scheduledDate)
    const endDate = new Date(startDate.getTime() + 2 * 60 * 60 * 1000) // Add 2 hours

    return {
      id: `assessment-session-${assessment.id}`,
      title: `${assessment.trainee?.userName || 'Trainee'} - Assessment Session`,
      start: startDate,
      end: endDate,
      allDay: false,
      type: 'assessment' as EventType,
      status: this.mapAssessmentStatusToEventStatus(assessment.status),
      priority: 'high' as EventPriority,
      description: `Q${assessment.quarter} ${assessment.year} Assessment Session`,
      location: 'Assessment Room',
      assessmentId: assessment.id,
      traineeIds: [assessment.traineeId],
      metadata,
      createdAt: assessment.createdAt,
      updatedAt: assessment.updatedAt,
    }
  }

  /**
   * Create a review event for an assessment
   */
  private static createReviewEvent(
    assessment: Assessment,
    review: AssessmentReview
  ): CalendarEvent {
    const metadata: AssessmentEventMetadata = {
      assessmentId: assessment.id,
      type: 'review',
      status: assessment.status,
      traineeId: assessment.traineeId,
      reviewerId: review.reviewerId,
      quarter: assessment.quarter,
      year: assessment.year,
      technicalScore: review.technicalScore,
      softSkillsScore: review.softSkillsScore,
      onJobScore: review.onJobScore,
      instructor: `${review.reviewerRole} Review`,
    }

    return {
      id: `assessment-review-${assessment.id}-${review.id}`,
      title: `${assessment.trainee?.userName || 'Trainee'} - Review Session`,
      start: new Date(review.createdAt),
      allDay: false,
      type: 'review' as EventType,
      status: 'completed' as EventStatus,
      priority: 'medium' as EventPriority,
      description: `Assessment review by ${review.reviewerRole}`,
      assessmentId: assessment.id,
      traineeIds: [assessment.traineeId],
      metadata,
      createdAt: review.createdAt,
      updatedAt: review.updatedAt,
    }
  }

  /**
   * Create a reminder event for an upcoming assessment
   */
  private static createReminderEvent(assessment: Assessment): CalendarEvent {
    const reminderDate = new Date(assessment.scheduledDate)
    reminderDate.setDate(reminderDate.getDate() - 1) // Remind 1 day before

    const metadata: AssessmentEventMetadata = {
      assessmentId: assessment.id,
      type: 'reminder',
      status: assessment.status,
      traineeId: assessment.traineeId,
      quarter: assessment.quarter,
      year: assessment.year,
    }

    return {
      id: `assessment-reminder-${assessment.id}`,
      title: `${assessment.trainee?.userName || 'Trainee'} - Assessment Reminder`,
      start: reminderDate,
      allDay: true,
      type: 'meeting' as EventType,
      status: 'scheduled' as EventStatus,
      priority: 'medium' as EventPriority,
      description: `Reminder for Q${assessment.quarter} ${assessment.year} assessment`,
      assessmentId: assessment.id,
      traineeIds: [assessment.traineeId],
      metadata,
      createdAt: assessment.createdAt,
      updatedAt: assessment.updatedAt,
    }
  }

  /**
   * Map assessment status to calendar event status
   */
  private static mapAssessmentStatusToEventStatus(status: AssessmentStatus): EventStatus {
    switch (status) {
      case 'pending':
        return 'scheduled'
      case 'in_progress':
        return 'in_progress'
      case 'completed':
        return 'completed'
      case 'overdue':
        return 'postponed'
      default:
        return 'scheduled'
    }
  }

  /**
   * Get deadline priority based on assessment status and due date
   */
  private static getDeadlinePriority(assessment: Assessment): EventPriority {
    const now = new Date()
    const dueDate = new Date(assessment.dueDate)
    const daysUntilDue = Math.ceil((dueDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))

    if (assessment.status === 'overdue') {
      return 'urgent'
    } else if (daysUntilDue <= 3) {
      return 'high'
    } else if (daysUntilDue <= 7) {
      return 'medium'
    } else {
      return 'low'
    }
  }

  /**
   * Helper function to safely convert DateInput to Date
   */
  private static safeToDate(dateInput: any): Date {
    if (dateInput instanceof Date) {
      return dateInput
    }
    if (typeof dateInput === 'string' || typeof dateInput === 'number') {
      return new Date(dateInput)
    }
    // Handle other cases (like arrays from FullCalendar)
    if (Array.isArray(dateInput)) {
      return new Date(
        dateInput[0],
        dateInput[1],
        dateInput[2],
        dateInput[3] || 0,
        dateInput[4] || 0,
        dateInput[5] || 0
      )
    }
    // Fallback
    return new Date()
  }

  /**
   * Get assessment statistics for calendar dashboard
   */
  static async getAssessmentStats(): Promise<{
    total: number
    byStatus: Record<AssessmentStatus, number>
    upcomingDeadlines: CalendarEvent[]
    overdueAssessments: CalendarEvent[]
    thisWeek: CalendarEvent[]
    thisMonth: CalendarEvent[]
  }> {
    try {
      const events = await this.getAssessmentEvents()
      const now = new Date()
      const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      const monthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
      const weekStart = new Date(now)
      weekStart.setDate(now.getDate() - now.getDay())
      weekStart.setHours(0, 0, 0, 0)
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)

      const assessments = await AssessmentService.getAssessments()

      const byStatus = assessments.reduce(
        (acc, assessment) => {
          acc[assessment.status] = (acc[assessment.status] || 0) + 1
          return acc
        },
        {} as Record<AssessmentStatus, number>
      )

      const upcomingDeadlines = events
        .filter(
          (event) =>
            event.type === 'deadline' &&
            this.safeToDate(event.start) > now &&
            this.safeToDate(event.start) <= weekFromNow
        )
        .sort((a, b) => this.safeToDate(a.start).getTime() - this.safeToDate(b.start).getTime())

      const overdueAssessments = events
        .filter(
          (event) =>
            event.type === 'deadline' &&
            this.safeToDate(event.start) < now &&
            event.status !== 'completed'
        )
        .sort((a, b) => this.safeToDate(b.start).getTime() - this.safeToDate(a.start).getTime())

      const thisWeek = events
        .filter(
          (event) =>
            this.safeToDate(event.start) >= weekStart && this.safeToDate(event.start) <= weekFromNow
        )
        .sort((a, b) => this.safeToDate(a.start).getTime() - this.safeToDate(b.start).getTime())

      const thisMonth = events
        .filter(
          (event) =>
            this.safeToDate(event.start) >= monthStart &&
            this.safeToDate(event.start) <= monthFromNow
        )
        .sort((a, b) => this.safeToDate(a.start).getTime() - this.safeToDate(b.start).getTime())

      return {
        total: assessments.length,
        byStatus,
        upcomingDeadlines,
        overdueAssessments,
        thisWeek,
        thisMonth,
      }
    } catch (error) {
      console.error('Failed to get assessment stats:', error)
      throw new Error(
        `Failed to get assessment stats: ${error instanceof Error ? error.message : 'Unknown error'}`
      )
    }
  }

  /**
   * Sync assessment events with calendar
   */
  static async syncAssessmentEvents(): Promise<{
    added: number
    updated: number
    removed: number
    errors: string[]
  }> {
    try {
      const assessmentEvents = await this.getAssessmentEvents()
      // This would integrate with the calendar service to sync events
      // For now, we'll return the events that would be synced
      return {
        added: assessmentEvents.length,
        updated: 0,
        removed: 0,
        errors: [],
      }
    } catch (error) {
      console.error('Failed to sync assessment events:', error)
      return {
        added: 0,
        updated: 0,
        removed: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
      }
    }
  }
}

export default AssessmentIntegrationService
