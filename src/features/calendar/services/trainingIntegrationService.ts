import type { CalendarEvent, EventType, EventPriority, EventStatus } from '../types'
import type {
  TrainingProgram,
  TrainingSession,
  TrainingMilestone,
  TrainingSchedule,
} from '../../../shared/types/trainingProgram'
import { TrainingProgramService } from '../../../features/training-programs/services/trainingProgramService'

/**
 * Service for integrating training programs with the calendar system
 * Transforms training data into calendar events and manages synchronization
 */
export class TrainingIntegrationService {
  /**
   * Get all training program events for the calendar
   */
  static async getTrainingProgramEvents(): Promise<CalendarEvent[]> {
    try {
      const programs = await TrainingProgramService.getTrainingPrograms()
      const allEvents: CalendarEvent[] = []

      for (const program of programs) {
        const programEvents = this.transformProgramToEvents(program)
        allEvents.push(...programEvents)
      }

      return allEvents
    } catch (error) {
      console.error('Error fetching training program events:', error)
      throw error
    }
  }

  /**
   * Get training program events for a specific program
   */
  static async getTrainingProgramEventsById(programId: string): Promise<CalendarEvent[]> {
    try {
      const program = await TrainingProgramService.getTrainingProgramById(programId)
      if (!program) {
        return []
      }

      return this.transformProgramToEvents(program)
    } catch (error) {
      console.error(`Error fetching training program events for ${programId}:`, error)
      throw error
    }
  }

  /**
   * Transform a training program into calendar events
   */
  static transformProgramToEvents(program: TrainingProgram): CalendarEvent[] {
    const events: CalendarEvent[] = []

    // Add program start/end events if schedule exists
    if (program.schedule) {
      // Program start event
      events.push({
        id: `program-start-${program.id}`,
        title: `${program.name} - Program Start`,
        start: program.schedule.startDate,
        end: program.schedule.startDate,
        allDay: true,
        type: 'training_session' as EventType,
        status: 'scheduled' as EventStatus,
        priority: 'high' as EventPriority,
        description: program.description,
        trainingProgramId: program.id,
        metadata: {
          programId: program.id,
          eventType: 'program_start',
          instructor: program.instructor,
          department: program.department,
          maxCapacity: program.maxCapacity,
          currentEnrollment: program.currentEnrollment,
        },
      })

      // Program end event
      events.push({
        id: `program-end-${program.id}`,
        title: `${program.name} - Program Completion`,
        start: program.schedule.endDate,
        end: program.schedule.endDate,
        allDay: true,
        type: 'deadline' as EventType,
        status: 'scheduled' as EventStatus,
        priority: 'high' as EventPriority,
        description: `Completion of ${program.name} training program`,
        trainingProgramId: program.id,
        metadata: {
          programId: program.id,
          eventType: 'program_end',
          instructor: program.instructor,
          department: program.department,
        },
      })

      // Add training sessions
      program.schedule.sessions?.forEach((session) => {
        events.push(this.transformSessionToEvent(session, program))
      })

      // Add milestones and deadlines
      program.schedule.milestones?.forEach((milestone) => {
        events.push(this.transformMilestoneToEvent(milestone, program))
      })
    }

    return events
  }

  /**
   * Transform a training session into a calendar event
   */
  static transformSessionToEvent(
    session: TrainingSession,
    program: TrainingProgram
  ): CalendarEvent {
    return {
      id: `training-${program.id}-${session.id}`,
      title: `${program.name} - ${session.title}`,
      start: session.startDate,
      end: session.endDate,
      allDay: false,
      type: 'training_session' as EventType,
      status: (session.status as EventStatus) || 'scheduled',
      priority: this.getSessionPriority(session.type),
      description: session.description,
      location: session.location,
      instructor: session.instructor || program.instructor,
      trainingProgramId: program.id,
      metadata: {
        programId: program.id,
        sessionId: session.id,
        sessionType: session.type,
        instructor: session.instructor,
        location: session.location,
        capacity: session.capacity,
        enrolledCount: session.enrolledCount,
        resources: session.resources,
        eventType: 'training_session',
      },
    }
  }

  /**
   * Transform a training milestone into a calendar event
   */
  static transformMilestoneToEvent(
    milestone: TrainingMilestone,
    program: TrainingProgram
  ): CalendarEvent {
    return {
      id: `milestone-${program.id}-${milestone.id}`,
      title: `${program.name} - ${milestone.title}`,
      start: milestone.dueDate,
      end: milestone.dueDate,
      allDay: true,
      type: this.getMilestoneEventType(milestone.type),
      status: (milestone.status as EventStatus) || 'scheduled',
      priority: this.getMilestonePriority(milestone.type),
      description: milestone.description,
      trainingProgramId: program.id,
      metadata: {
        programId: program.id,
        milestoneId: milestone.id,
        milestoneType: milestone.type,
        weight: milestone.weight,
        dependencies: milestone.dependencies,
        eventType: 'milestone',
      },
    }
  }

  /**
   * Get event type for training session based on session type
   */
  private static getSessionPriority(sessionType?: string): EventPriority {
    switch (sessionType) {
      case 'assessment':
        return 'high' as EventPriority
      case 'review':
        return 'medium' as EventPriority
      case 'workshop':
        return 'medium' as EventPriority
      case 'lab':
        return 'medium' as EventPriority
      case 'lecture':
      default:
        return 'low' as EventPriority
    }
  }

  /**
   * Get event type for milestone based on milestone type
   */
  private static getMilestoneEventType(milestoneType?: string): EventType {
    switch (milestoneType) {
      case 'assessment':
        return 'assessment' as EventType
      case 'project':
        return 'deadline' as EventType
      case 'certification':
        return 'deadline' as EventType
      case 'review':
        return 'review' as EventType
      case 'deadline':
      default:
        return 'deadline' as EventType
    }
  }

  /**
   * Get priority for milestone based on milestone type
   */
  private static getMilestonePriority(milestoneType?: string): EventPriority {
    switch (milestoneType) {
      case 'certification':
        return 'urgent' as EventPriority
      case 'assessment':
        return 'high' as EventPriority
      case 'project':
        return 'high' as EventPriority
      case 'review':
        return 'medium' as EventPriority
      case 'deadline':
      default:
        return 'medium' as EventPriority
    }
  }

  /**
   * Sync training program changes to calendar events
   */
  static async syncTrainingProgramChanges(programId: string): Promise<CalendarEvent[]> {
    try {
      const updatedEvents = await this.getTrainingProgramEventsById(programId)
      return updatedEvents
    } catch (error) {
      console.error(`Error syncing training program changes for ${programId}:`, error)
      throw error
    }
  }

  /**
   * Get training programs by date range for calendar filtering
   */
  static async getTrainingProgramsByDateRange(
    startDate: Date,
    endDate: Date
  ): Promise<CalendarEvent[]> {
    try {
      const allEvents = await this.getTrainingProgramEvents()

      return allEvents.filter((event) => {
        const eventStart = new Date(event.start as string)
        const eventEnd = event.end ? new Date(event.end as string) : eventStart

        return (
          (eventStart >= startDate && eventStart <= endDate) ||
          (eventEnd >= startDate && eventEnd <= endDate) ||
          (eventStart <= startDate && eventEnd >= endDate)
        )
      })
    } catch (error) {
      console.error('Error fetching training programs by date range:', error)
      throw error
    }
  }

  /**
   * Get training statistics for calendar analytics
   */
  static async getTrainingStatistics(): Promise<{
    totalPrograms: number
    activePrograms: number
    totalSessions: number
    upcomingSessions: number
    completedMilestones: number
    pendingMilestones: number
  }> {
    try {
      const programs = await TrainingProgramService.getTrainingPrograms()
      const events = await this.getTrainingProgramEvents()

      const now = new Date()
      const sessions = events.filter((e) => e.metadata?.eventType === 'training_session')
      const milestones = events.filter((e) => e.metadata?.eventType === 'milestone')

      return {
        totalPrograms: programs.length,
        activePrograms: programs.filter((p) => p.status === 'active').length,
        totalSessions: sessions.length,
        upcomingSessions: sessions.filter((s) => new Date(s.start as string) > now).length,
        completedMilestones: milestones.filter((m) => m.status === 'completed').length,
        pendingMilestones: milestones.filter(
          (m) => m.status === 'scheduled' || m.status === 'in_progress'
        ).length,
      }
    } catch (error) {
      console.error('Error fetching training statistics:', error)
      throw error
    }
  }

  /**
   * Create calendar event from training session data
   */
  static createCalendarEventFromSession(
    session: TrainingSession,
    programId: string,
    programName: string
  ): CalendarEvent {
    return this.transformSessionToEvent(session, {
      id: programId,
      name: programName,
      durationMonths: 0,
    } as TrainingProgram)
  }

  /**
   * Create calendar event from milestone data
   */
  static createCalendarEventFromMilestone(
    milestone: TrainingMilestone,
    programId: string,
    programName: string
  ): CalendarEvent {
    return this.transformMilestoneToEvent(milestone, {
      id: programId,
      name: programName,
      durationMonths: 0,
    } as TrainingProgram)
  }
}
