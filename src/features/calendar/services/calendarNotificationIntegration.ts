import { CalendarService } from './calendarService'
import { CalendarNotificationService } from './notificationService'
import type {
  CalendarEvent,
  CreateEventDto,
  UpdateEventDto,
  EventReminder,
  ReminderType,
} from '../types'
import type { DateInput } from '@fullcalendar/core'

/**
 * Calendar Notification Integration Service
 * Enhances calendar operations with automatic notification triggers
 */
export class CalendarNotificationIntegration {
  /**
   * Safely convert DateInput to Date object
   */
  private static toDate(dateInput: DateInput): Date {
    if (dateInput instanceof Date) {
      return dateInput
    }
    if (typeof dateInput === 'string' || typeof dateInput === 'number') {
      return new Date(dateInput)
    }
    // Handle other cases (like arrays from FullCalendar)
    if (Array.isArray(dateInput)) {
      return new Date(
        dateInput[0],
        dateInput[1],
        dateInput[2],
        dateInput[3] || 0,
        dateInput[4] || 0,
        dateInput[5] || 0
      )
    }
    // Fallback
    return new Date()
  }

  /**
   * Create an event with automatic notifications
   */
  static async createEventWithNotifications(
    eventData: CreateEventDto,
    notificationOptions?: {
      enableReminders?: boolean
      reminderTimes?: Array<{ time: Date; type: ReminderType }>
      notifyAttendees?: boolean
      attendeeIds?: string[]
    }
  ): Promise<CalendarEvent> {
    try {
      // Create the event first
      const event = await CalendarService.createEvent(eventData)

      // If notifications are enabled, set them up
      if (notificationOptions?.enableReminders) {
        await this.setupEventReminders(event.id, notificationOptions.reminderTimes)
      }

      // Notify attendees if specified
      if (notificationOptions?.notifyAttendees && notificationOptions.attendeeIds) {
        await CalendarNotificationService.sendEventNotification(
          'event_created',
          event.id,
          notificationOptions.attendeeIds,
          `New event "${event.title}" has been scheduled.`
        )
      }

      return event
    } catch (error) {
      throw new Error(
        `Failed to create event with notifications: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Update an event with change notifications
   */
  static async updateEventWithNotifications(
    eventData: UpdateEventDto,
    notificationOptions?: {
      notifyChanges?: boolean
      affectedUserIds?: string[]
      changes?: Record<string, { old: unknown; new: unknown }>
    }
  ): Promise<CalendarEvent> {
    try {
      // Get the original event for comparison
      const originalEvent = await CalendarService.getEvent(eventData.id)

      // Update the event
      const updatedEvent = await CalendarService.updateEvent(eventData)

      // Send change notifications if enabled
      if (notificationOptions?.notifyChanges && notificationOptions.affectedUserIds) {
        await CalendarNotificationService.notifyEventChange(
          eventData.id,
          'updated',
          notificationOptions.affectedUserIds,
          notificationOptions.changes
        )
      }

      return updatedEvent
    } catch (error) {
      throw new Error(
        `Failed to update event with notifications: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Delete an event with notifications
   */
  static async deleteEventWithNotifications(
    eventId: string,
    notificationOptions?: {
      notifyCancellation?: boolean
      affectedUserIds?: string[]
    }
  ): Promise<void> {
    try {
      // Get event details before deletion
      const event = await CalendarService.getEvent(eventId)

      // Delete the event
      await CalendarService.deleteEvent(eventId)

      // Send cancellation notifications if enabled
      if (notificationOptions?.notifyCancellation && notificationOptions.affectedUserIds) {
        await CalendarNotificationService.sendEventNotification(
          'event_deleted',
          eventId,
          notificationOptions.affectedUserIds,
          `Event "${event?.title}" has been deleted.`
        )
      }
    } catch (error) {
      throw new Error(
        `Failed to delete event with notifications: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Cancel an event with notifications
   */
  static async cancelEventWithNotifications(
    eventId: string,
    notificationOptions?: {
      notifyCancellation?: boolean
      affectedUserIds?: string[]
      cancellationReason?: string
    }
  ): Promise<CalendarEvent> {
    try {
      // Update event status to cancelled
      const cancelledEvent = await CalendarService.updateEvent({
        id: eventId,
        status: 'cancelled',
      })

      // Send cancellation notifications if enabled
      if (notificationOptions?.notifyCancellation && notificationOptions.affectedUserIds) {
        const message = notificationOptions.cancellationReason
          ? `Event "${cancelledEvent.title}" has been cancelled. Reason: ${notificationOptions.cancellationReason}`
          : `Event "${cancelledEvent.title}" has been cancelled.`

        await CalendarNotificationService.sendEventNotification(
          'event_cancelled',
          eventId,
          notificationOptions.affectedUserIds,
          message
        )
      }

      return cancelledEvent
    } catch (error) {
      throw new Error(
        `Failed to cancel event with notifications: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Setup automatic reminders for an event
   */
  static async setupEventReminders(
    eventId: string,
    reminderTimes?: Array<{ time: Date; type: ReminderType }>
  ): Promise<EventReminder[]> {
    try {
      const event = await CalendarService.getEvent(eventId)
      if (!event) {
        throw new Error(`Event not found: ${eventId}`)
      }

      const reminders: EventReminder[] = []

      // Default reminder times if none provided
      const defaultReminders = reminderTimes || [
        { time: new Date(this.toDate(event.start).getTime() - 15 * 60 * 1000), type: 'in_app' }, // 15 minutes before
        { time: new Date(this.toDate(event.start).getTime() - 60 * 60 * 1000), type: 'email' }, // 1 hour before
        { time: new Date(this.toDate(event.start).getTime() - 24 * 60 * 60 * 1000), type: 'email' }, // 1 day before
      ]

      // Schedule each reminder
      for (const { time, type } of defaultReminders) {
        const reminder = await CalendarNotificationService.scheduleEventReminder(
          eventId,
          'current_user', // This would come from auth context
          time,
          type
        )

        if (reminder) {
          reminders.push(reminder)
        }
      }

      return reminders
    } catch (error) {
      throw new Error(
        `Failed to setup event reminders: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Schedule training deadline notifications
   */
  static async scheduleTrainingDeadlineNotifications(
    trainingProgramId: string,
    deadlineDate: Date,
    participantIds: string[]
  ): Promise<void> {
    try {
      // Schedule multiple reminders for the deadline
      const reminderOffsets = [
        { days: 7, type: 'email' as ReminderType }, // 1 week before
        { days: 3, type: 'email' as ReminderType }, // 3 days before
        { days: 1, type: 'email' as ReminderType }, // 1 day before
        { hours: 2, type: 'in_app' as ReminderType }, // 2 hours before
      ]

      for (const offset of reminderOffsets) {
        let reminderTime: Date

        if (offset.days) {
          reminderTime = new Date(deadlineDate.getTime() - offset.days * 24 * 60 * 60 * 1000)
        } else if (offset.hours) {
          reminderTime = new Date(deadlineDate.getTime() - offset.hours * 60 * 60 * 1000)
        } else {
          continue
        }

        // Only schedule if the reminder time is in the future
        if (reminderTime > new Date()) {
          await CalendarNotificationService.sendTrainingDeadlineNotifications(
            trainingProgramId,
            'completion', // Using 'completion' instead of 'deadline'
            deadlineDate,
            participantIds
          )
        }
      }
    } catch (error) {
      throw new Error(
        `Failed to schedule training deadline notifications: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Schedule assessment deadline notifications
   */
  static async scheduleAssessmentDeadlineNotifications(
    assessmentId: string,
    deadlineDate: Date,
    participantIds: string[],
    reviewerIds?: string[]
  ): Promise<void> {
    try {
      // Schedule deadline reminders for participants
      const participantReminders = [
        { days: 3, type: 'email' as ReminderType }, // 3 days before
        { days: 1, type: 'email' as ReminderType }, // 1 day before
        { hours: 6, type: 'in_app' as ReminderType }, // 6 hours before
      ]

      for (const offset of participantReminders) {
        let reminderTime: Date

        if (offset.days) {
          reminderTime = new Date(deadlineDate.getTime() - offset.days * 24 * 60 * 60 * 1000)
        } else if (offset.hours) {
          reminderTime = new Date(deadlineDate.getTime() - offset.hours * 60 * 60 * 1000)
        } else {
          continue
        }

        // Only schedule if the reminder time is in the future
        if (reminderTime > new Date()) {
          await CalendarNotificationService.sendAssessmentReminders(
            assessmentId,
            'deadline',
            reminderTime,
            participantIds
          )
        }
      }

      // Schedule review reminders for reviewers if provided
      if (reviewerIds && reviewerIds.length > 0) {
        const reviewTime = new Date(deadlineDate.getTime() + 24 * 60 * 60 * 1000) // 1 day after deadline

        await CalendarNotificationService.sendAssessmentReminders(
          assessmentId,
          'review',
          reviewTime,
          reviewerIds
        )
      }
    } catch (error) {
      throw new Error(
        `Failed to schedule assessment deadline notifications: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Handle resource conflict notifications
   */
  static async handleResourceConflict(conflictDetails: {
    resourceId: string
    resourceName: string
    resourceType: string
    conflictType: 'double_booking' | 'overlapping' | 'capacity_exceeded'
    conflictingEvents: Array<{
      id: string
      title: string
      start: Date
      end: Date
    }>
    affectedUserIds: string[]
  }): Promise<void> {
    try {
      await CalendarNotificationService.notifyResourceConflict(
        {
          resourceId: conflictDetails.resourceId,
          resourceName: conflictDetails.resourceName,
          resourceType: conflictDetails.resourceType,
          conflictType: conflictDetails.conflictType,
          conflictingEvents: conflictDetails.conflictingEvents,
        },
        conflictDetails.affectedUserIds
      )
    } catch (error) {
      throw new Error(
        `Failed to handle resource conflict notifications: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Send calendar sharing notifications
   */
  static async sendCalendarSharingNotification(
    calendarId: string,
    calendarName: string,
    inviterName: string,
    inviterEmail: string,
    inviteeIds: string[],
    permissionLevel: 'view' | 'edit' | 'manage',
    expiresAt?: Date
  ): Promise<void> {
    try {
      for (const inviteeId of inviteeIds) {
        await CalendarNotificationService.sendEventNotification(
          'calendar_shared',
          '', // No specific event ID
          [inviteeId],
          `${inviterName} (${inviterEmail}) has shared the calendar "${calendarName}" with you.`,
          {
            invitationDetails: {
              calendarId,
              calendarName,
              inviterName,
              inviterEmail,
              permissionLevel,
              expiresAt,
            },
          }
        )
      }
    } catch (error) {
      throw new Error(
        `Failed to send calendar sharing notifications: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Get upcoming events with pending reminders
   */
  static async getUpcomingEventReminders(
    userId: string,
    hoursAhead: number = 24
  ): Promise<
    Array<{
      event: CalendarEvent
      reminders: EventReminder[]
    }>
  > {
    try {
      const events = await CalendarService.getEvents()
      const now = new Date()
      const futureTime = new Date(now.getTime() + hoursAhead * 60 * 60 * 1000)

      const upcomingEvents = events.filter((event) => {
        const eventStart = this.toDate(event.start)
        return eventStart >= now && eventStart <= futureTime
      })

      const eventsWithReminders = []

      for (const event of upcomingEvents) {
        // This would get reminders from the notification service
        // For now, return empty reminders array
        eventsWithReminders.push({
          event,
          reminders: [],
        })
      }

      return eventsWithReminders
    } catch (error) {
      throw new Error(
        `Failed to get upcoming event reminders: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }
}

// Export singleton instance for backward compatibility
export const calendarNotificationIntegration = CalendarNotificationIntegration
