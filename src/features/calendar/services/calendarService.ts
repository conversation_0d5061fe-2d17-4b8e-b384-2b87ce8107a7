import { TauriAPI } from '../../../services/tauriAPI'
import type {
  CalendarEvent,
  CreateEventDto,
  UpdateEventDto,
  CalendarEventFilter,
  CalendarStats,
  EventType,
  EventStatus,
  EventPriority,
} from '../types'

/**
 * Custom error class for calendar operations
 */
export class CalendarError extends Error {
  constructor(
    message: string,
    public cause?: Error
  ) {
    super(message)
    this.name = 'CalendarError'
  }
}

/**
 * Calendar Service
 * Provides API integration for calendar operations using TauriAPI
 * Following GT-EGA service patterns with proper error handling and data transformation
 */
export class CalendarService {
  /**
   * Safely convert DateInput to Date object
   */
  private static toDate(dateInput: any): Date {
    if (dateInput instanceof Date) {
      return dateInput
    }
    if (typeof dateInput === 'string' || typeof dateInput === 'number') {
      return new Date(dateInput)
    }
    // Handle other cases (like arrays from FullCalendar)
    if (Array.isArray(dateInput)) {
      return new Date(
        dateInput[0],
        dateInput[1],
        dateInput[2],
        dateInput[3] || 0,
        dateInput[4] || 0,
        dateInput[5] || 0
      )
    }
    // Fallback
    return new Date()
  }
  /**
   * Transform API event data to frontend CalendarEvent format
   */
  private static transformApiEvent(apiEvent: any): CalendarEvent {
    return {
      id: apiEvent.id,
      title: apiEvent.title,
      start: new Date(apiEvent.start),
      end: apiEvent.end ? new Date(apiEvent.end) : undefined,
      allDay: apiEvent.allDay || false,
      description: apiEvent.description,
      type: apiEvent.type as EventType,
      status: apiEvent.status as EventStatus,
      priority: apiEvent.priority as EventPriority,
      resourceId: apiEvent.resourceId,
      resources: apiEvent.resources,
      location: apiEvent.location,
      instructor: apiEvent.instructor,
      traineeIds: apiEvent.traineeIds,
      assessmentId: apiEvent.assessmentId,
      trainingProgramId: apiEvent.trainingProgramId,
      createdAt: apiEvent.createdAt,
      updatedAt: apiEvent.updatedAt,
      createdBy: apiEvent.createdBy,
      metadata: apiEvent.metadata,
    }
  }

  /**
   * Transform frontend CalendarEvent to API format
   */
  private static transformToApiEvent(event: CreateEventDto | UpdateEventDto): any {
    const apiEvent: any = {
      title: event.title,
      start: event.start instanceof Date ? event.start.toISOString() : event.start,
      end: event.end
        ? event.end instanceof Date
          ? event.end.toISOString()
          : event.end
        : undefined,
      allDay: event.allDay || false,
      description: event.description,
      type: event.type || 'other',
      priority: event.priority || 'medium',
      location: event.location,
      resourceId: event.resourceId,
      traineeIds: event.traineeIds,
      assessmentId: event.assessmentId,
      trainingProgramId: event.trainingProgramId,
      metadata: event.metadata,
    }

    // Add status for updates
    if ('status' in event) {
      apiEvent.status = event.status
    }

    // Add ID for updates
    if ('id' in event) {
      apiEvent.id = event.id
    }

    return apiEvent
  }

  /**
   * Fetch all events with optional filtering
   */
  static async getEvents(filter?: CalendarEventFilter): Promise<CalendarEvent[]> {
    try {
      const apiEvents = await TauriAPI.getEvents()

      let transformedEvents = apiEvents.map(this.transformApiEvent)

      // Apply client-side filtering if needed (since TauriAPI.getEvents doesn't support filters yet)
      if (filter) {
        transformedEvents = this.applyFilters(transformedEvents, filter)
      }

      return transformedEvents
    } catch (error) {
      throw new CalendarError(
        'Failed to fetch events',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Get a single event by ID
   */
  static async getEvent(id: string): Promise<CalendarEvent | null> {
    try {
      const events = await this.getEvents()
      return events.find((event) => event.id === id) || null
    } catch (error) {
      throw new CalendarError(
        'Failed to fetch event',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Create a new event
   */
  static async createEvent(eventData: CreateEventDto): Promise<CalendarEvent> {
    try {
      const apiEventData = this.transformToApiEvent(eventData)
      const response = await TauriAPI.createEvent(apiEventData)
      return this.transformApiEvent(response)
    } catch (error) {
      throw new CalendarError(
        'Failed to create event',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Update an existing event
   */
  static async updateEvent(eventData: UpdateEventDto): Promise<CalendarEvent> {
    try {
      const apiEventData = this.transformToApiEvent(eventData)
      await TauriAPI.updateEvent(eventData.id, apiEventData)

      // Return the updated event by fetching it again
      const updatedEvent = await this.getEvent(eventData.id)
      if (!updatedEvent) {
        throw new CalendarError('Event not found after update')
      }
      return updatedEvent
    } catch (error) {
      throw new CalendarError(
        'Failed to update event',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Delete an event
   */
  static async deleteEvent(id: string): Promise<void> {
    try {
      await TauriAPI.deleteEvent(id)
    } catch (error) {
      throw new CalendarError(
        'Failed to delete event',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Get calendar statistics
   */
  static async getCalendarStats(filter?: CalendarEventFilter): Promise<CalendarStats> {
    try {
      const events = await this.getEvents(filter)
      const now = new Date()
      const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000)
      const monthFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
      const weekStart = new Date(now)
      weekStart.setDate(now.getDate() - now.getDay())
      weekStart.setHours(0, 0, 0, 0)
      const monthStart = new Date(now.getFullYear(), now.getMonth(), 1)

      const eventsByType = events.reduce(
        (acc, event) => {
          const type = event.type || 'other'
          acc[type] = (acc[type] || 0) + 1
          return acc
        },
        {} as Record<EventType, number>
      )

      const eventsByStatus = events.reduce(
        (acc, event) => {
          const status = event.status || 'scheduled'
          acc[status] = (acc[status] || 0) + 1
          return acc
        },
        {} as Record<EventStatus, number>
      )

      const eventsByPriority = events.reduce(
        (acc, event) => {
          const priority = event.priority || 'medium'
          acc[priority] = (acc[priority] || 0) + 1
          return acc
        },
        {} as Record<EventPriority, number>
      )

      const upcomingEvents = events
        .filter(
          (event) => this.toDate(event.start) > now && this.toDate(event.start) <= weekFromNow
        )
        .sort((a, b) => this.toDate(a.start).getTime() - this.toDate(b.start).getTime())

      const overdueEvents = events
        .filter(
          (event) =>
            this.toDate(event.start) < now &&
            event.status !== 'completed' &&
            event.status !== 'cancelled'
        )
        .sort((a, b) => this.toDate(b.start).getTime() - this.toDate(a.start).getTime())

      const eventsThisWeek = events
        .filter(
          (event) =>
            this.toDate(event.start) >= weekStart && this.toDate(event.start) <= weekFromNow
        )
        .sort((a, b) => this.toDate(a.start).getTime() - this.toDate(b.start).getTime())

      const eventsThisMonth = events
        .filter(
          (event) =>
            this.toDate(event.start) >= monthStart && this.toDate(event.start) <= monthFromNow
        )
        .sort((a, b) => this.toDate(a.start).getTime() - this.toDate(b.start).getTime())

      return {
        totalEvents: events.length,
        eventsByType,
        eventsByStatus,
        eventsByPriority,
        upcomingEvents,
        overdueEvents,
        eventsThisWeek,
        eventsThisMonth,
      }
    } catch (error) {
      throw new CalendarError(
        'Failed to get calendar statistics',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Export calendar events
   */
  static async exportEvents(options: {
    format: 'json' | 'ics' | 'csv'
    filter?: CalendarEventFilter
  }): Promise<Blob> {
    try {
      const events = await this.getEvents(options.filter)

      switch (options.format) {
        case 'json':
          return new Blob([JSON.stringify(events, null, 2)], { type: 'application/json' })
        case 'csv':
          const csv = this.convertEventsToCSV(events)
          return new Blob([csv], { type: 'text/csv' })
        case 'ics':
          const ics = this.convertEventsToICS(events)
          return new Blob([ics], { type: 'text/calendar' })
        default:
          throw new CalendarError(`Unsupported export format: ${options.format}`)
      }
    } catch (error) {
      throw new CalendarError(
        'Failed to export events',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Import calendar events
   */
  static async importEvents(file: File): Promise<{ imported: number; errors: string[] }> {
    try {
      const content = await file.text()
      let events: any[] = []
      const errors: string[] = []

      if (file.name.endsWith('.json')) {
        try {
          events = JSON.parse(content)
        } catch (parseError) {
          errors.push('Invalid JSON format')
          return { imported: 0, errors }
        }
      } else {
        errors.push('Unsupported file format. Only JSON files are currently supported.')
        return { imported: 0, errors }
      }

      let imported = 0
      for (const eventData of events) {
        try {
          // Validate required fields
          if (!eventData.title || !eventData.start) {
            errors.push(`Invalid event data: missing title or start date`)
            continue
          }

          // Convert to CreateEventDto format
          const createDto: CreateEventDto = {
            title: eventData.title,
            start: new Date(eventData.start),
            end: eventData.end ? new Date(eventData.end) : undefined,
            allDay: eventData.allDay || false,
            description: eventData.description,
            type: eventData.type,
            priority: eventData.priority,
            location: eventData.location,
            resourceId: eventData.resourceId,
            traineeIds: eventData.traineeIds,
            assessmentId: eventData.assessmentId,
            trainingProgramId: eventData.trainingProgramId,
            metadata: eventData.metadata,
          }

          await this.createEvent(createDto)
          imported++
        } catch (eventError) {
          errors.push(
            `Failed to import event "${eventData.title}": ${eventError instanceof Error ? eventError.message : String(eventError)}`
          )
        }
      }

      return { imported, errors }
    } catch (error) {
      throw new CalendarError(
        'Failed to import events',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Sync with external calendar (Google Calendar, Outlook, etc.)
   */
  static async syncWithExternalCalendar(provider: 'google' | 'outlook' | 'apple'): Promise<void> {
    try {
      // This would be implemented when external calendar integration is added
      throw new CalendarError(`External calendar sync not implemented for ${provider}`)
    } catch (error) {
      throw new CalendarError(
        'Failed to sync with external calendar',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Get available time slots for scheduling
   */
  static async getAvailableTimeSlots(
    startDate: Date,
    endDate: Date,
    duration: number,
    resourceId?: string
  ): Promise<Date[]> {
    try {
      const events = await this.getEvents()
      const availableSlots: Date[] = []

      // Generate time slots and check for conflicts
      const currentSlot = new Date(startDate)
      const durationMs = duration * 60 * 1000 // Convert minutes to milliseconds

      while (currentSlot.getTime() + durationMs <= endDate.getTime()) {
        const slotEnd = new Date(currentSlot.getTime() + durationMs)

        // Check if slot conflicts with existing events
        const hasConflict = events.some((event) => {
          if (resourceId && event.resourceId !== resourceId) {
            return false // Skip events for different resources
          }

          const eventStart = this.toDate(event.start)
          const eventEnd = event.end
            ? this.toDate(event.end)
            : new Date(eventStart.getTime() + 60 * 60 * 1000)

          return (
            currentSlot < eventEnd && slotEnd > eventStart // Overlap check
          )
        })

        if (!hasConflict) {
          availableSlots.push(new Date(currentSlot))
        }

        // Move to next slot (30-minute intervals)
        currentSlot.setTime(currentSlot.getTime() + 30 * 60 * 1000)
      }

      return availableSlots
    } catch (error) {
      throw new CalendarError(
        'Failed to get available time slots',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Apply client-side filters to events
   */
  private static applyFilters(
    events: CalendarEvent[],
    filter: CalendarEventFilter
  ): CalendarEvent[] {
    return events.filter((event) => {
      // Type filter
      if (filter.type?.length && (!event.type || !filter.type.includes(event.type))) {
        return false
      }

      // Status filter
      if (filter.status?.length && (!event.status || !filter.status.includes(event.status))) {
        return false
      }

      // Priority filter
      if (
        filter.priority?.length &&
        (!event.priority || !filter.priority.includes(event.priority))
      ) {
        return false
      }

      // Date range filter
      if (filter.dateRange) {
        const eventStart = this.toDate(event.start)
        if (eventStart < filter.dateRange.start || eventStart > filter.dateRange.end) {
          return false
        }
      }

      // Instructor filter
      if (filter.instructor && event.instructor !== filter.instructor) {
        return false
      }

      // Location filter
      if (filter.location && event.location !== filter.location) {
        return false
      }

      // Trainee filter
      if (filter.traineeId && (!event.traineeIds || !event.traineeIds.includes(filter.traineeId))) {
        return false
      }

      // Assessment filter
      if (filter.assessmentId && event.assessmentId !== filter.assessmentId) {
        return false
      }

      // Training program filter
      if (filter.trainingProgramId && event.trainingProgramId !== filter.trainingProgramId) {
        return false
      }

      return true
    })
  }

  /**
   * Convert events to CSV format
   */
  private static convertEventsToCSV(events: CalendarEvent[]): string {
    const headers = [
      'ID',
      'Title',
      'Start',
      'End',
      'All Day',
      'Type',
      'Status',
      'Priority',
      'Location',
      'Description',
    ]
    const rows = events.map((event) => [
      event.id,
      `"${event.title}"`,
      this.toDate(event.start).toISOString(),
      event.end ? this.toDate(event.end).toISOString() : '',
      event.allDay ? 'Yes' : 'No',
      event.type || '',
      event.status || '',
      event.priority || '',
      `"${event.location || ''}"`,
      `"${event.description || ''}"`,
    ])

    return [headers.join(','), ...rows.map((row) => row.join(','))].join('\n')
  }

  /**
   * Convert events to ICS format
   */
  private static convertEventsToICS(events: CalendarEvent[]): string {
    const icsEvents = events.map((event) => {
      const start = this.toDate(event.start)
        .toISOString()
        .replace(/[-:]/g, '')
        .replace(/\.\d{3}/, '')
      const end = event.end
        ? this.toDate(event.end)
            .toISOString()
            .replace(/[-:]/g, '')
            .replace(/\.\d{3}/, '')
        : start

      return [
        'BEGIN:VEVENT',
        `UID:${event.id}`,
        `DTSTART:${start}`,
        `DTEND:${end}`,
        `SUMMARY:${event.title}`,
        event.description ? `DESCRIPTION:${event.description.replace(/\n/g, '\\n')}` : '',
        event.location ? `LOCATION:${event.location}` : '',
        'END:VEVENT',
      ]
        .filter(Boolean)
        .join('\n')
    })

    return [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//GT-EGA Calendar//EN',
      ...icsEvents,
      'END:VCALENDAR',
    ].join('\n')
  }
}

// Export singleton instance for backward compatibility
export const calendarService = CalendarService
