import { TauriAPI } from '../../../services/tauriAPI'
import { CalendarService } from './calendarService'
import type {
  CalendarEvent,
  CreateEventDto,
  UpdateEventDto,
  CalendarEventFilter,
  CalendarStats,
} from '../types'

/**
 * Pagination options
 */
export interface PaginationOptions {
  page: number
  limit: number
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}

/**
 * Paginated response
 */
export interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

/**
 * Bulk operation result
 */
export interface BulkOperationResult {
  successful: string[]
  failed: {
    id: string
    error: string
  }[]
  total: number
}

/**
 * Advanced search options
 */
export interface AdvancedSearchOptions {
  query?: string
  filters?: CalendarEventFilter
  pagination?: PaginationOptions
  includeMetadata?: boolean
  searchFields?: string[]
}

/**
 * Calendar Analytics options
 */
export interface AnalyticsOptions {
  dateRange: {
    start: Date
    end: Date
  }
  groupBy?: 'type' | 'status' | 'priority' | 'location' | 'instructor'
  includeTrends?: boolean
  metrics?: string[]
}

/**
 * Advanced Calendar Service
 * Provides API-specific features including:
 * - Pagination for large datasets
 * - Server-side filtering and search
 * - Bulk operations
 * - Advanced analytics
 * - Event export/import with progress tracking
 */
export class CalendarAdvancedService {
  /**
   * Safely convert DateInput to Date object
   */
  private static toDate(dateInput: any): Date {
    if (dateInput instanceof Date) {
      return dateInput
    }
    if (typeof dateInput === 'string' || typeof dateInput === 'number') {
      return new Date(dateInput)
    }
    // Handle other cases (like arrays from FullCalendar)
    if (Array.isArray(dateInput)) {
      return new Date(
        dateInput[0],
        dateInput[1],
        dateInput[2],
        dateInput[3] || 0,
        dateInput[4] || 0,
        dateInput[5] || 0
      )
    }
    // Fallback
    return new Date()
  }
  /**
   * Get paginated events
   */
  static async getPaginatedEvents(
    options: PaginationOptions & { filters?: CalendarEventFilter }
  ): Promise<PaginatedResponse<CalendarEvent>> {
    try {
      // For now, we'll implement client-side pagination since TauriAPI doesn't support it yet
      const allEvents = await CalendarService.getEvents(options.filters)

      // Apply sorting
      const sortedEvents = [...allEvents]
      if (options.sortBy) {
        sortedEvents.sort((a, b) => {
          const aValue = this.getEventProperty(a, options.sortBy!)
          const bValue = this.getEventProperty(b, options.sortBy!)

          if (aValue < bValue) return options.sortOrder === 'desc' ? 1 : -1
          if (aValue > bValue) return options.sortOrder === 'desc' ? -1 : 1
          return 0
        })
      }

      // Apply pagination
      const startIndex = (options.page - 1) * options.limit
      const endIndex = startIndex + options.limit
      const paginatedEvents = sortedEvents.slice(startIndex, endIndex)

      return {
        data: paginatedEvents,
        pagination: {
          page: options.page,
          limit: options.limit,
          total: allEvents.length,
          totalPages: Math.ceil(allEvents.length / options.limit),
          hasNext: endIndex < allEvents.length,
          hasPrev: options.page > 1,
        },
      }
    } catch (error) {
      throw new Error(
        `Failed to get paginated events: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Advanced search with server-side filtering
   */
  static async advancedSearch(
    options: AdvancedSearchOptions
  ): Promise<PaginatedResponse<CalendarEvent>> {
    try {
      let events = await CalendarService.getEvents(options.filters)

      // Apply text search
      if (options.query) {
        const searchFields = options.searchFields || ['title', 'description', 'location']
        const query = options.query.toLowerCase()

        events = events.filter((event) => {
          return searchFields.some((field) => {
            const value = this.getEventProperty(event, field)
            return value && value.toString().toLowerCase().includes(query)
          })
        })
      }

      // Apply pagination if specified
      if (options.pagination) {
        const startIndex = (options.pagination.page - 1) * options.pagination.limit
        const endIndex = startIndex + options.pagination.limit
        const paginatedEvents = events.slice(startIndex, endIndex)

        return {
          data: paginatedEvents,
          pagination: {
            page: options.pagination.page,
            limit: options.pagination.limit,
            total: events.length,
            totalPages: Math.ceil(events.length / options.pagination.limit),
            hasNext: endIndex < events.length,
            hasPrev: options.pagination.page > 1,
          },
        }
      }

      return {
        data: events,
        pagination: {
          page: 1,
          limit: events.length,
          total: events.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      }
    } catch (error) {
      throw new Error(
        `Advanced search failed: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Bulk create events
   */
  static async bulkCreateEvents(events: CreateEventDto[]): Promise<BulkOperationResult> {
    const result: BulkOperationResult = {
      successful: [],
      failed: [],
      total: events.length,
    }

    for (const eventData of events) {
      try {
        const createdEvent = await CalendarService.createEvent(eventData)
        result.successful.push(createdEvent.id)
      } catch (error) {
        result.failed.push({
          id: eventData.title || 'unknown',
          error: error instanceof Error ? error.message : String(error),
        })
      }
    }

    return result
  }

  /**
   * Bulk update events
   */
  static async bulkUpdateEvents(events: UpdateEventDto[]): Promise<BulkOperationResult> {
    const result: BulkOperationResult = {
      successful: [],
      failed: [],
      total: events.length,
    }

    for (const eventData of events) {
      try {
        const updatedEvent = await CalendarService.updateEvent(eventData)
        result.successful.push(updatedEvent.id)
      } catch (error) {
        result.failed.push({
          id: eventData.id,
          error: error instanceof Error ? error.message : String(error),
        })
      }
    }

    return result
  }

  /**
   * Bulk delete events
   */
  static async bulkDeleteEvents(eventIds: string[]): Promise<BulkOperationResult> {
    const result: BulkOperationResult = {
      successful: [],
      failed: [],
      total: eventIds.length,
    }

    for (const eventId of eventIds) {
      try {
        await CalendarService.deleteEvent(eventId)
        result.successful.push(eventId)
      } catch (error) {
        result.failed.push({
          id: eventId,
          error: error instanceof Error ? error.message : String(error),
        })
      }
    }

    return result
  }

  /**
   * Get calendar analytics
   */
  static async getAnalytics(options: AnalyticsOptions): Promise<any> {
    try {
      const events = await CalendarService.getEvents({
        dateRange: options.dateRange,
      })

      const analytics: any = {
        totalEvents: events.length,
        dateRange: options.dateRange,
      }

      // Group events by specified field
      if (options.groupBy) {
        const grouped = events.reduce(
          (acc, event) => {
            const key = this.getEventProperty(event, options.groupBy!) || 'unknown'
            acc[key] = (acc[key] || 0) + 1
            return acc
          },
          {} as Record<string, number>
        )

        analytics.groupedBy = grouped
      }

      // Calculate trends if requested
      if (options.includeTrends) {
        analytics.trends = this.calculateTrends(events, options.dateRange)
      }

      // Calculate custom metrics
      if (options.metrics) {
        analytics.metrics = {}
        for (const metric of options.metrics) {
          analytics.metrics[metric] = this.calculateMetric(events, metric)
        }
      }

      return analytics
    } catch (error) {
      throw new Error(
        `Failed to get analytics: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Export events with progress tracking
   */
  static async exportEventsWithProgress(
    options: { format: 'json' | 'ics' | 'csv'; filter?: CalendarEventFilter },
    onProgress?: (progress: number) => void
  ): Promise<Blob> {
    try {
      const events = await CalendarService.getEvents(options.filter)
      const totalEvents = events.length

      // Simulate progress for large exports
      if (onProgress && totalEvents > 100) {
        const chunkSize = Math.ceil(totalEvents / 10)
        for (let i = 0; i < 10; i++) {
          await new Promise((resolve) => setTimeout(resolve, 50))
          onProgress((i + 1) * 10)
        }
      }

      return await CalendarService.exportEvents(options)
    } catch (error) {
      throw new Error(`Export failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Import events with progress tracking
   */
  static async importEventsWithProgress(
    file: File,
    onProgress?: (progress: number) => void
  ): Promise<{ imported: number; errors: string[] }> {
    try {
      const content = await file.text()
      let events: any[] = []
      const errors: string[] = []

      if (file.name.endsWith('.json')) {
        try {
          events = JSON.parse(content)
        } catch (parseError) {
          errors.push('Invalid JSON format')
          return { imported: 0, errors }
        }
      } else {
        errors.push('Unsupported file format. Only JSON files are currently supported.')
        return { imported: 0, errors }
      }

      let imported = 0
      const totalEvents = events.length

      for (let i = 0; i < events.length; i++) {
        const eventData = events[i]

        try {
          // Validate required fields
          if (!eventData.title || !eventData.start) {
            errors.push(`Invalid event data: missing title or start date`)
            continue
          }

          // Convert to CreateEventDto format
          const createDto: CreateEventDto = {
            title: eventData.title,
            start: new Date(eventData.start),
            end: eventData.end ? new Date(eventData.end) : undefined,
            allDay: eventData.allDay || false,
            description: eventData.description,
            type: eventData.type,
            priority: eventData.priority,
            location: eventData.location,
            resourceId: eventData.resourceId,
            traineeIds: eventData.traineeIds,
            assessmentId: eventData.assessmentId,
            trainingProgramId: eventData.trainingProgramId,
            metadata: eventData.metadata,
          }

          await CalendarService.createEvent(createDto)
          imported++

          // Report progress
          if (onProgress) {
            onProgress(Math.round(((i + 1) / totalEvents) * 100))
          }
        } catch (eventError) {
          errors.push(
            `Failed to import event "${eventData.title}": ${eventError instanceof Error ? eventError.message : String(eventError)}`
          )
        }
      }

      return { imported, errors }
    } catch (error) {
      throw new Error(`Import failed: ${error instanceof Error ? error.message : String(error)}`)
    }
  }

  /**
   * Get event property by path (supports nested properties)
   */
  private static getEventProperty(event: CalendarEvent, path: string): any {
    return path.split('.').reduce((obj, key) => obj?.[key], event as any)
  }

  /**
   * Calculate trends for events
   */
  private static calculateTrends(
    events: CalendarEvent[],
    dateRange: { start: Date; end: Date }
  ): any {
    const dayCount = Math.ceil(
      (dateRange.end.getTime() - dateRange.start.getTime()) / (1000 * 60 * 60 * 24)
    )
    const eventsByDay = new Map<string, number>()

    // Initialize all days with 0
    for (let i = 0; i < dayCount; i++) {
      const date = new Date(dateRange.start.getTime() + i * 24 * 60 * 60 * 1000)
      const dateKey = date.toISOString().split('T')[0]
      eventsByDay.set(dateKey, 0)
    }

    // Count events per day
    events.forEach((event) => {
      const eventDate = this.toDate(event.start)
      const dateKey = eventDate.toISOString().split('T')[0]
      eventsByDay.set(dateKey, (eventsByDay.get(dateKey) || 0) + 1)
    })

    return {
      daily: Object.fromEntries(eventsByDay),
      average: events.length / dayCount,
      peak: Math.max(...eventsByDay.values()),
    }
  }

  /**
   * Calculate custom metrics
   */
  private static calculateMetric(events: CalendarEvent[], metric: string): any {
    switch (metric) {
      case 'averageDuration':
        const durations = events
          .filter((event) => event.end)
          .map((event) => this.toDate(event.end!).getTime() - this.toDate(event.start).getTime())
        return durations.length > 0 ? durations.reduce((a, b) => a + b, 0) / durations.length : 0

      case 'completionRate':
        const completed = events.filter((event) => event.status === 'completed').length
        return events.length > 0 ? (completed / events.length) * 100 : 0

      case 'utilizationRate':
        // Calculate time utilization (events with resources vs total)
        const withResources = events.filter((event) => event.resourceId).length
        return events.length > 0 ? (withResources / events.length) * 100 : 0

      default:
        return null
    }
  }

  /**
   * Get resource utilization analytics
   */
  static async getResourceUtilization(
    resourceId: string,
    dateRange: { start: Date; end: Date }
  ): Promise<any> {
    try {
      // Get all events and filter by resourceId client-side since it's not in the filter type yet
      const allEvents = await CalendarService.getEvents({
        dateRange,
      })
      const events = allEvents.filter((event) => event.resourceId === resourceId)

      const totalDuration = events.reduce((total, event) => {
        const start = this.toDate(event.start)
        const end = event.end ? this.toDate(event.end) : new Date(start.getTime() + 60 * 60 * 1000)
        return total + (end.getTime() - start.getTime())
      }, 0)

      const totalTime = dateRange.end.getTime() - dateRange.start.getTime()
      const utilizationRate = (totalDuration / totalTime) * 100

      return {
        resourceId,
        utilizationRate,
        totalEvents: events.length,
        totalDuration,
        availableTime: totalTime - totalDuration,
        events: events.map((event) => ({
          id: event.id,
          title: event.title,
          start: event.start,
          end: event.end,
          duration: event.end
            ? this.toDate(event.end).getTime() - this.toDate(event.start).getTime()
            : 0,
        })),
      }
    } catch (error) {
      throw new Error(
        `Failed to get resource utilization: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }

  /**
   * Get conflict analysis for scheduling
   */
  static async getConflictAnalysis(
    events: CreateEventDto[]
  ): Promise<{ conflicts: any[]; suggestions: any[] }> {
    try {
      const existingEvents = await CalendarService.getEvents()
      const conflicts: any[] = []
      const suggestions: any[] = []

      for (const newEvent of events) {
        const newStart = this.toDate(newEvent.start)
        const newEnd = newEvent.end
          ? this.toDate(newEvent.end)
          : new Date(newStart.getTime() + 60 * 60 * 1000)

        // Check for conflicts with existing events
        const conflictingEvents = existingEvents.filter((existing) => {
          if (newEvent.resourceId && existing.resourceId !== newEvent.resourceId) {
            return false
          }

          const existingStart = this.toDate(existing.start)
          const existingEnd = existing.end
            ? this.toDate(existing.end)
            : new Date(existingStart.getTime() + 60 * 60 * 1000)

          return (
            newStart < existingEnd && newEnd > existingStart // Overlap check
          )
        })

        if (conflictingEvents.length > 0) {
          conflicts.push({
            newEvent,
            conflictingEvents,
            type: 'resource_conflict',
          })

          // Generate suggestions
          const availableSlots = await CalendarService.getAvailableTimeSlots(
            newStart,
            newEnd,
            60, // 1 hour duration
            newEvent.resourceId
          )

          if (availableSlots.length > 0) {
            suggestions.push({
              newEvent,
              suggestedSlots: availableSlots.slice(0, 3), // Top 3 suggestions
              type: 'reschedule_suggestion',
            })
          }
        }
      }

      return { conflicts, suggestions }
    } catch (error) {
      throw new Error(
        `Failed to get conflict analysis: ${error instanceof Error ? error.message : String(error)}`
      )
    }
  }
}

// Export singleton instance for easy usage
export const calendarAdvancedService = CalendarAdvancedService
