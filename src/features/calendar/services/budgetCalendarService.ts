import type {
  BudgetCalendarEvent,
  BudgetCalendarFilter,
  BudgetCalendarStats,
} from '../types/budgetCalendar'

/**
 * Service for managing budget calendar events and integration
 */
export class BudgetCalendarService {
  /**
   * Get budget calendar events with optional filtering
   */
  static async getBudgetCalendarEvents(
    filter?: BudgetCalendarFilter
  ): Promise<BudgetCalendarEvent[]> {
    try {
      // Mock budget calendar events - in real implementation, this would fetch from API
      const mockEvents: BudgetCalendarEvent[] = [
        {
          id: 'budget-1-creation',
          title: 'Budget Created: Q1 Training Budget',
          start: '2024-01-01',
          allDay: true,
          budgetType: 'budget_creation',
          budgetId: 'budget-1',
          budgetAmount: 50000,
          currency: 'USD',
          budgetStatus: 'approved',
          description: 'Training budget created for Q1 training program',
          extendedProps: {
            eventType: 'budget_creation',
          },
        },
        {
          id: 'budget-1-expiry',
          title: 'Budget Expires: Q1 Training Budget',
          start: '2024-03-31',
          allDay: true,
          budgetType: 'budget_expiry',
          budgetId: 'budget-1',
          budgetAmount: 50000,
          currency: 'USD',
          budgetStatus: 'in_progress',
          description: 'Training budget expires on March 31, 2024',
          extendedProps: {
            eventType: 'budget_expiry',
          },
        },
        {
          id: 'training-1-session',
          title: 'Training: Q1 Training Program',
          start: '2024-01-15',
          end: '2024-03-30',
          budgetType: 'training_session',
          budgetId: 'budget-1',
          budgetAmount: 50000,
          currency: 'USD',
          budgetStatus: 'in_progress',
          utilizationPercentage: 50,
          location: 'Training Center',
          description: 'Q1 training program for new engineers',
          extendedProps: {
            eventType: 'training_session',
          },
        },
        {
          id: 'budget-2-creation',
          title: 'Budget Created: React Development Training',
          start: '2024-02-01',
          allDay: true,
          budgetType: 'budget_creation',
          budgetId: 'budget-2',
          budgetAmount: 30000,
          currency: 'USD',
          budgetStatus: 'approved',
          description: 'Training budget created for React development program',
          extendedProps: {
            eventType: 'budget_creation',
          },
        },
        {
          id: 'training-2-session',
          title: 'Training: React Development Program',
          start: '2024-02-15',
          end: '2024-04-30',
          budgetType: 'training_session',
          budgetId: 'budget-2',
          budgetAmount: 30000,
          currency: 'USD',
          budgetStatus: 'approved',
          utilizationPercentage: 75,
          location: 'Conference Room B',
          description: 'Advanced React development training program',
          extendedProps: {
            eventType: 'training_session',
          },
        },
      ]

      let events = mockEvents

      // Apply filters
      if (filter) {
        events = this.applyFilters(events, filter)
      }

      return events
    } catch (error) {
      console.error('Error fetching budget calendar events:', error)
      throw error
    }
  }

  /**
   * Get budget calendar statistics
   */
  static async getBudgetCalendarStats(filter?: BudgetCalendarFilter): Promise<BudgetCalendarStats> {
    try {
      const events = await this.getBudgetCalendarEvents(filter)

      const totalBudgetAmount = events.reduce((sum, event) => sum + (event.budgetAmount || 0), 0)
      const totalSpent = events.reduce((sum, event) => sum + (event.spentAmount || 0), 0)
      const averageUtilization =
        events.length > 0
          ? events.reduce((sum, event) => sum + (event.utilizationPercentage || 0), 0) /
            events.length
          : 0

      const overdueBudgets = events.filter((event) => {
        if (!event.end) return false
        const eventEnd =
          typeof event.end === 'string' ? new Date(event.end) : new Date(event.end as any)
        return eventEnd < new Date() && event.budgetStatus !== 'completed'
      })

      const upcomingDeadlines = events
        .filter((event) => event.end && new Date(event.end as string) > new Date())
        .sort((a, b) => {
          const aEnd = new Date(a.end as string)
          const bEnd = new Date(b.end as string)
          return aEnd.getTime() - bEnd.getTime()
        })
        .slice(0, 5)

      return {
        totalBudgetAmount,
        totalSpent,
        averageUtilization,

        overdueBudgets: overdueBudgets,
        upcomingDeadlines: upcomingDeadlines,
      }
    } catch (error) {
      console.error('Error fetching budget calendar stats:', error)
      throw error
    }
  }

  /**
   * Apply filters to events
   */
  private static applyFilters(
    events: BudgetCalendarEvent[],
    filter: BudgetCalendarFilter
  ): BudgetCalendarEvent[] {
    return events.filter((event) => {
      // Budget type filter
      if (
        filter.budgetType?.length &&
        (!event.budgetType || !filter.budgetType.includes(event.budgetType))
      ) {
        return false
      }

      // Budget status filter
      if (
        filter.budgetStatus?.length &&
        (!event.budgetStatus || !filter.budgetStatus.includes(event.budgetStatus))
      ) {
        return false
      }

      // Budget ID filter
      if (
        filter.budgetId?.length &&
        (!event.budgetId || !filter.budgetId.includes(event.budgetId))
      ) {
        return false
      }

      return true
    })
  }

  /**
   * Export calendar events to different formats
   */
  static async exportEvents(
    events: BudgetCalendarEvent[],
    format: 'ical' | 'csv' | 'json'
  ): Promise<string> {
    switch (format) {
      case 'json':
        return JSON.stringify(events, null, 2)

      case 'csv':
        const headers = ['ID', 'Title', 'Start', 'End', 'Budget Type', 'Budget Amount', 'Status']
        const rows = events.map((event) => [
          event.id,
          event.title,
          event.start,
          event.end || '',
          event.budgetType || '',
          event.budgetAmount?.toString() || '',
          event.budgetStatus || '',
        ])
        return [headers, ...rows].map((row) => row.join(',')).join('\n')

      case 'ical':
        return this.generateICal(events)

      default:
        throw new Error(`Unsupported export format: ${format}`)
    }
  }

  /**
   * Generate iCal format
   */
  private static generateICal(events: BudgetCalendarEvent[]): string {
    const icalEvents = events.map((event) => {
      const startDate = new Date(event.start)
        .toISOString()
        .replace(/[-:]/g, '')
        .replace(/\.\d{3}/, '')
      const endDate = event.end
        ? new Date(event.end)
            .toISOString()
            .replace(/[-:]/g, '')
            .replace(/\.\d{3}/, '')
        : startDate

      return [
        'BEGIN:VEVENT',
        `UID:${event.id}`,
        `DTSTART:${startDate}`,
        `DTEND:${endDate}`,
        `SUMMARY:${event.title}`,
        `DESCRIPTION:${event.description || ''}`,
        'END:VEVENT',
      ].join('\r\n')
    })

    return [
      'BEGIN:VCALENDAR',
      'VERSION:2.0',
      'PRODID:-//GT-EGA Budget Calendar//EN',
      ...icalEvents,
      'END:VCALENDAR',
    ].join('\r\n')
  }

  /**
   * Group events by budget type
   */
  private static groupByBudgetType(events: BudgetCalendarEvent[]): Record<string, number> {
    return events.reduce(
      (acc, event) => {
        const type = event.budgetType || 'unknown'
        acc[type] = (acc[type] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )
  }

  /**
   * Group events by status
   */
  private static groupByStatus(events: BudgetCalendarEvent[]): Record<string, number> {
    return events.reduce(
      (acc, event) => {
        const status = event.budgetStatus || 'unknown'
        acc[status] = (acc[status] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )
  }

  /**
   * Get utilization distribution
   */
  private static getUtilizationDistribution(events: BudgetCalendarEvent[]): {
    low: number
    medium: number
    high: number
    critical: number
  } {
    const distribution = {
      low: 0,
      medium: 0,
      high: 0,
      critical: 0,
    }

    events.forEach((event) => {
      const utilization = event.utilizationPercentage || 0
      if (utilization < 25) distribution.low++
      else if (utilization < 50) distribution.medium++
      else if (utilization < 75) distribution.high++
      else distribution.critical++
    })

    return distribution
  }
}
