import type {
  Resource,
  ResourceBooking,
  ResourceConflict,
  ResourceAvailability,
  ResourceSchedulingRequest,
  ResourceSchedulingResponse,
  UtilizationMetrics,
  ResourceSearchFilter,
  ResourceAnalytics,
  MaintenanceSchedule,
  TimeSlot,
  AvailabilityPattern,
  BookingStatus,
  ResourceStatus,
  BookingPriority,
} from '../types/resources'
import type { EntityId } from '../../../shared/types/common'

/**
 * Comprehensive resource scheduling service
 * Provides functionality for resource management, booking, conflict detection, and analytics
 */
export class ResourceSchedulingService {
  // In-memory storage for demo purposes (replace with actual API calls)
  private static resources: Map<EntityId, Resource> = new Map()
  private static bookings: Map<EntityId, ResourceBooking> = new Map()
  private static conflicts: Map<string, ResourceConflict> = new Map()

  /**
   * Resource Management Methods
   */

  /**
   * Get all resources with optional filtering
   */
  static async getResources(filter?: ResourceSearchFilter): Promise<Resource[]> {
    // Simulate API delay
    await new Promise((resolve) => setTimeout(resolve, 100))

    let resources = Array.from(this.resources.values())

    if (filter) {
      resources = this.filterResources(resources, filter)
    }

    return resources
  }

  /**
   * Get a specific resource by ID
   */
  static async getResource(id: EntityId): Promise<Resource | null> {
    await new Promise((resolve) => setTimeout(resolve, 50))
    return this.resources.get(id) || null
  }

  /**
   * Create a new resource
   */
  static async createResource(
    resourceData: Omit<Resource, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<Resource> {
    await new Promise((resolve) => setTimeout(resolve, 100))

    const now = new Date().toISOString()
    const resource: Resource = {
      ...resourceData,
      id: this.generateId(),
      createdAt: now,
      updatedAt: now,
      bookings: [],
    }

    this.resources.set(resource.id, resource)
    return resource
  }

  /**
   * Update an existing resource
   */
  static async updateResource(id: EntityId, updates: Partial<Resource>): Promise<Resource> {
    await new Promise((resolve) => setTimeout(resolve, 100))

    const existing = this.resources.get(id)
    if (!existing) {
      throw new Error(`Resource with ID ${id} not found`)
    }

    const updated: Resource = {
      ...existing,
      ...updates,
      id,
      updatedAt: new Date().toISOString(),
    }

    this.resources.set(id, updated)
    return updated
  }

  /**
   * Delete a resource
   */
  static async deleteResource(id: EntityId): Promise<void> {
    await new Promise((resolve) => setTimeout(resolve, 50))

    const resource = this.resources.get(id)
    if (!resource) {
      throw new Error(`Resource with ID ${id} not found`)
    }

    // Check for active bookings
    const activeBookings = resource.bookings.filter((booking) =>
      ['pending', 'confirmed'].includes(booking.status)
    )

    if (activeBookings.length > 0) {
      throw new Error(`Cannot delete resource with ${activeBookings.length} active bookings`)
    }

    this.resources.delete(id)
  }

  /**
   * Resource Availability Methods
   */

  /**
   * Check if a resource is available for a specific time period
   */
  static async checkResourceAvailability(
    resourceId: EntityId,
    startTime: Date,
    endTime: Date
  ): Promise<boolean> {
    await new Promise((resolve) => setTimeout(resolve, 50))

    const resource = this.resources.get(resourceId)
    if (!resource) {
      throw new Error(`Resource with ID ${resourceId} not found`)
    }

    // Check resource status
    if (resource.status !== 'available') {
      return false
    }

    // Check for conflicting bookings
    const bookings = await this.getResourceBookings(resourceId, startTime, endTime)
    const hasConflict = bookings.some((booking) => this.isTimeConflict(booking, startTime, endTime))

    return !hasConflict
  }

  /**
   * Get resource availability for a date range
   */
  static async getResourceAvailability(
    resourceId: EntityId,
    startDate: Date,
    endDate: Date
  ): Promise<ResourceAvailability[]> {
    await new Promise((resolve) => setTimeout(resolve, 100))

    const resource = this.resources.get(resourceId)
    if (!resource) {
      throw new Error(`Resource with ID ${resourceId} not found`)
    }

    const availability: ResourceAvailability[] = []
    const currentDate = new Date(startDate)

    while (currentDate <= endDate) {
      const dayAvailability = this.calculateDayAvailability(resource, currentDate)
      availability.push(dayAvailability)
      currentDate.setDate(currentDate.getDate() + 1)
    }

    return availability
  }

  /**
   * Resource Booking Methods
   */

  /**
   * Book a resource for a specific time period
   */
  static async bookResource(
    request: ResourceSchedulingRequest
  ): Promise<ResourceSchedulingResponse> {
    await new Promise((resolve) => setTimeout(resolve, 100))

    const resource = this.resources.get(request.resourceId)
    if (!resource) {
      return {
        success: false,
        message: `Resource with ID ${request.resourceId} not found`,
      }
    }

    // Check availability
    const isAvailable = await this.checkResourceAvailability(
      request.resourceId,
      request.startTime,
      request.endTime
    )

    if (!isAvailable) {
      const conflicts = await this.detectConflicts(
        request.resourceId,
        request.startTime,
        request.endTime
      )
      return {
        success: false,
        conflicts,
        message: 'Resource not available for requested time',
      }
    }

    // Create booking
    const booking: ResourceBooking = {
      id: this.generateId(),
      resourceId: request.resourceId,
      eventId: request.eventId,
      title: request.title,
      startTime: request.startTime,
      endTime: request.endTime,
      status: 'confirmed',
      priority: request.priority,
      requestedBy: request.requestedBy,
      notes: request.notes,
      metadata: request.metadata,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      requestedAt: new Date().toISOString(),
      approvedAt: new Date().toISOString(),
    }

    this.bookings.set(booking.id, booking)

    // Update resource bookings
    resource.bookings.push(booking)
    this.resources.set(resource.id, resource)

    return {
      success: true,
      booking,
      message: 'Resource booked successfully',
    }
  }

  /**
   * Get all bookings for a resource
   */
  static async getResourceBookings(
    resourceId: EntityId,
    startDate?: Date,
    endDate?: Date
  ): Promise<ResourceBooking[]> {
    await new Promise((resolve) => setTimeout(resolve, 50))

    const resource = this.resources.get(resourceId)
    if (!resource) {
      throw new Error(`Resource with ID ${resourceId} not found`)
    }

    let bookings = resource.bookings

    if (startDate && endDate) {
      bookings = bookings.filter((booking) => {
        const bookingStart = new Date(booking.startTime)
        const bookingEnd = new Date(booking.endTime)
        return bookingStart <= endDate && bookingEnd >= startDate
      })
    }

    return bookings.sort(
      (a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime()
    )
  }

  /**
   * Update a booking
   */
  static async updateBooking(
    bookingId: EntityId,
    updates: Partial<ResourceBooking>
  ): Promise<ResourceBooking> {
    await new Promise((resolve) => setTimeout(resolve, 100))

    const booking = this.bookings.get(bookingId)
    if (!booking) {
      throw new Error(`Booking with ID ${bookingId} not found`)
    }

    const updated: ResourceBooking = {
      ...booking,
      ...updates,
      id: bookingId,
      updatedAt: new Date().toISOString(),
    }

    this.bookings.set(bookingId, updated)

    // Update resource bookings
    const resource = this.resources.get(booking.resourceId)
    if (resource) {
      const bookingIndex = resource.bookings.findIndex((b) => b.id === bookingId)
      if (bookingIndex !== -1) {
        resource.bookings[bookingIndex] = updated
        this.resources.set(resource.id, resource)
      }
    }

    return updated
  }

  /**
   * Cancel a booking
   */
  static async cancelBooking(bookingId: EntityId, reason?: string): Promise<ResourceBooking> {
    await new Promise((resolve) => setTimeout(resolve, 50))

    return this.updateBooking(bookingId, {
      status: 'cancelled',
      notes: reason,
      cancelledAt: new Date().toISOString(),
    })
  }

  /**
   * Conflict Detection and Resolution
   */

  /**
   * Detect conflicts for a resource booking
   */
  static async detectConflicts(
    resourceId: EntityId,
    startTime: Date,
    endTime: Date
  ): Promise<ResourceConflict[]> {
    await new Promise((resolve) => setTimeout(resolve, 50))

    const conflicts: ResourceConflict[] = []
    const bookings = await this.getResourceBookings(resourceId)

    const conflictingBookings = bookings.filter(
      (booking) =>
        this.isTimeConflict(booking, startTime, endTime) &&
        ['pending', 'confirmed'].includes(booking.status)
    )

    conflictingBookings.forEach((booking) => {
      const conflict: ResourceConflict = {
        id: this.generateId(),
        resourceId,
        bookingIds: [booking.id],
        conflictType: 'overlap',
        severity: 'high',
        description: `Booking conflicts with existing booking: ${booking.title}`,
        resolved: false,
      }
      conflicts.push(conflict)
      this.conflicts.set(conflict.id, conflict)
    })

    return conflicts
  }

  /**
   * Get all conflicts for a resource
   */
  static async getResourceConflicts(resourceId: EntityId): Promise<ResourceConflict[]> {
    await new Promise((resolve) => setTimeout(resolve, 50))

    return Array.from(this.conflicts.values()).filter(
      (conflict) => conflict.resourceId === resourceId
    )
  }

  /**
   * Resolve a conflict
   */
  static async resolveConflict(
    conflictId: string,
    resolution: string,
    resolvedBy: string
  ): Promise<ResourceConflict> {
    await new Promise((resolve) => setTimeout(resolve, 50))

    const conflict = this.conflicts.get(conflictId)
    if (!conflict) {
      throw new Error(`Conflict with ID ${conflictId} not found`)
    }

    const resolved: ResourceConflict = {
      ...conflict,
      resolved: true,
      resolvedAt: new Date().toISOString(),
      resolvedBy,
    }

    this.conflicts.set(conflictId, resolved)
    return resolved
  }

  /**
   * Resource Utilization Analytics
   */

  /**
   * Calculate utilization metrics for a resource
   */
  static async getResourceUtilization(
    resourceId: EntityId,
    dateRange: { start: Date; end: Date }
  ): Promise<UtilizationMetrics> {
    await new Promise((resolve) => setTimeout(resolve, 100))

    const resource = this.resources.get(resourceId)
    if (!resource) {
      throw new Error(`Resource with ID ${resourceId} not found`)
    }

    const bookings = await this.getResourceBookings(resourceId, dateRange.start, dateRange.end)
    const confirmedBookings = bookings.filter(
      (b) => b.status === 'completed' || b.status === 'confirmed'
    )

    // Calculate total booked hours
    const totalHours = confirmedBookings.reduce((total, booking) => {
      const duration =
        (new Date(booking.endTime).getTime() - new Date(booking.startTime).getTime()) /
        (1000 * 60 * 60)
      return total + duration
    }, 0)

    // Calculate total available hours
    const totalDays = Math.ceil(
      (dateRange.end.getTime() - dateRange.start.getTime()) / (1000 * 60 * 60 * 24)
    )
    const totalAvailableHours = totalDays * 24 // Assuming 24-hour availability

    // Calculate utilization rate
    const utilizationRate = totalAvailableHours > 0 ? (totalHours / totalAvailableHours) * 100 : 0

    // Calculate bookings by status
    const bookingsByStatus: Record<BookingStatus, number> = {
      pending: 0,
      confirmed: 0,
      cancelled: 0,
      completed: 0,
      no_show: 0,
      rejected: 0,
    }

    bookings.forEach((booking) => {
      bookingsByStatus[booking.status]++
    })

    return {
      resourceId,
      dateRange,
      totalBookings: bookings.length,
      totalHours,
      utilizationRate,
      averageBookingDuration:
        confirmedBookings.length > 0 ? (totalHours / confirmedBookings.length) * 60 : 0, // in minutes
      noShowRate: bookings.length > 0 ? (bookingsByStatus.no_show / bookings.length) * 100 : 0,
      bookingsByStatus,
      utilizationByDay: {},
      utilizationByWeek: {},
      utilizationByMonth: {},
    }
  }

  /**
   * Get overall resource analytics
   */
  static async getResourceAnalytics(dateRange: {
    start: Date
    end: Date
  }): Promise<ResourceAnalytics> {
    await new Promise((resolve) => setTimeout(resolve, 200))

    const resources = Array.from(this.resources.values())
    const totalResources = resources.length

    // Calculate resources by type
    const resourcesByType: Record<string, number> = {}
    resources.forEach((resource) => {
      resourcesByType[resource.type] = (resourcesByType[resource.type] || 0) + 1
    })

    // Calculate resources by status
    const resourcesByStatus: Record<ResourceStatus, number> = {
      available: 0,
      occupied: 0,
      maintenance: 0,
      unavailable: 0,
      reserved: 0,
      retired: 0,
    }
    resources.forEach((resource) => {
      resourcesByStatus[resource.status]++
    })

    // Calculate average utilization
    const utilizationMetrics = await Promise.all(
      resources.map((resource) => this.getResourceUtilization(resource.id, dateRange))
    )

    const averageUtilizationRate =
      utilizationMetrics.length > 0
        ? utilizationMetrics.reduce((sum, metrics) => sum + metrics.utilizationRate, 0) /
          utilizationMetrics.length
        : 0

    // Get top utilized resources
    const topUtilizedResources = utilizationMetrics
      .sort((a, b) => b.utilizationRate - a.utilizationRate)
      .slice(0, 5)
      .map((metrics) => {
        const resource = resources.find((r) => r.id === metrics.resourceId)!
        return {
          resourceId: metrics.resourceId,
          name: resource.name,
          utilizationRate: metrics.utilizationRate,
          totalBookings: metrics.totalBookings,
        }
      })

    // Calculate conflict stats
    const allConflicts = Array.from(this.conflicts.values())
    const resolvedConflicts = allConflicts.filter((c) => c.resolved)
    const avgResolutionTime = resolvedConflicts.length > 0 ? 24 : 0 // Simplified calculation

    return {
      totalResources,
      resourcesByType: resourcesByType as any,
      resourcesByStatus,
      averageUtilizationRate,
      totalRevenue: 0, // Would be calculated based on booking costs
      topUtilizedResources,
      conflictStats: {
        totalConflicts: allConflicts.length,
        resolvedConflicts: resolvedConflicts.length,
        avgResolutionTime,
      },
      trends: {
        utilization: [],
        bookings: [],
        conflicts: [],
      },
    }
  }

  /**
   * Maintenance Schedule Methods
   */

  /**
   * Schedule maintenance for a resource
   */
  static async scheduleMaintenance(
    resourceId: EntityId,
    maintenance: Omit<MaintenanceSchedule, 'id' | 'createdAt' | 'updatedAt'>
  ): Promise<MaintenanceSchedule> {
    await new Promise((resolve) => setTimeout(resolve, 100))

    const resource = this.resources.get(resourceId)
    if (!resource) {
      throw new Error(`Resource with ID ${resourceId} not found`)
    }

    const now = new Date().toISOString()
    const newMaintenance: MaintenanceSchedule = {
      ...maintenance,
      id: this.generateId(),
      resourceId,
      createdAt: now,
      updatedAt: now,
    }

    if (!resource.maintenanceSchedule) {
      resource.maintenanceSchedule = []
    }
    resource.maintenanceSchedule.push(newMaintenance)

    // Update resource status if maintenance is starting
    if (new Date(maintenance.scheduledStart) <= new Date()) {
      resource.status = 'maintenance'
    }

    this.resources.set(resourceId, resource)
    return newMaintenance
  }

  /**
   * Utility Methods
   */

  /**
   * Filter resources based on search criteria
   */
  private static filterResources(resources: Resource[], filter: ResourceSearchFilter): Resource[] {
    return resources.filter((resource) => {
      // Filter by type
      if (filter.type && filter.type.length > 0 && !filter.type.includes(resource.type)) {
        return false
      }

      // Filter by status
      if (filter.status && filter.status.length > 0 && !filter.status.includes(resource.status)) {
        return false
      }

      // Filter by location
      if (
        filter.location &&
        resource.location?.toLowerCase().indexOf(filter.location.toLowerCase()) === -1
      ) {
        return false
      }

      // Filter by capacity
      if (filter.capacity) {
        if (
          filter.capacity.min &&
          (!resource.capacity || resource.capacity < filter.capacity.min)
        ) {
          return false
        }
        if (
          filter.capacity.max &&
          (!resource.capacity || resource.capacity > filter.capacity.max)
        ) {
          return false
        }
      }

      // Filter by search text
      if (filter.searchText) {
        const searchText = filter.searchText.toLowerCase()
        const searchFields = [resource.name, resource.description, resource.location].filter(
          Boolean
        )
        const matchesSearch = searchFields.some(
          (field) => field!.toLowerCase().indexOf(searchText) !== -1
        )
        if (!matchesSearch) {
          return false
        }
      }

      // Filter by availability for date range
      if (filter.available && filter.dateRange) {
        // This would require checking availability for the date range
        // For now, just check if resource is marked as available
        if (resource.status !== 'available') {
          return false
        }
      }

      return true
    })
  }

  /**
   * Check if two time periods conflict
   */
  private static isTimeConflict(booking: ResourceBooking, startTime: Date, endTime: Date): boolean {
    const bookingStart = new Date(booking.startTime)
    const bookingEnd = new Date(booking.endTime)

    return (
      (startTime < bookingEnd && endTime > bookingStart) ||
      (startTime <= bookingStart && endTime >= bookingEnd) ||
      (startTime >= bookingStart && startTime < bookingEnd) ||
      (endTime > bookingStart && endTime <= bookingEnd)
    )
  }

  /**
   * Calculate availability for a specific day
   */
  private static calculateDayAvailability(resource: Resource, date: Date): ResourceAvailability {
    const dayOfWeek = date.getDay()
    const timeSlots: TimeSlot[] = []

    // Default availability (9 AM - 5 PM on weekdays)
    if (dayOfWeek >= 1 && dayOfWeek <= 5) {
      timeSlots.push({
        id: this.generateId(),
        startTime: '09:00',
        endTime: '17:00',
        daysOfWeek: [dayOfWeek],
        available: true,
        maxConcurrentBookings: resource.capacity || 1,
      })
    }

    // Check for maintenance on this day
    const hasMaintenance = resource.maintenanceSchedule?.some((maintenance) => {
      const maintenanceStart = new Date(maintenance.scheduledStart)
      const maintenanceEnd = new Date(maintenance.scheduledEnd)
      return date >= maintenanceStart && date <= maintenanceEnd
    })

    return {
      id: this.generateId(),
      resourceId: resource.id,
      date,
      timeSlots,
      patterns: [],
      isAvailable: resource.status === 'available' && !hasMaintenance,
      utilizationRate: 0, // Would be calculated based on bookings
    }
  }

  /**
   * Generate a unique ID
   */
  private static generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  /**
   * Initialize demo data
   */
  static async initializeDemoData(): Promise<void> {
    // Create demo resources
    const demoResources: Omit<Resource, 'id' | 'createdAt' | 'updatedAt'>[] = [
      {
        name: 'Conference Room A',
        type: 'room',
        description: 'Large conference room with projector and whiteboard',
        location: 'Building 1, Floor 2',
        status: 'available',
        capacity: 20,
        availability: [],
        metadata: {
          capacity: 20,
          hasProjector: true,
          hasWhiteboard: true,
          hasComputer: true,
          floor: 2,
          building: 'Building 1',
        },
        isActive: true,
        public: true,
        tags: ['conference', 'large', 'projector'],
        category: 'Meeting Rooms',
        department: 'Facilities',
        bookings: [],
      },
      {
        name: 'Training Room B',
        type: 'room',
        description: 'Medium-sized training room with computers',
        location: 'Building 2, Floor 1',
        status: 'available',
        capacity: 15,
        availability: [],
        metadata: {
          capacity: 15,
          hasProjector: true,
          hasWhiteboard: true,
          hasComputer: true,
          floor: 1,
          building: 'Building 2',
        },
        isActive: true,
        public: true,
        tags: ['training', 'computers', 'medium'],
        category: 'Training Rooms',
        department: 'Training',
        bookings: [],
      },
      {
        name: 'John Smith',
        type: 'instructor',
        description: 'Senior software development instructor',
        status: 'available',
        capacity: 1,
        availability: [],
        metadata: {
          specialties: ['JavaScript', 'React', 'Node.js'],
          certifications: ['AWS', 'Google Cloud'],
          maxConcurrentSessions: 3,
          rating: 4.8,
          hourlyRate: 150,
        },
        isActive: true,
        public: true,
        tags: ['senior', 'javascript', 'react'],
        category: 'Technical Instructors',
        department: 'Training',
        bookings: [],
      },
      {
        name: 'Projector 001',
        type: 'equipment',
        description: 'High-definition portable projector',
        location: 'Equipment Room',
        status: 'available',
        capacity: 1,
        availability: [],
        metadata: {
          brand: 'Epson',
          model: 'PowerLite',
          category: 'Audio/Visual',
          specifications: {
            resolution: '1920x1080',
            brightness: '5000 lumens',
          },
        },
        isActive: true,
        public: true,
        tags: ['projector', 'portable', 'hd'],
        category: 'Audio/Visual Equipment',
        department: 'Facilities',
        bookings: [],
      },
    ]

    for (const resourceData of demoResources) {
      const resource = await this.createResource(resourceData)
      this.resources.set(resource.id, resource)
    }
  }
}

// Export singleton instance
export const resourceSchedulingService = ResourceSchedulingService
