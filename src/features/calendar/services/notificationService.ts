import { TauriAPI } from '../../../services/tauriAPI'
import { CalendarService } from './calendarService'
import type {
  CalendarNotification,
  EventReminder,
  NotificationSchedule,
  NotificationPreferences,
  NotificationHistoryEntry,
  NotificationTemplate,
  NotificationBatch,
  NotificationStats,
  NotificationFilter,
  NotificationDeliveryOptions,
  CalendarNotificationType,
  NotificationStatus,
  ReminderType,
  NotificationPriority,
  ReminderMetadata,
  NotificationMetadata,
  ConflictDetails,
  InvitationDetails,
} from '../types'
import type { DateInput } from '@fullcalendar/core'

/**
 * Custom error class for calendar notification operations
 */
export class CalendarNotificationError extends Error {
  constructor(
    message: string,
    public cause?: Error
  ) {
    super(message)
    this.name = 'CalendarNotificationError'
  }
}

/**
 * Calendar Notification Service
 * Provides comprehensive notification management for calendar events
 * Integrates with GT-EGA's notification system for seamless user experience
 */
export class CalendarNotificationService {
  /**
   * Safely convert DateInput to Date object
   */
  private static toDate(dateInput: DateInput): Date {
    if (dateInput instanceof Date) {
      return dateInput
    }
    if (typeof dateInput === 'string' || typeof dateInput === 'number') {
      return new Date(dateInput)
    }
    // Handle other cases (like arrays from FullCalendar)
    if (Array.isArray(dateInput)) {
      return new Date(
        dateInput[0],
        dateInput[1],
        dateInput[2],
        dateInput[3] || 0,
        dateInput[4] || 0,
        dateInput[5] || 0
      )
    }
    // Fallback
    return new Date()
  }
  /**
   * Schedule an event reminder for a user
   */
  static async scheduleEventReminder(
    eventId: string,
    userId: string,
    reminderTime: Date,
    reminderType: ReminderType,
    metadata?: Partial<ReminderMetadata>
  ): Promise<EventReminder> {
    try {
      // Get event details
      const event = await CalendarService.getEvent(eventId)
      if (!event) {
        throw new CalendarNotificationError(`Event not found: ${eventId}`)
      }

      // Calculate reminder offset
      const eventStart = this.toDate(event.start)
      const reminderOffset = (eventStart.getTime() - reminderTime.getTime()) / (1000 * 60) // minutes

      // Create reminder metadata
      const reminderMetadata: ReminderMetadata = {
        eventId,
        eventTitle: event.title,
        eventStart,
        eventEnd: event.end ? this.toDate(event.end) : undefined,
        reminderOffset,
        reminderType,
        ...metadata,
      }

      // Create reminder object
      const reminder: EventReminder = {
        id: `reminder_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        eventId,
        userId,
        reminderType,
        scheduledFor: reminderTime,
        sent: false,
        metadata: reminderMetadata,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }

      // Schedule the notification
      await this.scheduleNotification(reminder)

      return reminder
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to schedule event reminder',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Send an event notification to multiple users
   */
  static async sendEventNotification(
    type: CalendarNotificationType,
    eventId: string,
    userIds: string[],
    message?: string,
    metadata?: Partial<NotificationMetadata>
  ): Promise<CalendarNotification[]> {
    try {
      // Get event details
      const event = await CalendarService.getEvent(eventId)
      if (!event) {
        throw new CalendarNotificationError(`Event not found: ${eventId}`)
      }

      // Generate notification content
      const { title, notificationMessage } = this.generateNotificationContent(type, event, message)

      // Create notifications for each user
      const notifications: CalendarNotification[] = []

      for (const userId of userIds) {
        const notification: CalendarNotification = {
          id: `notif_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type,
          title,
          message: notificationMessage,
          eventId,
          userId,
          scheduledFor: new Date(),
          status: 'pending',
          priority: this.getNotificationPriority(type),
          reminderType: 'in_app',
          metadata: {
            eventId,
            eventTitle: event.title,
            eventStart: this.toDate(event.start),
            eventEnd: event.end ? this.toDate(event.end) : undefined,
            eventLocation: event.location,
            eventInstructor: event.instructor,
            ...metadata,
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }

        notifications.push(notification)
      }

      // Send notifications via GT-EGA's notification system
      await this.sendNotifications(notifications)

      return notifications
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to send event notification',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Notify users about event changes
   */
  static async notifyEventChange(
    eventId: string,
    changeType: 'created' | 'updated' | 'deleted' | 'cancelled',
    affectedUsers: string[],
    changes?: Record<string, { old: unknown; new: unknown }>
  ): Promise<void> {
    try {
      const event = await CalendarService.getEvent(eventId)
      if (!event && changeType !== 'deleted') {
        throw new CalendarNotificationError(`Event not found: ${eventId}`)
      }

      const notificationType = `event_${changeType}` as CalendarNotificationType
      const message = this.generateChangeMessage(event, changeType, changes)

      await this.sendEventNotification(notificationType, eventId, affectedUsers, message, {
        changeType,
        changes,
      })
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to notify event change',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Notify about resource conflicts
   */
  static async notifyResourceConflict(
    conflictDetails: ConflictDetails,
    affectedUsers: string[]
  ): Promise<CalendarNotification[]> {
    try {
      const title = `Resource Conflict: ${conflictDetails.resourceName}`
      const message = this.generateConflictMessage(conflictDetails)

      const notifications: CalendarNotification[] = []

      for (const userId of affectedUsers) {
        const notification: CalendarNotification = {
          id: `conflict_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: 'resource_conflict',
          title,
          message,
          userId,
          scheduledFor: new Date(),
          status: 'pending',
          priority: 'high',
          reminderType: 'in_app',
          metadata: {
            resourceType: conflictDetails.resourceType,
            resourceName: conflictDetails.resourceName,
            conflictDetails,
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }

        notifications.push(notification)
      }

      await this.sendNotifications(notifications)
      return notifications
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to notify resource conflict',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Send training deadline notifications
   */
  static async sendTrainingDeadlineNotifications(
    trainingProgramId: string,
    deadlineType: 'milestone' | 'completion',
    deadlineDate: Date,
    userIds: string[]
  ): Promise<CalendarNotification[]> {
    try {
      const notificationType =
        deadlineType === 'milestone' ? 'training_deadline' : 'training_deadline'
      const title = `Training ${deadlineType === 'milestone' ? 'Milestone' : 'Completion'} Deadline`
      const message = `Your training program has an upcoming ${deadlineType} deadline on ${deadlineDate.toLocaleDateString()}.`

      const notifications: CalendarNotification[] = []

      for (const userId of userIds) {
        const notification: CalendarNotification = {
          id: `training_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: notificationType,
          title,
          message,
          userId,
          scheduledFor: new Date(),
          status: 'pending',
          priority: 'medium',
          reminderType: 'in_app',
          metadata: {
            trainingProgramId,
            deadlineType,
            deadlineDate,
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }

        notifications.push(notification)
      }

      await this.sendNotifications(notifications)
      return notifications
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to send training deadline notifications',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Send assessment reminder notifications
   */
  static async sendAssessmentReminders(
    assessmentId: string,
    reminderType: 'deadline' | 'review',
    reminderDate: Date,
    userIds: string[]
  ): Promise<CalendarNotification[]> {
    try {
      const title = `Assessment ${reminderType === 'deadline' ? 'Deadline' : 'Review'} Reminder`
      const message = `You have an assessment ${reminderType} approaching on ${reminderDate.toLocaleDateString()}.`

      const notifications: CalendarNotification[] = []

      for (const userId of userIds) {
        const notification: CalendarNotification = {
          id: `assessment_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          type: 'assessment_reminder',
          title,
          message,
          userId,
          scheduledFor: new Date(),
          status: 'pending',
          priority: 'medium',
          reminderType: 'in_app',
          metadata: {
            assessmentId,
            reminderType,
            reminderDate,
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
        }

        notifications.push(notification)
      }

      await this.sendNotifications(notifications)
      return notifications
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to send assessment reminders',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Get user notification preferences
   */
  static async getUserNotificationPreferences(
    userId: string
  ): Promise<NotificationPreferences | null> {
    try {
      // This would integrate with the user preferences API
      // For now, return default preferences
      return {
        userId,
        enabled: true,
        eventReminders: {
          enabled: true,
          defaultReminders: [
            { value: 15, unit: 'minutes', beforeEvent: true },
            { value: 1, unit: 'hours', beforeEvent: true },
            { value: 1, unit: 'days', beforeEvent: true },
          ],
          customReminders: [],
        },
        changeNotifications: {
          enabled: true,
          eventCreated: true,
          eventUpdated: true,
          eventDeleted: true,
          eventCancelled: true,
        },
        resourceNotifications: {
          enabled: true,
          conflicts: true,
          availability: false,
        },
        trainingNotifications: {
          enabled: true,
          deadlines: true,
          milestones: true,
          completion: true,
        },
        assessmentNotifications: {
          enabled: true,
          deadlines: true,
          reviews: true,
          results: true,
        },
        sharingNotifications: {
          enabled: true,
          invitations: true,
          acceptance: true,
        },
        channels: {
          email: true,
          push: true,
          inApp: true,
          sms: false,
        },
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00',
          timezone: 'UTC',
        },
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to get user notification preferences',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Update user notification preferences
   */
  static async updateUserNotificationPreferences(
    userId: string,
    preferences: Partial<NotificationPreferences>
  ): Promise<NotificationPreferences> {
    try {
      const currentPreferences = await this.getUserNotificationPreferences(userId)
      if (!currentPreferences) {
        throw new CalendarNotificationError('User preferences not found')
      }

      const updatedPreferences: NotificationPreferences = {
        ...currentPreferences,
        ...preferences,
        userId,
        updatedAt: new Date().toISOString(),
      }

      // This would save to the user preferences API
      return updatedPreferences
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to update user notification preferences',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Get notification history for a user
   */
  static async getNotificationHistory(
    userId: string,
    filter?: NotificationFilter
  ): Promise<CalendarNotification[]> {
    try {
      // This would integrate with the notification history API
      // For now, return empty array
      return []
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to get notification history',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Get notification statistics
   */
  static async getNotificationStats(userId?: string): Promise<NotificationStats> {
    try {
      // This would integrate with the notification analytics API
      // For now, return default stats
      return {
        totalNotifications: 0,
        notificationsByType: {} as Record<CalendarNotificationType, number>,
        notificationsByStatus: {} as Record<NotificationStatus, number>,
        notificationsByChannel: {} as Record<ReminderType, number>,
        sentToday: 0,
        sentThisWeek: 0,
        sentThisMonth: 0,
        deliveryRate: 0,
        readRate: 0,
        failureRate: 0,
        averageDeliveryTime: 0,
      }
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to get notification statistics',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Schedule a notification
   */
  private static async scheduleNotification(reminder: EventReminder): Promise<void> {
    try {
      // This would integrate with the task scheduling system
      // For now, we'll simulate scheduling
      console.log(`Scheduling reminder for event ${reminder.eventId} at ${reminder.scheduledFor}`)
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to schedule notification',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Send notifications via GT-EGA's notification system
   */
  private static async sendNotifications(notifications: CalendarNotification[]): Promise<void> {
    try {
      // This would integrate with GT-EGA's notification center
      // For now, we'll simulate sending
      for (const notification of notifications) {
        console.log(`Sending notification: ${notification.title} to user ${notification.userId}`)

        // Update status to sent
        notification.status = 'sent'
        notification.sentAt = new Date()
        notification.updatedAt = new Date().toISOString()
      }
    } catch (error) {
      throw new CalendarNotificationError(
        'Failed to send notifications',
        error instanceof Error ? error : new Error(String(error))
      )
    }
  }

  /**
   * Generate notification content based on type and event
   */
  private static generateNotificationContent(
    type: CalendarNotificationType,
    event: any,
    customMessage?: string
  ): { title: string; notificationMessage: string } {
    const eventTitle = event.title
    const eventDate = event.start ? new Date(event.start).toLocaleDateString() : 'Unknown date'
    const eventTime = event.start ? new Date(event.start).toLocaleTimeString() : 'Unknown time'

    switch (type) {
      case 'event_created':
        return {
          title: 'New Event Created',
          notificationMessage:
            customMessage ||
            `Event "${eventTitle}" has been scheduled for ${eventDate} at ${eventTime}.`,
        }
      case 'event_updated':
        return {
          title: 'Event Updated',
          notificationMessage: customMessage || `Event "${eventTitle}" has been updated.`,
        }
      case 'event_deleted':
        return {
          title: 'Event Deleted',
          notificationMessage: customMessage || `Event "${eventTitle}" has been deleted.`,
        }
      case 'event_cancelled':
        return {
          title: 'Event Cancelled',
          notificationMessage: customMessage || `Event "${eventTitle}" has been cancelled.`,
        }
      case 'event_reminder':
        return {
          title: 'Event Reminder',
          notificationMessage: customMessage || `Reminder: "${eventTitle}" is starting soon.`,
        }
      default:
        return {
          title: 'Calendar Notification',
          notificationMessage: customMessage || `Notification about event "${eventTitle}".`,
        }
    }
  }

  /**
   * Generate change message for event updates
   */
  private static generateChangeMessage(
    event: any,
    changeType: 'created' | 'updated' | 'deleted' | 'cancelled',
    changes?: Record<string, { old: unknown; new: unknown }>
  ): string {
    const eventTitle = event?.title || 'Unknown event'

    switch (changeType) {
      case 'created':
        return `New event "${eventTitle}" has been created.`
      case 'updated':
        if (changes) {
          const changeList = Object.entries(changes)
            .map(([field, { old, new: newVal }]) => `${field}: ${old} → ${newVal}`)
            .join(', ')
          return `Event "${eventTitle}" has been updated: ${changeList}`
        }
        return `Event "${eventTitle}" has been updated.`
      case 'deleted':
        return `Event "${eventTitle}" has been deleted.`
      case 'cancelled':
        return `Event "${eventTitle}" has been cancelled.`
      default:
        return `Event "${eventTitle}" has been changed.`
    }
  }

  /**
   * Generate conflict message for resource conflicts
   */
  private static generateConflictMessage(conflictDetails: ConflictDetails): string {
    const { resourceName, conflictType, conflictingEvents } = conflictDetails

    switch (conflictType) {
      case 'double_booking':
        return `Resource "${resourceName}" is double booked with ${conflictingEvents.length} other events.`
      case 'overlapping':
        return `Resource "${resourceName}" has overlapping scheduling with ${conflictingEvents.length} other events.`
      case 'capacity_exceeded':
        return `Resource "${resourceName}" capacity exceeded for ${conflictingEvents.length} events.`
      default:
        return `Resource "${resourceName}" has scheduling conflicts.`
    }
  }

  /**
   * Get notification priority based on type
   */
  private static getNotificationPriority(type: CalendarNotificationType): NotificationPriority {
    switch (type) {
      case 'resource_conflict':
        return 'high'
      case 'event_reminder':
      case 'training_deadline':
      case 'assessment_reminder':
        return 'medium'
      case 'event_created':
      case 'event_updated':
      case 'event_deleted':
      case 'event_cancelled':
        return 'low'
      default:
        return 'medium'
    }
  }
}

// Export singleton instance for backward compatibility
export const calendarNotificationService = CalendarNotificationService
