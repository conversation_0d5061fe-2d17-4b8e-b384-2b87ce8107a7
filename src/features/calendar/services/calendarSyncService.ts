import { CalendarService } from './calendarService'
import type { CalendarEvent, CalendarEventFilter } from '../types'

/**
 * Sync status types
 */
export type SyncStatus = 'idle' | 'syncing' | 'success' | 'error'

/**
 * Conflict resolution strategies
 */
export type ConflictResolution = 'local' | 'remote' | 'merge' | 'manual'

/**
 * Sync conflict information
 */
export interface SyncConflict {
  id: string
  localEvent: CalendarEvent
  remoteEvent: CalendarEvent
  conflictType: 'update' | 'delete'
  timestamp: Date
}

/**
 * Sync result information
 */
export interface SyncResult {
  status: SyncStatus
  conflicts: SyncConflict[]
  created: number
  updated: number
  deleted: number
  errors: string[]
  lastSyncTime: Date
}

/**
 * Sync configuration
 */
export interface SyncConfig {
  autoSync: boolean
  syncInterval: number // in milliseconds
  retryAttempts: number
  retryDelay: number // in milliseconds
  conflictResolution: ConflictResolution
  enableOfflineSupport: boolean
}

/**
 * Calendar Sync Service
 * Provides advanced synchronization capabilities including:
 * - Real-time synchronization
 * - Conflict detection and resolution
 * - Offline support
 * - Background sync
 * - Optimistic updates
 */
export class CalendarSyncService {
  private static instance: CalendarSyncService
  private syncConfig: SyncConfig = {
    autoSync: true,
    syncInterval: 30000, // 30 seconds
    retryAttempts: 3,
    retryDelay: 1000, // 1 second
    conflictResolution: 'merge',
    enableOfflineSupport: true,
  }
  private syncTimer: NodeJS.Timeout | null = null
  private isOnline: boolean = navigator.onLine
  private pendingOperations: Map<string, () => Promise<any>> = new Map()
  private lastSyncTime: Date | null = null
  private syncStatus: SyncStatus = 'idle'

  private constructor() {
    this.setupEventListeners()
  }

  static getInstance(): CalendarSyncService {
    if (!CalendarSyncService.instance) {
      CalendarSyncService.instance = new CalendarSyncService()
    }
    return CalendarSyncService.instance
  }

  /**
   * Configure sync settings
   */
  configure(config: Partial<SyncConfig>): void {
    this.syncConfig = { ...this.syncConfig, ...config }

    if (this.syncConfig.autoSync) {
      this.startAutoSync()
    } else {
      this.stopAutoSync()
    }
  }

  /**
   * Get current sync configuration
   */
  getConfig(): SyncConfig {
    return { ...this.syncConfig }
  }

  /**
   * Get current sync status
   */
  getSyncStatus(): SyncStatus {
    return this.syncStatus
  }

  /**
   * Get last sync time
   */
  getLastSyncTime(): Date | null {
    return this.lastSyncTime
  }

  /**
   * Start automatic synchronization
   */
  startAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
    }

    this.syncTimer = setInterval(() => {
      if (this.isOnline) {
        this.performSync()
      }
    }, this.syncConfig.syncInterval)
  }

  /**
   * Stop automatic synchronization
   */
  stopAutoSync(): void {
    if (this.syncTimer) {
      clearInterval(this.syncTimer)
      this.syncTimer = null
    }
  }

  /**
   * Perform manual synchronization
   */
  async performSync(): Promise<SyncResult> {
    this.syncStatus = 'syncing'

    try {
      const result = await this.syncWithRetry()
      this.lastSyncTime = new Date()
      this.syncStatus = 'success'

      // Process pending operations
      await this.processPendingOperations()

      return result
    } catch (error) {
      this.syncStatus = 'error'
      throw error
    }
  }

  /**
   * Synchronize with retry logic
   */
  private async syncWithRetry(attempt = 1): Promise<SyncResult> {
    try {
      return await this.doSync()
    } catch (error) {
      if (attempt < this.syncConfig.retryAttempts) {
        await this.delay(this.syncConfig.retryDelay * attempt)
        return this.syncWithRetry(attempt + 1)
      }
      throw error
    }
  }

  /**
   * Perform actual synchronization
   */
  private async doSync(): Promise<SyncResult> {
    const result: SyncResult = {
      status: 'success',
      conflicts: [],
      created: 0,
      updated: 0,
      deleted: 0,
      errors: [],
      lastSyncTime: new Date(),
    }

    try {
      // Get local events (from cache/indexedDB if offline)
      const localEvents = await this.getLocalEvents()

      // Get remote events
      const remoteEvents = await CalendarService.getEvents()

      // Detect conflicts
      const conflicts = this.detectConflicts(localEvents, remoteEvents)
      result.conflicts = conflicts

      // Resolve conflicts
      for (const conflict of conflicts) {
        await this.resolveConflict(conflict)
      }

      // Apply remote changes to local
      await this.applyRemoteChanges(localEvents, remoteEvents, result)

      // Apply local changes to remote
      await this.applyLocalChanges(localEvents, remoteEvents, result)

      result.status = 'success'
    } catch (error) {
      result.status = 'error'
      result.errors.push(error instanceof Error ? error.message : String(error))
    }

    return result
  }

  /**
   * Get local events (from cache or indexedDB)
   */
  private async getLocalEvents(): Promise<CalendarEvent[]> {
    try {
      // Try to get from IndexedDB first
      const cachedEvents = await this.getCachedEvents()
      if (cachedEvents.length > 0) {
        return cachedEvents
      }

      // Fallback to API if online
      if (this.isOnline) {
        return await CalendarService.getEvents()
      }

      return []
    } catch (error) {
      console.warn('Failed to get local events:', error)
      return []
    }
  }

  /**
   * Get cached events from IndexedDB
   */
  private async getCachedEvents(): Promise<CalendarEvent[]> {
    return new Promise((resolve) => {
      try {
        const request = indexedDB.open('GT-EGA-Calendar', 1)

        request.onerror = () => resolve([])

        request.onsuccess = (event) => {
          const db = (event.target as IDBOpenDBRequest).result

          if (!db.objectStoreNames.contains('events')) {
            resolve([])
            return
          }

          const transaction = db.transaction(['events'], 'readonly')
          const store = transaction.objectStore('events')
          const getAllRequest = store.getAll()

          getAllRequest.onsuccess = () => {
            resolve(getAllRequest.result || [])
          }

          getAllRequest.onerror = () => resolve([])
        }
      } catch (error) {
        resolve([])
      }
    })
  }

  /**
   * Cache events to IndexedDB
   */
  private async cacheEvents(events: CalendarEvent[]): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const request = indexedDB.open('GT-EGA-Calendar', 1)

        request.onerror = () => reject(new Error('Failed to open database'))

        request.onupgradeneeded = (event) => {
          const db = (event.target as IDBOpenDBRequest).result
          if (!db.objectStoreNames.contains('events')) {
            db.createObjectStore('events', { keyPath: 'id' })
          }
        }

        request.onsuccess = (event) => {
          const db = (event.target as IDBOpenDBRequest).result
          const transaction = db.transaction(['events'], 'readwrite')
          const store = transaction.objectStore('events')

          // Clear existing events
          store.clear()

          // Add new events
          events.forEach((event) => {
            store.add(event)
          })

          transaction.oncomplete = () => resolve()
          transaction.onerror = () => reject(new Error('Failed to cache events'))
        }
      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * Detect conflicts between local and remote events
   */
  private detectConflicts(
    localEvents: CalendarEvent[],
    remoteEvents: CalendarEvent[]
  ): SyncConflict[] {
    const conflicts: SyncConflict[] = []
    const localEventMap = new Map(localEvents.map((e) => [e.id, e]))
    const remoteEventMap = new Map(remoteEvents.map((e) => [e.id, e]))

    // Check for update conflicts
    for (const [id, localEvent] of localEventMap) {
      const remoteEvent = remoteEventMap.get(id)
      if (remoteEvent && this.hasUpdateConflict(localEvent, remoteEvent)) {
        conflicts.push({
          id,
          localEvent,
          remoteEvent,
          conflictType: 'update',
          timestamp: new Date(),
        })
      }
    }

    return conflicts
  }

  /**
   * Check if two events have update conflicts
   */
  private hasUpdateConflict(localEvent: CalendarEvent, remoteEvent: CalendarEvent): boolean {
    const localUpdated = new Date(localEvent.updatedAt || '')
    const remoteUpdated = new Date(remoteEvent.updatedAt || '')

    // If remote was updated after local, there might be a conflict
    return remoteUpdated > localUpdated
  }

  /**
   * Resolve a sync conflict
   */
  private async resolveConflict(conflict: SyncConflict): Promise<void> {
    switch (this.syncConfig.conflictResolution) {
      case 'local':
        // Keep local version, push to remote
        await CalendarService.updateEvent(conflict.localEvent)
        break

      case 'remote':
        // Keep remote version, update local
        await this.updateLocalEvent(conflict.remoteEvent)
        break

      case 'merge':
        // Attempt to merge changes
        const mergedEvent = this.mergeEvents(conflict.localEvent, conflict.remoteEvent)
        await CalendarService.updateEvent(mergedEvent)
        await this.updateLocalEvent(mergedEvent)
        break

      case 'manual':
        // Store conflict for manual resolution
        await this.storeConflictForManualResolution(conflict)
        break
    }
  }

  /**
   * Merge two events
   */
  private mergeEvents(localEvent: CalendarEvent, remoteEvent: CalendarEvent): CalendarEvent {
    const localUpdated = new Date(localEvent.updatedAt || '')
    const remoteUpdated = new Date(remoteEvent.updatedAt || '')

    // Use the most recently updated version as base
    const baseEvent = localUpdated > remoteUpdated ? localEvent : remoteEvent
    const otherEvent = localUpdated > remoteUpdated ? remoteEvent : localEvent

    // Merge non-conflicting fields
    return {
      ...baseEvent,
      // Keep the most recent values for these fields
      title: baseEvent.title,
      description: baseEvent.description || otherEvent.description,
      location: baseEvent.location || otherEvent.location,
      // Merge arrays
      traineeIds: [...new Set([...(baseEvent.traineeIds || []), ...(otherEvent.traineeIds || [])])],
      // Merge metadata
      metadata: { ...(baseEvent.metadata || {}), ...(otherEvent.metadata || {}) },
      updatedAt: new Date().toISOString(),
    }
  }

  /**
   * Update local event in cache
   */
  private async updateLocalEvent(event: CalendarEvent): Promise<void> {
    const cachedEvents = await this.getCachedEvents()
    const index = cachedEvents.findIndex((e) => e.id === event.id)

    if (index >= 0) {
      cachedEvents[index] = event
      await this.cacheEvents(cachedEvents)
    }
  }

  /**
   * Store conflict for manual resolution
   */
  private async storeConflictForManualResolution(conflict: SyncConflict): Promise<void> {
    // Store in IndexedDB for later resolution
    try {
      const request = indexedDB.open('GT-EGA-Calendar', 1)

      request.onsuccess = (event) => {
        const db = (event.target as IDBOpenDBRequest).result

        if (!db.objectStoreNames.contains('conflicts')) {
          const store = db.createObjectStore('conflicts', { keyPath: 'id' })
          store.add(conflict)
        } else {
          const transaction = db.transaction(['conflicts'], 'readwrite')
          const store = transaction.objectStore('conflicts')
          store.add(conflict)
        }
      }
    } catch (error) {
      console.warn('Failed to store conflict for manual resolution:', error)
    }
  }

  /**
   * Apply remote changes to local cache
   */
  private async applyRemoteChanges(
    localEvents: CalendarEvent[],
    remoteEvents: CalendarEvent[],
    result: SyncResult
  ): Promise<void> {
    const localEventMap = new Map(localEvents.map((e) => [e.id, e]))
    const remoteEventMap = new Map(remoteEvents.map((e) => [e.id, e]))
    const updatedLocalEvents: CalendarEvent[] = [...localEvents]

    // Find new and updated remote events
    for (const [id, remoteEvent] of remoteEventMap) {
      const localEvent = localEventMap.get(id)

      if (!localEvent) {
        // New event
        updatedLocalEvents.push(remoteEvent)
        result.created++
      } else if (new Date(remoteEvent.updatedAt || '') > new Date(localEvent.updatedAt || '')) {
        // Updated event
        const index = updatedLocalEvents.findIndex((e) => e.id === id)
        if (index >= 0) {
          updatedLocalEvents[index] = remoteEvent
          result.updated++
        }
      }
    }

    // Find deleted events
    for (const [id, localEvent] of localEventMap) {
      if (!remoteEventMap.has(id)) {
        const index = updatedLocalEvents.findIndex((e) => e.id === id)
        if (index >= 0) {
          updatedLocalEvents.splice(index, 1)
          result.deleted++
        }
      }
    }

    // Update cache
    await this.cacheEvents(updatedLocalEvents)
  }

  /**
   * Apply local changes to remote
   */
  private async applyLocalChanges(
    localEvents: CalendarEvent[],
    remoteEvents: CalendarEvent[],
    result: SyncResult
  ): Promise<void> {
    const remoteEventMap = new Map(remoteEvents.map((e) => [e.id, e]))

    // Find local changes that need to be pushed to remote
    for (const localEvent of localEvents) {
      const remoteEvent = remoteEventMap.get(localEvent.id)

      if (!remoteEvent) {
        // New local event, create on remote
        try {
          await CalendarService.createEvent(localEvent)
          result.created++
        } catch (error) {
          result.errors.push(`Failed to create event ${localEvent.id}: ${error}`)
        }
      } else if (new Date(localEvent.updatedAt || '') > new Date(remoteEvent.updatedAt || '')) {
        // Updated local event, update on remote
        try {
          await CalendarService.updateEvent(localEvent)
          result.updated++
        } catch (error) {
          result.errors.push(`Failed to update event ${localEvent.id}: ${error}`)
        }
      }
    }
  }

  /**
   * Process pending operations
   */
  private async processPendingOperations(): Promise<void> {
    const operations = Array.from(this.pendingOperations.values())

    for (const operation of operations) {
      try {
        await operation()
      } catch (error) {
        console.warn('Failed to process pending operation:', error)
      }
    }

    this.pendingOperations.clear()
  }

  /**
   * Add pending operation for when we come back online
   */
  addPendingOperation(id: string, operation: () => Promise<any>): void {
    this.pendingOperations.set(id, operation)
  }

  /**
   * Setup event listeners for online/offline status
   */
  private setupEventListeners(): void {
    window.addEventListener('online', () => {
      this.isOnline = true
      if (this.syncConfig.autoSync) {
        this.performSync()
      }
    })

    window.addEventListener('offline', () => {
      this.isOnline = false
    })
  }

  /**
   * Delay helper
   */
  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * Cleanup
   */
  destroy(): void {
    this.stopAutoSync()
    this.pendingOperations.clear()
  }
}

// Export singleton instance
export const calendarSyncService = CalendarSyncService.getInstance()
