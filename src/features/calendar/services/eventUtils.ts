import type { CalendarEvent } from '../types'

let eventGuid = 0
const todayStr = new Date().toISOString().replace(/T.*$/, '') // YYYY-MM-DD of today
const tomorrowStr = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString().replace(/T.*$/, '')
const nextWeekStr = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString().replace(/T.*$/, '')

export const INITIAL_EVENTS: CalendarEvent[] = [
  {
    id: createEventId(),
    title: 'Training Session: React Fundamentals',
    start: todayStr,
    allDay: true,
    type: 'training_session',
    description: 'Introduction to React components and hooks',
  },
  {
    id: createEventId(),
    title: 'Assessment: JavaScript Skills',
    start: todayStr + 'T14:00:00',
    end: todayStr + 'T16:00:00',
    type: 'assessment',
    description: 'JavaScript proficiency assessment',
  },
  {
    id: createEventId(),
    title: 'Team Meeting',
    start: tomorrowStr + 'T10:00:00',
    end: tomorrowStr + 'T11:00:00',
    type: 'meeting',
    description: 'Weekly team sync',
  },
  {
    id: createEventId(),
    title: 'System Maintenance',
    start: nextWeekStr + 'T02:00:00',
    end: nextWeekStr + 'T04:00:00',
    type: 'maintenance',
    description: 'Scheduled system maintenance',
  },
  {
    id: createEventId(),
    title: 'Project Deadline',
    start: nextWeekStr + 'T17:00:00',
    type: 'deadline',
    description: 'Q4 project submission deadline',
  },
  {
    id: createEventId(),
    title: 'Code Review',
    start: todayStr + 'T15:00:00',
    end: todayStr + 'T16:00:00',
    type: 'review',
    description: 'Review pull requests',
  },
  {
    id: createEventId(),
    title: 'Public Holiday',
    start: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString().replace(/T.*$/, ''),
    allDay: true,
    type: 'holiday',
    description: 'National holiday',
  },
  {
    id: createEventId(),
    title: 'General Task',
    start: todayStr + 'T09:00:00',
    end: todayStr + 'T10:00:00',
    type: 'other',
    description: 'Miscellaneous task',
  },
]

export function createEventId(): string {
  return String(eventGuid++)
}

export function getEventColor(type: string): string {
  const colors: Record<string, string> = {
    training_session: 'hsl(var(--primary))',
    assessment: 'hsl(142 76% 36%)',
    meeting: 'hsl(217 91% 60%)',
    maintenance: 'hsl(43 96% 56%)',
    holiday: 'hsl(346 87% 43%)',
    deadline: 'hsl(var(--destructive))',
    review: 'hsl(262 83% 58%)',
    other: 'hsl(var(--secondary))',
  }
  return colors[type] || colors.other
}

export function getEventIcon(type: string): string {
  const icons: Record<string, string> = {
    training_session: '📚',
    assessment: '📝',
    meeting: '👥',
    maintenance: '🔧',
    holiday: '🎉',
    deadline: '⏰',
    review: '👀',
    other: '📌',
  }
  return icons[type] || icons.other
}
