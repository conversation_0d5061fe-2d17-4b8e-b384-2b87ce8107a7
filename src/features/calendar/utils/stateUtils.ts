import type {
  CalendarEvent,
  CalendarEventFilter,
  EventType,
  EventStatus,
  EventPriority,
} from '../types'
import type { DateInput } from '@fullcalendar/core'

/**
 * Utility functions for calendar state management
 */

/**
 * Safely convert DateInput to Date object
 */
export const toDate = (dateInput: DateInput): Date => {
  if (dateInput instanceof Date) {
    return dateInput
  }
  if (typeof dateInput === 'string' || typeof dateInput === 'number') {
    return new Date(dateInput)
  }
  // Handle other cases (like arrays from FullCalendar)
  if (Array.isArray(dateInput)) {
    return new Date(
      dateInput[0],
      dateInput[1],
      dateInput[2],
      dateInput[3] || 0,
      dateInput[4] || 0,
      dateInput[5] || 0
    )
  }
  // Fallback
  return new Date()
}

/**
 * Normalize calendar events to a consistent format
 */
export const normalizeEvent = (event: Partial<CalendarEvent>): CalendarEvent => {
  return {
    id: event.id || '',
    title: event.title || '',
    start: event.start || new Date(),
    end: event.end,
    allDay: event.allDay || false,
    description: event.description || '',
    type: event.type || 'other',
    status: event.status || 'scheduled',
    priority: event.priority || 'medium',
    resourceId: event.resourceId,
    resources: event.resources || [],
    location: event.location || '',
    instructor: event.instructor || '',
    traineeIds: event.traineeIds || [],
    assessmentId: event.assessmentId,
    trainingProgramId: event.trainingProgramId,
    createdAt: event.createdAt,
    updatedAt: event.updatedAt,
    createdBy: event.createdBy,
    metadata: event.metadata || {},
  }
}

/**
 * Normalize an array of calendar events
 */
export const normalizeEvents = (events: Partial<CalendarEvent>[]): CalendarEvent[] => {
  return events.map(normalizeEvent)
}

/**
 * Convert events array to a normalized record (object with IDs as keys)
 */
export const eventsToRecord = (events: CalendarEvent[]): Record<string, CalendarEvent> => {
  return events.reduce(
    (acc, event) => {
      acc[event.id] = event
      return acc
    },
    {} as Record<string, CalendarEvent>
  )
}

/**
 * Convert events record back to array
 */
export const recordToEvents = (eventsRecord: Record<string, CalendarEvent>): CalendarEvent[] => {
  return Object.values(eventsRecord)
}

/**
 * Validate calendar event data
 */
export const validateEvent = (
  event: Partial<CalendarEvent>
): { isValid: boolean; errors: string[] } => {
  const errors: string[] = []

  if (!event.title || event.title.trim().length === 0) {
    errors.push('Event title is required')
  }

  if (!event.start) {
    errors.push('Event start date is required')
  }

  if (event.start && event.end && toDate(event.start) >= toDate(event.end)) {
    errors.push('Event end date must be after start date')
  }

  if (event.type && !isValidEventType(event.type)) {
    errors.push('Invalid event type')
  }

  if (event.status && !isValidEventStatus(event.status)) {
    errors.push('Invalid event status')
  }

  if (event.priority && !isValidEventPriority(event.priority)) {
    errors.push('Invalid event priority')
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * Check if a value is a valid event type
 */
export const isValidEventType = (type: string): type is EventType => {
  const validTypes: EventType[] = [
    'training_session',
    'assessment',
    'meeting',
    'maintenance',
    'holiday',
    'deadline',
    'review',
    'other',
  ]
  return validTypes.includes(type as EventType)
}

/**
 * Check if a value is a valid event status
 */
export const isValidEventStatus = (status: string): status is EventStatus => {
  const validStatuses: EventStatus[] = [
    'scheduled',
    'in_progress',
    'completed',
    'cancelled',
    'postponed',
  ]
  return validStatuses.includes(status as EventStatus)
}

/**
 * Check if a value is a valid event priority
 */
export const isValidEventPriority = (priority: string): priority is EventPriority => {
  const validPriorities: EventPriority[] = ['low', 'medium', 'high', 'urgent']
  return validPriorities.includes(priority as EventPriority)
}

/**
 * Sanitize event data before storing
 */
export const sanitizeEvent = (event: Partial<CalendarEvent>): Partial<CalendarEvent> => {
  const sanitized: Partial<CalendarEvent> = {
    ...event,
    title: event.title?.trim() || '',
    description: event.description?.trim() || '',
    location: event.location?.trim() || '',
    instructor: event.instructor?.trim() || '',
  }

  // Ensure valid enum values
  if (sanitized.type && !isValidEventType(sanitized.type)) {
    sanitized.type = 'other'
  }

  if (sanitized.status && !isValidEventStatus(sanitized.status)) {
    sanitized.status = 'scheduled'
  }

  if (sanitized.priority && !isValidEventPriority(sanitized.priority)) {
    sanitized.priority = 'medium'
  }

  return sanitized
}

/**
 * Apply filters to events array
 */
export const applyFilters = (
  events: CalendarEvent[],
  filters: CalendarEventFilter,
  searchQuery: string = ''
): CalendarEvent[] => {
  let filtered = [...events]

  // Apply type filter
  if (filters.type?.length) {
    filtered = filtered.filter((event) => event.type && filters.type!.includes(event.type))
  }

  // Apply status filter
  if (filters.status?.length) {
    filtered = filtered.filter((event) => event.status && filters.status!.includes(event.status))
  }

  // Apply priority filter
  if (filters.priority?.length) {
    filtered = filtered.filter(
      (event) => event.priority && filters.priority!.includes(event.priority)
    )
  }

  // Apply date range filter
  if (filters.dateRange) {
    filtered = filtered.filter((event) => {
      const eventStart = toDate(event.start)
      return eventStart >= filters.dateRange!.start && eventStart <= filters.dateRange!.end
    })
  }

  // Apply instructor filter
  if (filters.instructor) {
    filtered = filtered.filter((event) => event.instructor === filters.instructor)
  }

  // Apply location filter
  if (filters.location) {
    filtered = filtered.filter((event) => event.location === filters.location)
  }

  // Apply trainee filter
  if (filters.traineeId) {
    filtered = filtered.filter((event) => event.traineeIds?.includes(filters.traineeId!))
  }

  // Apply assessment filter
  if (filters.assessmentId) {
    filtered = filtered.filter((event) => event.assessmentId === filters.assessmentId)
  }

  // Apply training program filter
  if (filters.trainingProgramId) {
    filtered = filtered.filter((event) => event.trainingProgramId === filters.trainingProgramId)
  }

  // Apply search query
  if (searchQuery.trim()) {
    const query = searchQuery.toLowerCase()
    filtered = filtered.filter(
      (event) =>
        event.title.toLowerCase().includes(query) ||
        event.description?.toLowerCase().includes(query) ||
        event.location?.toLowerCase().includes(query) ||
        event.instructor?.toLowerCase().includes(query)
    )
  }

  return filtered
}

/**
 * Create a cache key for filters
 */
export const createFilterCacheKey = (
  filters: CalendarEventFilter,
  searchQuery: string = ''
): string => {
  return JSON.stringify({ filters, searchQuery })
}

/**
 * Check if two filter objects are equal
 */
export const areFiltersEqual = (
  filters1: CalendarEventFilter,
  filters2: CalendarEventFilter,
  searchQuery1: string = '',
  searchQuery2: string = ''
): boolean => {
  return (
    createFilterCacheKey(filters1, searchQuery1) === createFilterCacheKey(filters2, searchQuery2)
  )
}

/**
 * Sort events by date
 */
export const sortEventsByDate = (
  events: CalendarEvent[],
  ascending: boolean = true
): CalendarEvent[] => {
  return [...events].sort((a, b) => {
    const dateA = toDate(a.start).getTime()
    const dateB = toDate(b.start).getTime()
    return ascending ? dateA - dateB : dateB - dateA
  })
}

/**
 * Sort events by priority
 */
export const sortEventsByPriority = (events: CalendarEvent[]): CalendarEvent[] => {
  const priorityOrder: Record<EventPriority, number> = {
    urgent: 0,
    high: 1,
    medium: 2,
    low: 3,
  }

  return [...events].sort((a, b) => {
    const priorityA = priorityOrder[a.priority || 'medium']
    const priorityB = priorityOrder[b.priority || 'medium']
    return priorityA - priorityB
  })
}

/**
 * Group events by type
 */
export const groupEventsByType = (events: CalendarEvent[]): Record<EventType, CalendarEvent[]> => {
  return events.reduce(
    (acc, event) => {
      const type = event.type || 'other'
      if (!acc[type]) {
        acc[type] = []
      }
      acc[type].push(event)
      return acc
    },
    {} as Record<EventType, CalendarEvent[]>
  )
}

/**
 * Group events by status
 */
export const groupEventsByStatus = (
  events: CalendarEvent[]
): Record<EventStatus, CalendarEvent[]> => {
  return events.reduce(
    (acc, event) => {
      const status = event.status || 'scheduled'
      if (!acc[status]) {
        acc[status] = []
      }
      acc[status].push(event)
      return acc
    },
    {} as Record<EventStatus, CalendarEvent[]>
  )
}

/**
 * Group events by date
 */
export const groupEventsByDate = (events: CalendarEvent[]): Record<string, CalendarEvent[]> => {
  return events.reduce(
    (acc, event) => {
      const date = toDate(event.start).toDateString()
      if (!acc[date]) {
        acc[date] = []
      }
      acc[date].push(event)
      return acc
    },
    {} as Record<string, CalendarEvent[]>
  )
}

/**
 * Get events for a specific date range
 */
export const getEventsInRange = (
  events: CalendarEvent[],
  startDate: Date,
  endDate: Date
): CalendarEvent[] => {
  return events.filter((event) => {
    const eventStart = toDate(event.start)
    const eventEnd = event.end ? toDate(event.end) : eventStart
    return eventStart <= endDate && eventEnd >= startDate
  })
}

/**
 * Get upcoming events
 */
export const getUpcomingEvents = (events: CalendarEvent[], days: number = 7): CalendarEvent[] => {
  const now = new Date()
  const futureDate = new Date(now.getTime() + days * 24 * 60 * 60 * 1000)

  return events
    .filter((event) => toDate(event.start) >= now && toDate(event.start) <= futureDate)
    .sort((a, b) => toDate(a.start).getTime() - toDate(b.start).getTime())
}

/**
 * Get past events
 */
export const getPastEvents = (events: CalendarEvent[]): CalendarEvent[] => {
  const now = new Date()

  return events
    .filter((event) => toDate(event.start) < now)
    .sort((a, b) => toDate(b.start).getTime() - toDate(a.start).getTime())
}

/**
 * Get events happening today
 */
export const getTodayEvents = (events: CalendarEvent[]): CalendarEvent[] => {
  const today = new Date()
  const todayStart = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0)
  const todayEnd = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59)

  return events.filter((event) => {
    const eventStart = toDate(event.start)
    return eventStart >= todayStart && eventStart <= todayEnd
  })
}

/**
 * Check if an event conflicts with another event
 */
export const hasTimeConflict = (event1: CalendarEvent, event2: CalendarEvent): boolean => {
  const start1 = toDate(event1.start)
  const end1 = event1.end ? toDate(event1.end) : new Date(start1.getTime() + 60 * 60 * 1000)
  const start2 = toDate(event2.start)
  const end2 = event2.end ? toDate(event2.end) : new Date(start2.getTime() + 60 * 60 * 1000)

  return start1 < end2 && end1 > start2
}

/**
 * Find conflicting events for a given event
 */
export const findConflictingEvents = (
  targetEvent: CalendarEvent,
  events: CalendarEvent[]
): CalendarEvent[] => {
  return events.filter(
    (event) => event.id !== targetEvent.id && hasTimeConflict(targetEvent, event)
  )
}

/**
 * Debounce function for search queries
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null

  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }

    timeout = setTimeout(() => {
      func(...args)
    }, wait)
  }
}

/**
 * Throttle function for performance optimization
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  limit: number
): ((...args: Parameters<T>) => void) => {
  let inThrottle: boolean = false

  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args)
      inThrottle = true
      setTimeout(() => {
        inThrottle = false
      }, limit)
    }
  }
}

/**
 * Memoize function for expensive computations
 */
export const memoize = <T extends (...args: any[]) => any>(
  func: T
): ((...args: Parameters<T>) => ReturnType<T>) => {
  const cache = new Map<string, ReturnType<T>>()

  return (...args: Parameters<T>): ReturnType<T> => {
    const key = JSON.stringify(args)

    if (cache.has(key)) {
      return cache.get(key)!
    }

    const result = func(...args)
    cache.set(key, result)
    return result
  }
}

/**
 * Performance monitoring utilities
 */
export const performance = {
  /**
   * Measure execution time of a function
   */
  measure: <T>(fn: () => T, label?: string): T => {
    const start = window.performance.now()
    const result = fn()
    const end = window.performance.now()
    console.log(`${label || 'Function'} took ${end - start} milliseconds`)
    return result
  },

  /**
   * Create a performance marker
   */
  mark: (name: string): void => {
    console.time(name)
  },

  /**
   * End a performance marker
   */
  markEnd: (name: string): void => {
    console.timeEnd(name)
  },
}

export default {
  toDate,
  normalizeEvent,
  normalizeEvents,
  eventsToRecord,
  recordToEvents,
  validateEvent,
  isValidEventType,
  isValidEventStatus,
  isValidEventPriority,
  sanitizeEvent,
  applyFilters,
  createFilterCacheKey,
  areFiltersEqual,
  sortEventsByDate,
  sortEventsByPriority,
  groupEventsByType,
  groupEventsByStatus,
  groupEventsByDate,
  getEventsInRange,
  getUpcomingEvents,
  getPastEvents,
  getTodayEvents,
  hasTimeConflict,
  findConflictingEvents,
  debounce,
  throttle,
  memoize,
  performance,
}
