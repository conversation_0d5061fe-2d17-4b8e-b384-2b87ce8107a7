import { useEffect, useCallback } from 'react'
import type { CalendarEvent } from '../types'

interface KeyboardShortcutsOptions {
  onCreateEvent?: () => void
  onEditEvent?: (event: CalendarEvent) => void
  onDeleteEvent?: (event: CalendarEvent) => void
  onDuplicateEvent?: (event: CalendarEvent) => void
  onToggleWeekends?: () => void
  onGoToToday?: () => void
  onChangeView?: (view: string) => void
  onSearch?: () => void
  selectedEvent?: CalendarEvent | null
  enabled?: boolean
}

interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  shiftKey?: boolean
  altKey?: boolean
  description: string
  action: () => void
}

export function useKeyboardShortcuts({
  onCreateEvent,
  onEditEvent,
  onDeleteEvent,
  onDuplicateEvent,
  onToggleWeekends,
  onGoToToday,
  onChangeView,
  onSearch,
  selectedEvent,
  enabled = true,
}: KeyboardShortcutsOptions) {
  const handleKeyDown = useCallback(
    (event: KeyboardEvent) => {
      if (!enabled) return

      // Don't trigger shortcuts when user is typing in input fields
      const target = event.target as HTMLElement
      if (
        target.tagName === 'INPUT' ||
        target.tagName === 'TEXTAREA' ||
        target.contentEditable === 'true'
      ) {
        return
      }

      const { key, ctrlKey, shiftKey, altKey } = event

      // Define keyboard shortcuts
      const shortcuts: KeyboardShortcut[] = [
        // Event creation
        {
          key: 'n',
          ctrlKey: true,
          description: 'Create new event',
          action: () => onCreateEvent?.(),
        },
        // Event editing (requires selected event)
        ...(selectedEvent
          ? [
              {
                key: 'e',
                ctrlKey: true,
                description: 'Edit selected event',
                action: () => onEditEvent?.(selectedEvent),
              },
              {
                key: 'd',
                ctrlKey: true,
                description: 'Duplicate selected event',
                action: () => onDuplicateEvent?.(selectedEvent),
              },
              {
                key: 'Delete',
                description: 'Delete selected event',
                action: () => onDeleteEvent?.(selectedEvent),
              },
            ]
          : []),
        // View controls
        {
          key: 't',
          ctrlKey: true,
          description: 'Go to today',
          action: () => onGoToToday?.(),
        },
        {
          key: 'w',
          ctrlKey: true,
          description: 'Toggle weekends',
          action: () => onToggleWeekends?.(),
        },
        // View switching
        {
          key: '1',
          ctrlKey: true,
          description: 'Month view',
          action: () => onChangeView?.('dayGridMonth'),
        },
        {
          key: '2',
          ctrlKey: true,
          description: 'Week view',
          action: () => onChangeView?.('timeGridWeek'),
        },
        {
          key: '3',
          ctrlKey: true,
          description: 'Day view',
          action: () => onChangeView?.('timeGridDay'),
        },
        {
          key: '4',
          ctrlKey: true,
          description: 'List view',
          action: () => onChangeView?.('listWeek'),
        },
        // Search
        {
          key: 'f',
          ctrlKey: true,
          description: 'Focus search',
          action: () => onSearch?.(),
        },
        // Help
        {
          key: '?',
          shiftKey: true,
          description: 'Show keyboard shortcuts help',
          action: () => {
            // This would typically open a help modal
            console.log('Keyboard shortcuts help')
          },
        },
      ]

      // Find matching shortcut
      const matchingShortcut = shortcuts.find(
        (shortcut) =>
          shortcut.key === key &&
          !!shortcut.ctrlKey === ctrlKey &&
          !!shortcut.shiftKey === shiftKey &&
          !!shortcut.altKey === altKey
      )

      if (matchingShortcut) {
        event.preventDefault()
        matchingShortcut.action()
      }
    },
    [
      enabled,
      onCreateEvent,
      onEditEvent,
      onDeleteEvent,
      onDuplicateEvent,
      onToggleWeekends,
      onGoToToday,
      onChangeView,
      onSearch,
      selectedEvent,
    ]
  )

  useEffect(() => {
    if (!enabled) return

    document.addEventListener('keydown', handleKeyDown)
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown, enabled])

  // Return available shortcuts for help display
  const getAvailableShortcuts = (): KeyboardShortcut[] => {
    const shortcuts: KeyboardShortcut[] = [
      {
        key: 'n',
        ctrlKey: true,
        description: 'Create new event',
        action: () => onCreateEvent?.(),
      },
      {
        key: 't',
        ctrlKey: true,
        description: 'Go to today',
        action: () => onGoToToday?.(),
      },
      {
        key: 'w',
        ctrlKey: true,
        description: 'Toggle weekends',
        action: () => onToggleWeekends?.(),
      },
      {
        key: '1',
        ctrlKey: true,
        description: 'Month view',
        action: () => onChangeView?.('dayGridMonth'),
      },
      {
        key: '2',
        ctrlKey: true,
        description: 'Week view',
        action: () => onChangeView?.('timeGridWeek'),
      },
      {
        key: '3',
        ctrlKey: true,
        description: 'Day view',
        action: () => onChangeView?.('timeGridDay'),
      },
      {
        key: '4',
        ctrlKey: true,
        description: 'List view',
        action: () => onChangeView?.('listWeek'),
      },
      {
        key: 'f',
        ctrlKey: true,
        description: 'Focus search',
        action: () => onSearch?.(),
      },
      {
        key: '?',
        shiftKey: true,
        description: 'Show keyboard shortcuts help',
        action: () => console.log('Keyboard shortcuts help'),
      },
    ]

    // Add event-specific shortcuts if there's a selected event
    if (selectedEvent) {
      shortcuts.push(
        {
          key: 'e',
          ctrlKey: true,
          description: 'Edit selected event',
          action: () => onEditEvent?.(selectedEvent),
        },
        {
          key: 'd',
          ctrlKey: true,
          description: 'Duplicate selected event',
          action: () => onDuplicateEvent?.(selectedEvent),
        },
        {
          key: 'Delete',
          description: 'Delete selected event',
          action: () => onDeleteEvent?.(selectedEvent),
        }
      )
    }

    return shortcuts
  }

  return {
    getAvailableShortcuts,
  }
}

// Helper function to format shortcut keys for display
export function formatShortcutKey(shortcut: KeyboardShortcut): string {
  const parts: string[] = []

  if (shortcut.ctrlKey) parts.push('Ctrl')
  if (shortcut.shiftKey) parts.push('Shift')
  if (shortcut.altKey) parts.push('Alt')

  let key = shortcut.key
  if (key === 'Delete') key = 'Del'
  if (key === '?') key = 'Shift+?'

  parts.push(key)

  return parts.join('+')
}

export default useKeyboardShortcuts
