import { useState, useEffect, useCallback, useMemo } from 'react'
import type {
  Resource,
  ResourceBooking,
  ResourceConflict,
  ResourceAvailability,
  ResourceSchedulingRequest,
  ResourceSchedulingResponse,
  UtilizationMetrics,
  ResourceSearchFilter,
  ResourceAnalytics,
  MaintenanceSchedule,
  ResourceSchedulingState,
  ResourceSchedulingAction,
} from '../types/resources'
import type { EntityId } from '../../../shared/types/common'
import { ResourceSchedulingService } from '../services/resourceSchedulingService'

/**
 * Main resource scheduling hook
 * Provides comprehensive resource management functionality
 */
export const useResourceScheduling = () => {
  const [state, setState] = useState<ResourceSchedulingState>({
    resources: [],
    selectedResource: null,
    bookings: [],
    conflicts: [],
    loading: false,
    error: null,
    filters: {},
    analytics: null,
    dateRange: {
      start: new Date(),
      end: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
    },
  })

  // Action dispatcher
  const dispatch = useCallback((action: ResourceSchedulingAction) => {
    setState((prevState) => {
      switch (action.type) {
        case 'SET_LOADING':
          return { ...prevState, loading: action.payload }

        case 'SET_ERROR':
          return { ...prevState, error: action.payload, loading: false }

        case 'SET_RESOURCES':
          return { ...prevState, resources: action.payload, loading: false }

        case 'ADD_RESOURCE':
          return {
            ...prevState,
            resources: [...prevState.resources, action.payload],
            loading: false,
          }

        case 'UPDATE_RESOURCE':
          return {
            ...prevState,
            resources: prevState.resources.map((r) =>
              r.id === action.payload.id ? action.payload : r
            ),
            selectedResource:
              prevState.selectedResource?.id === action.payload.id
                ? action.payload
                : prevState.selectedResource,
            loading: false,
          }

        case 'DELETE_RESOURCE':
          return {
            ...prevState,
            resources: prevState.resources.filter((r) => r.id !== action.payload),
            selectedResource:
              prevState.selectedResource?.id === action.payload ? null : prevState.selectedResource,
            loading: false,
          }

        case 'SET_SELECTED_RESOURCE':
          return { ...prevState, selectedResource: action.payload }

        case 'SET_BOOKINGS':
          return { ...prevState, bookings: action.payload }

        case 'ADD_BOOKING':
          return { ...prevState, bookings: [...prevState.bookings, action.payload] }

        case 'UPDATE_BOOKING':
          return {
            ...prevState,
            bookings: prevState.bookings.map((b) =>
              b.id === action.payload.id ? action.payload : b
            ),
          }

        case 'DELETE_BOOKING':
          return {
            ...prevState,
            bookings: prevState.bookings.filter((b) => b.id !== action.payload),
          }

        case 'SET_CONFLICTS':
          return { ...prevState, conflicts: action.payload }

        case 'ADD_CONFLICT':
          return { ...prevState, conflicts: [...prevState.conflicts, action.payload] }

        case 'RESOLVE_CONFLICT':
          return {
            ...prevState,
            conflicts: prevState.conflicts.map((c) =>
              c.id === action.payload.conflictId
                ? { ...c, resolved: true, resolvedAt: new Date().toISOString() }
                : c
            ),
          }

        case 'SET_FILTERS':
          return { ...prevState, filters: action.payload }

        case 'SET_DATE_RANGE':
          return { ...prevState, dateRange: action.payload }

        case 'SET_ANALYTICS':
          return { ...prevState, analytics: action.payload }

        default:
          return prevState
      }
    })
  }, [])

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      dispatch({ type: 'SET_LOADING', payload: true })

      try {
        // Initialize demo data if needed
        await ResourceSchedulingService.initializeDemoData()

        // Load resources
        const resources = await ResourceSchedulingService.getResources(state.filters)
        dispatch({ type: 'SET_RESOURCES', payload: resources })

        // Load analytics
        const analytics = await ResourceSchedulingService.getResourceAnalytics(state.dateRange)
        dispatch({ type: 'SET_ANALYTICS', payload: analytics })
      } catch (error) {
        dispatch({
          type: 'SET_ERROR',
          payload: error instanceof Error ? error.message : 'Failed to load resources',
        })
      }
    }

    loadInitialData()
  }, []) // Only run once on mount

  return {
    ...state,
    dispatch,
  }
}

/**
 * Hook for resource management operations
 */
export const useResources = (filter?: ResourceSearchFilter) => {
  const [resources, setResources] = useState<Resource[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadResources = useCallback(
    async (customFilter?: ResourceSearchFilter) => {
      setLoading(true)
      setError(null)

      try {
        const result = await ResourceSchedulingService.getResources(customFilter || filter)
        setResources(result)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load resources')
      } finally {
        setLoading(false)
      }
    },
    [filter]
  )

  const createResource = useCallback(
    async (resourceData: Omit<Resource, 'id' | 'createdAt' | 'updatedAt'>) => {
      setLoading(true)
      setError(null)

      try {
        const newResource = await ResourceSchedulingService.createResource(resourceData)
        setResources((prev) => [...prev, newResource])
        return newResource
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to create resource')
        throw err
      } finally {
        setLoading(false)
      }
    },
    []
  )

  const updateResource = useCallback(async (id: EntityId, updates: Partial<Resource>) => {
    setLoading(true)
    setError(null)

    try {
      const updatedResource = await ResourceSchedulingService.updateResource(id, updates)
      setResources((prev) => prev.map((r) => (r.id === id ? updatedResource : r)))
      return updatedResource
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update resource')
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteResource = useCallback(async (id: EntityId) => {
    setLoading(true)
    setError(null)

    try {
      await ResourceSchedulingService.deleteResource(id)
      setResources((prev) => prev.filter((r) => r.id !== id))
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete resource')
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    loadResources()
  }, [loadResources])

  return {
    resources,
    loading,
    error,
    loadResources,
    createResource,
    updateResource,
    deleteResource,
  }
}

/**
 * Hook for resource availability checking
 */
export const useResourceAvailability = (resourceId: EntityId) => {
  const [availability, setAvailability] = useState<ResourceAvailability[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const checkAvailability = useCallback(
    async (startTime: Date, endTime: Date) => {
      setLoading(true)
      setError(null)

      try {
        const isAvailable = await ResourceSchedulingService.checkResourceAvailability(
          resourceId,
          startTime,
          endTime
        )
        return isAvailable
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to check availability')
        return false
      } finally {
        setLoading(false)
      }
    },
    [resourceId]
  )

  const getAvailability = useCallback(
    async (startDate: Date, endDate: Date) => {
      setLoading(true)
      setError(null)

      try {
        const result = await ResourceSchedulingService.getResourceAvailability(
          resourceId,
          startDate,
          endDate
        )
        setAvailability(result)
        return result
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to get availability')
        return []
      } finally {
        setLoading(false)
      }
    },
    [resourceId]
  )

  return {
    availability,
    loading,
    error,
    checkAvailability,
    getAvailability,
  }
}

/**
 * Hook for resource booking operations
 */
export const useResourceBooking = (resourceId?: EntityId) => {
  const [bookings, setBookings] = useState<ResourceBooking[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadBookings = useCallback(
    async (customResourceId?: EntityId) => {
      setLoading(true)
      setError(null)

      try {
        const targetResourceId = customResourceId || resourceId
        if (!targetResourceId) {
          throw new Error('Resource ID is required')
        }

        const result = await ResourceSchedulingService.getResourceBookings(targetResourceId)
        setBookings(result)
        return result
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load bookings')
        return []
      } finally {
        setLoading(false)
      }
    },
    [resourceId]
  )

  const bookResource = useCallback(async (request: ResourceSchedulingRequest) => {
    setLoading(true)
    setError(null)

    try {
      const result = await ResourceSchedulingService.bookResource(request)

      if (result.success && result.booking) {
        setBookings((prev) => [...prev, result.booking!])
      }

      return result
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to book resource')
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  const updateBooking = useCallback(
    async (bookingId: EntityId, updates: Partial<ResourceBooking>) => {
      setLoading(true)
      setError(null)

      try {
        const updatedBooking = await ResourceSchedulingService.updateBooking(bookingId, updates)
        setBookings((prev) => prev.map((b) => (b.id === bookingId ? updatedBooking : b)))
        return updatedBooking
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update booking')
        throw err
      } finally {
        setLoading(false)
      }
    },
    []
  )

  const cancelBooking = useCallback(async (bookingId: EntityId, reason?: string) => {
    setLoading(true)
    setError(null)

    try {
      const cancelledBooking = await ResourceSchedulingService.cancelBooking(bookingId, reason)
      setBookings((prev) => prev.map((b) => (b.id === bookingId ? cancelledBooking : b)))
      return cancelledBooking
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to cancel booking')
      throw err
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    if (resourceId) {
      loadBookings()
    }
  }, [resourceId, loadBookings])

  return {
    bookings,
    loading,
    error,
    loadBookings,
    bookResource,
    updateBooking,
    cancelBooking,
  }
}

/**
 * Hook for resource conflict detection and resolution
 */
export const useResourceConflicts = (resourceId?: EntityId) => {
  const [conflicts, setConflicts] = useState<ResourceConflict[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const detectConflicts = useCallback(
    async (targetResourceId: EntityId, startTime: Date, endTime: Date) => {
      setLoading(true)
      setError(null)

      try {
        const result = await ResourceSchedulingService.detectConflicts(
          targetResourceId,
          startTime,
          endTime
        )
        return result
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to detect conflicts')
        return []
      } finally {
        setLoading(false)
      }
    },
    []
  )

  const loadConflicts = useCallback(
    async (customResourceId?: EntityId) => {
      setLoading(true)
      setError(null)

      try {
        const targetResourceId = customResourceId || resourceId
        if (!targetResourceId) {
          throw new Error('Resource ID is required')
        }

        const result = await ResourceSchedulingService.getResourceConflicts(targetResourceId)
        setConflicts(result)
        return result
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load conflicts')
        return []
      } finally {
        setLoading(false)
      }
    },
    [resourceId]
  )

  const resolveConflict = useCallback(
    async (conflictId: string, resolution: string, resolvedBy: string) => {
      setLoading(true)
      setError(null)

      try {
        const resolvedConflict = await ResourceSchedulingService.resolveConflict(
          conflictId,
          resolution,
          resolvedBy
        )
        setConflicts((prev) => prev.map((c) => (c.id === conflictId ? resolvedConflict : c)))
        return resolvedConflict
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to resolve conflict')
        throw err
      } finally {
        setLoading(false)
      }
    },
    []
  )

  useEffect(() => {
    if (resourceId) {
      loadConflicts()
    }
  }, [resourceId, loadConflicts])

  return {
    conflicts,
    loading,
    error,
    detectConflicts,
    loadConflicts,
    resolveConflict,
  }
}

/**
 * Hook for resource utilization analytics
 */
export const useResourceUtilization = (resourceId?: EntityId) => {
  const [utilization, setUtilization] = useState<UtilizationMetrics | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getUtilization = useCallback(
    async (targetResourceId: EntityId, dateRange: { start: Date; end: Date }) => {
      setLoading(true)
      setError(null)

      try {
        const result = await ResourceSchedulingService.getResourceUtilization(
          targetResourceId,
          dateRange
        )
        setUtilization(result)
        return result
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to get utilization metrics')
        return null
      } finally {
        setLoading(false)
      }
    },
    []
  )

  return {
    utilization,
    loading,
    error,
    getUtilization,
  }
}

/**
 * Hook for overall resource analytics
 */
export const useResourceAnalytics = (dateRange?: { start: Date; end: Date }) => {
  const [analytics, setAnalytics] = useState<ResourceAnalytics | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getAnalytics = useCallback(
    async (customDateRange?: { start: Date; end: Date }) => {
      setLoading(true)
      setError(null)

      try {
        const targetDateRange = customDateRange ||
          dateRange || {
            start: new Date(),
            end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
          }

        const result = await ResourceSchedulingService.getResourceAnalytics(targetDateRange)
        setAnalytics(result)
        return result
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to get analytics')
        return null
      } finally {
        setLoading(false)
      }
    },
    [dateRange]
  )

  useEffect(() => {
    getAnalytics()
  }, [getAnalytics])

  return {
    analytics,
    loading,
    error,
    getAnalytics,
  }
}

/**
 * Hook for resource maintenance scheduling
 */
export const useResourceMaintenance = (resourceId: EntityId) => {
  const [maintenanceSchedule, setMaintenanceSchedule] = useState<MaintenanceSchedule[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const scheduleMaintenance = useCallback(
    async (maintenance: Omit<MaintenanceSchedule, 'id' | 'createdAt' | 'updatedAt'>) => {
      setLoading(true)
      setError(null)

      try {
        const newMaintenance = await ResourceSchedulingService.scheduleMaintenance(
          resourceId,
          maintenance
        )
        setMaintenanceSchedule((prev) => [...prev, newMaintenance])
        return newMaintenance
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to schedule maintenance')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [resourceId]
  )

  return {
    maintenanceSchedule,
    loading,
    error,
    scheduleMaintenance,
  }
}

/**
 * Combined hook for comprehensive resource management
 * Combines multiple resource hooks for convenience
 */
export const useResourceManagement = (resourceId?: EntityId) => {
  const resources = useResources()
  const availability = useResourceAvailability(resourceId || '')
  const booking = useResourceBooking(resourceId)
  const conflicts = useResourceConflicts(resourceId)
  const utilization = useResourceUtilization(resourceId)
  const analytics = useResourceAnalytics()
  const maintenance = useResourceMaintenance(resourceId || '')

  const isLoading = useMemo(
    () =>
      resources.loading ||
      availability.loading ||
      booking.loading ||
      conflicts.loading ||
      utilization.loading ||
      analytics.loading ||
      maintenance.loading,
    [
      resources.loading,
      availability.loading,
      booking.loading,
      conflicts.loading,
      utilization.loading,
      analytics.loading,
      maintenance.loading,
    ]
  )

  const hasError = useMemo(
    () =>
      !!resources.error ||
      !!availability.error ||
      !!booking.error ||
      !!conflicts.error ||
      !!utilization.error ||
      !!analytics.error ||
      !!maintenance.error,
    [
      resources.error,
      availability.error,
      booking.error,
      conflicts.error,
      utilization.error,
      analytics.error,
      maintenance.error,
    ]
  )

  return {
    resources,
    availability,
    booking,
    conflicts,
    utilization,
    analytics,
    maintenance,
    isLoading,
    hasError,
  }
}
