import { useState, useEffect, useCallback, useMemo } from 'react'
import { useCalendarState, useCalendarFilters } from './useCalendarState'
import { AssessmentIntegrationService } from '../services/assessmentIntegrationService'
import { AssessmentService } from '../../assessments/services/assessmentService'
import { useAsyncOperation } from '../../../hooks/useAsyncOperation'
import type { CalendarEvent, CalendarEventFilter, EventType } from '../types'
import type {
  Assessment,
  AssessmentStatus,
  CreateAssessmentForm,
  SubmitReviewForm,
} from '../../../shared/types/assessment'
import type { EntityId } from '../../../shared/types/common'

/**
 * Hook for fetching and managing assessments
 */
export const useAssessments = () => {
  const [assessments, setAssessments] = useState<Assessment[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { execute } = useAsyncOperation()

  const loadAssessments = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const assessmentsData = await execute(() => AssessmentService.getAssessments())
      setAssessments(assessmentsData)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load assessments')
    } finally {
      setLoading(false)
    }
  }, [execute])

  const createAssessment = useCallback(
    async (assessmentData: CreateAssessmentForm) => {
      try {
        setLoading(true)
        setError(null)
        const newAssessment = await execute(() =>
          AssessmentService.createAssessment(assessmentData)
        )
        setAssessments((prev) => [...prev, newAssessment])
        return newAssessment
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to create assessment')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const updateAssessment = useCallback(
    async (id: EntityId, assessmentData: Partial<Assessment>) => {
      try {
        setLoading(true)
        setError(null)
        const updatedAssessment = await execute(() =>
          AssessmentService.updateAssessment(id, assessmentData)
        )
        setAssessments((prev) => prev.map((a) => (a.id === id ? updatedAssessment : a)))
        return updatedAssessment
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update assessment')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const deleteAssessment = useCallback(
    async (id: EntityId) => {
      try {
        setLoading(true)
        setError(null)
        await execute(() => AssessmentService.deleteAssessment(id))
        setAssessments((prev) => prev.filter((a) => a.id !== id))
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete assessment')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const getAssessmentsByTrainee = useCallback(
    async (traineeId: EntityId) => {
      try {
        setLoading(true)
        setError(null)
        return await execute(() => AssessmentService.getAssessmentsByTrainee(traineeId))
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to get assessments by trainee')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const getAssessmentsByQuarter = useCallback(
    async (quarter: number, year: number) => {
      try {
        setLoading(true)
        setError(null)
        return await execute(() => AssessmentService.getAssessmentsByQuarter(quarter, year))
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to get assessments by quarter')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const getAssessmentsByStatus = useCallback(
    async (status: AssessmentStatus) => {
      try {
        setLoading(true)
        setError(null)
        return await execute(() => AssessmentService.getAssessmentsByStatus(status))
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to get assessments by status')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const submitReview = useCallback(
    async (
      assessmentId: EntityId,
      reviewerId: EntityId,
      reviewerRole: string,
      reviewData: SubmitReviewForm
    ) => {
      try {
        setLoading(true)
        setError(null)
        const review = await execute(() =>
          AssessmentService.submitReview(assessmentId, reviewerId, reviewerRole, reviewData)
        )

        // Update the assessment with the new review
        setAssessments((prev) =>
          prev.map((assessment) => {
            if (assessment.id === assessmentId) {
              return {
                ...assessment,
                reviews: [...(assessment.reviews || []), review],
                updatedAt: new Date().toISOString(),
              }
            }
            return assessment
          })
        )

        return review
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to submit review')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  useEffect(() => {
    loadAssessments()
  }, [loadAssessments])

  return {
    assessments,
    loading,
    error,
    loadAssessments,
    createAssessment,
    updateAssessment,
    deleteAssessment,
    getAssessmentsByTrainee,
    getAssessmentsByQuarter,
    getAssessmentsByStatus,
    submitReview,
    clearError: () => setError(null),
  }
}

/**
 * Hook for converting assessment data to calendar events
 */
export const useAssessmentEvents = () => {
  const { assessments } = useAssessments()
  const [assessmentEvents, setAssessmentEvents] = useState<CalendarEvent[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { execute } = useAsyncOperation()

  const loadAssessmentEvents = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const events = await execute(() => AssessmentIntegrationService.getAssessmentEvents())
      setAssessmentEvents(events)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load assessment events')
    } finally {
      setLoading(false)
    }
  }, [execute])

  const getAssessmentEventsByTrainee = useCallback(
    async (traineeId: EntityId) => {
      try {
        setLoading(true)
        setError(null)
        return await execute(() =>
          AssessmentIntegrationService.getAssessmentEventsByTrainee(traineeId)
        )
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to get assessment events by trainee')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const getAssessmentEventsByQuarter = useCallback(
    async (quarter: number, year: number) => {
      try {
        setLoading(true)
        setError(null)
        return await execute(() =>
          AssessmentIntegrationService.getAssessmentEventsByQuarter(quarter, year)
        )
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to get assessment events by quarter')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const getAssessmentEventsByStatus = useCallback(
    async (status: AssessmentStatus) => {
      try {
        setLoading(true)
        setError(null)
        return await execute(() => AssessmentIntegrationService.getAssessmentEventsByStatus(status))
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to get assessment events by status')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  // Auto-refresh assessment events when assessments change
  useEffect(() => {
    loadAssessmentEvents()
  }, [assessments, loadAssessmentEvents])

  return {
    assessmentEvents,
    loading,
    error,
    loadAssessmentEvents,
    getAssessmentEventsByTrainee,
    getAssessmentEventsByQuarter,
    getAssessmentEventsByStatus,
    clearError: () => setError(null),
  }
}

/**
 * Hook for synchronizing assessment changes with calendar
 */
export const useAssessmentSync = () => {
  const { events, addEvent, updateEvent, deleteEvent, setEvents } = useCalendarState()
  const { assessmentEvents, loadAssessmentEvents } = useAssessmentEvents()
  const [syncing, setSyncing] = useState(false)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)
  const { execute } = useAsyncOperation()

  const syncAssessmentEvents = useCallback(async () => {
    try {
      setSyncing(true)

      // Get current assessment events
      await loadAssessmentEvents()

      // Find existing assessment events in calendar
      const existingAssessmentEvents = events.filter(
        (event) =>
          event.assessmentId &&
          event.metadata?.type &&
          ['deadline', 'session', 'review', 'reminder'].includes(event.metadata.type as string)
      )

      // Sync events
      const syncResult = await execute(() => AssessmentIntegrationService.syncAssessmentEvents())

      // Get fresh assessment events after loading
      const currentAssessmentEvents = assessmentEvents

      // Add new events
      const newEvents = currentAssessmentEvents.filter(
        (event) => !existingAssessmentEvents.some((existing) => existing.id === event.id)
      )

      if (newEvents.length > 0) {
        newEvents.forEach((event) => addEvent(event))
      }

      // Update existing events
      existingAssessmentEvents.forEach((existingEvent) => {
        const updatedEvent = currentAssessmentEvents.find((event) => event.id === existingEvent.id)
        if (updatedEvent) {
          updateEvent(existingEvent.id, updatedEvent)
        }
      })

      // Remove events that no longer exist
      const removedEvents = existingAssessmentEvents.filter(
        (existingEvent) => !currentAssessmentEvents.some((event) => event.id === existingEvent.id)
      )

      removedEvents.forEach((event) => {
        deleteEvent(event.id)
      })

      setLastSyncTime(new Date())
      return syncResult
    } catch (err) {
      console.error('Failed to sync assessment events:', err)
      throw err
    } finally {
      setSyncing(false)
    }
  }, [events, addEvent, updateEvent, deleteEvent, assessmentEvents, loadAssessmentEvents, execute])

  // Auto-sync when assessment events change
  useEffect(() => {
    if (assessmentEvents.length > 0) {
      syncAssessmentEvents()
    }
  }, [assessmentEvents.length]) // Only re-run when count changes, not individual events

  return {
    syncAssessmentEvents,
    syncing,
    lastSyncTime,
  }
}

/**
 * Hook for assessment-specific filtering
 */
export const useAssessmentFilters = () => {
  const { filters } = useCalendarState()
  const { updateFilters, resetFilters } = useCalendarFilters()
  const [assessmentFilter, setAssessmentFilter] = useState<{
    traineeId?: EntityId
    status?: AssessmentStatus
    quarter?: number
    year?: number
    reviewerId?: EntityId
    eventType?: 'deadline' | 'session' | 'review' | 'reminder'
  }>({})

  const updateAssessmentFilters = useCallback(
    (newFilters: Partial<typeof assessmentFilter>) => {
      const updatedFilter = { ...assessmentFilter, ...newFilters }
      setAssessmentFilter(updatedFilter)

      // Convert to calendar filter format
      const calendarFilter: Partial<CalendarEventFilter> = {}

      if (newFilters.traineeId) {
        calendarFilter.traineeId = newFilters.traineeId
      }

      if (newFilters.eventType) {
        const eventTypeMap: Record<string, EventType> = {
          deadline: 'deadline',
          session: 'assessment',
          review: 'review',
          reminder: 'meeting',
        }
        calendarFilter.type = [eventTypeMap[newFilters.eventType]]
      }

      updateFilters(calendarFilter)
    },
    [assessmentFilter, updateFilters]
  )

  const resetAssessmentFilters = useCallback(() => {
    setAssessmentFilter({})
    resetFilters()
  }, [resetFilters])

  const filteredAssessmentEvents = useMemo(() => {
    // This would be implemented to filter events based on assessment-specific criteria
    // For now, return empty array
    return []
  }, [])

  return {
    assessmentFilter,
    updateAssessmentFilters,
    resetAssessmentFilters,
    filteredAssessmentEvents,
  }
}

/**
 * Hook for assessment statistics and analytics
 */
export const useAssessmentStats = () => {
  const [stats, setStats] = useState<{
    total: number
    byStatus: Record<AssessmentStatus, number>
    upcomingDeadlines: CalendarEvent[]
    overdueAssessments: CalendarEvent[]
    thisWeek: CalendarEvent[]
    thisMonth: CalendarEvent[]
  } | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { execute } = useAsyncOperation()

  const loadStats = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const assessmentStats = await execute(() => AssessmentIntegrationService.getAssessmentStats())
      setStats(assessmentStats)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load assessment stats')
    } finally {
      setLoading(false)
    }
  }, [execute])

  useEffect(() => {
    loadStats()
  }, [loadStats])

  return {
    stats,
    loading,
    error,
    loadStats,
    clearError: () => setError(null),
  }
}

/**
 * Main hook for assessment calendar integration
 */
export const useAssessmentCalendar = () => {
  const assessments = useAssessments()
  const assessmentEvents = useAssessmentEvents()
  const assessmentSync = useAssessmentSync()
  const assessmentFilters = useAssessmentFilters()
  const assessmentStats = useAssessmentStats()

  return {
    // Assessment data
    assessments: assessments.assessments,
    assessmentsLoading: assessments.loading,
    assessmentsError: assessments.error,

    // Assessment events
    assessmentEvents: assessmentEvents.assessmentEvents,
    eventsLoading: assessmentEvents.loading,
    eventsError: assessmentEvents.error,

    // Sync
    syncAssessmentEvents: assessmentSync.syncAssessmentEvents,
    syncing: assessmentSync.syncing,
    lastSyncTime: assessmentSync.lastSyncTime,

    // Filters
    assessmentFilter: assessmentFilters.assessmentFilter,
    updateAssessmentFilters: assessmentFilters.updateAssessmentFilters,
    resetAssessmentFilters: assessmentFilters.resetAssessmentFilters,

    // Stats
    stats: assessmentStats.stats,
    statsLoading: assessmentStats.loading,
    statsError: assessmentStats.error,

    // Actions
    loadAssessments: assessments.loadAssessments,
    createAssessment: assessments.createAssessment,
    updateAssessment: assessments.updateAssessment,
    deleteAssessment: assessments.deleteAssessment,
    getAssessmentsByTrainee: assessments.getAssessmentsByTrainee,
    getAssessmentsByQuarter: assessments.getAssessmentsByQuarter,
    getAssessmentsByStatus: assessments.getAssessmentsByStatus,
    submitReview: assessments.submitReview,

    loadAssessmentEvents: assessmentEvents.loadAssessmentEvents,
    getAssessmentEventsByTrainee: assessmentEvents.getAssessmentEventsByTrainee,
    getAssessmentEventsByQuarter: assessmentEvents.getAssessmentEventsByQuarter,
    getAssessmentEventsByStatus: assessmentEvents.getAssessmentEventsByStatus,

    loadStats: assessmentStats.loadStats,

    // Clear errors
    clearAssessmentsError: assessments.clearError,
    clearEventsError: assessmentEvents.clearError,
    clearStatsError: assessmentStats.clearError,
  }
}

export default useAssessmentCalendar
