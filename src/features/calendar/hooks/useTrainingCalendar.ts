import { useCallback, useEffect, useState, useMemo } from 'react'
import { useCalendarState } from './useCalendarState'
import { useCalendarActions, useCalendarStore } from '../stores/calendarStore'
import { TrainingIntegrationService } from '../services/trainingIntegrationService'
import { TrainingProgramService } from '../../../features/training-programs/services/trainingProgramService'
import { useAsyncOperation } from '../../../hooks/useAsyncOperation'
import type { CalendarEvent, CalendarEventFilter, EventType } from '../types'
import type { TrainingProgram, TrainingProgramFilter } from '../../../shared/types/trainingProgram'

/**
 * Hook for fetching and managing training programs
 */
export const useTrainingPrograms = () => {
  const [programs, setPrograms] = useState<TrainingProgram[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { execute } = useAsyncOperation()

  const loadPrograms = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const result = await execute(() => TrainingProgramService.getTrainingPrograms())
      setPrograms(result)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load training programs')
    } finally {
      setLoading(false)
    }
  }, [execute])

  const loadProgramById = useCallback(
    async (id: string) => {
      setLoading(true)
      setError(null)
      try {
        const result = await execute(() => TrainingProgramService.getTrainingProgramById(id))
        return result
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load training program')
        return null
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const createProgram = useCallback(
    async (programData: Omit<TrainingProgram, 'id' | 'createdAt' | 'updatedAt'>) => {
      setLoading(true)
      setError(null)
      try {
        const result = await execute(() =>
          TrainingProgramService.createTrainingProgram(programData)
        )
        setPrograms((prev) => [...prev, result])
        return result
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to create training program')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const updateProgram = useCallback(
    async (id: string, programData: Partial<TrainingProgram>) => {
      setLoading(true)
      setError(null)
      try {
        const result = await execute(() =>
          TrainingProgramService.updateTrainingProgram(id, programData)
        )
        setPrograms((prev) => prev.map((p) => (p.id === id ? result : p)))
        return result
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to update training program')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const deleteProgram = useCallback(
    async (id: string) => {
      setLoading(true)
      setError(null)
      try {
        await execute(() => TrainingProgramService.deleteTrainingProgram(id))
        setPrograms((prev) => prev.filter((p) => p.id !== id))
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to delete training program')
        throw err
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  useEffect(() => {
    loadPrograms()
  }, [loadPrograms])

  return {
    programs,
    loading,
    error,
    loadPrograms,
    loadProgramById,
    createProgram,
    updateProgram,
    deleteProgram,
  }
}

/**
 * Hook for converting training data to calendar events
 */
export const useTrainingEvents = () => {
  const [trainingEvents, setTrainingEvents] = useState<CalendarEvent[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { execute } = useAsyncOperation()

  const loadTrainingEvents = useCallback(
    async (programId?: string) => {
      setLoading(true)
      setError(null)
      try {
        const events = programId
          ? await execute(() => TrainingIntegrationService.getTrainingProgramEventsById(programId))
          : await execute(() => TrainingIntegrationService.getTrainingProgramEvents())
        setTrainingEvents(events)
        return events
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load training events')
        return []
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const loadEventsByDateRange = useCallback(
    async (startDate: Date, endDate: Date) => {
      setLoading(true)
      setError(null)
      try {
        const events = await execute(() =>
          TrainingIntegrationService.getTrainingProgramsByDateRange(startDate, endDate)
        )
        setTrainingEvents(events)
        return events
      } catch (err) {
        setError(
          err instanceof Error ? err.message : 'Failed to load training events by date range'
        )
        return []
      } finally {
        setLoading(false)
      }
    },
    [execute]
  )

  const refreshTrainingEvents = useCallback(async () => {
    return loadTrainingEvents()
  }, [loadTrainingEvents])

  const createEventFromSession = useCallback(
    (session: any, programId: string, programName: string) => {
      return TrainingIntegrationService.createCalendarEventFromSession(
        session,
        programId,
        programName
      )
    },
    []
  )

  const createEventFromMilestone = useCallback(
    (milestone: any, programId: string, programName: string) => {
      return TrainingIntegrationService.createCalendarEventFromMilestone(
        milestone,
        programId,
        programName
      )
    },
    []
  )

  return {
    trainingEvents,
    loading,
    error,
    loadTrainingEvents,
    loadEventsByDateRange,
    refreshTrainingEvents,
    createEventFromSession,
    createEventFromMilestone,
  }
}

/**
 * Hook for synchronizing training changes with calendar
 */
export const useTrainingSync = () => {
  const { addEvent, updateEvent, deleteEvent, bulkDeleteEvents, setEvents } = useCalendarActions()
  const { execute } = useAsyncOperation()
  const [syncing, setSyncing] = useState(false)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)
  const [error, setError] = useState<string | null>(null)

  const syncTrainingPrograms = useCallback(async () => {
    setSyncing(true)
    setError(null)
    try {
      const events = await execute(() => TrainingIntegrationService.getTrainingProgramEvents())

      // Get current events from store to identify training events
      const currentEvents = useCalendarStore.getState().events
      const existingTrainingEventIds = Object.values(currentEvents)
        .filter((e: CalendarEvent) => e.trainingProgramId)
        .map((e: CalendarEvent) => e.id)

      // Remove existing training events
      if (existingTrainingEventIds.length > 0) {
        bulkDeleteEvents(existingTrainingEventIds)
      }

      // Add fresh training events
      setEvents(events)
      setLastSyncTime(new Date())

      return events
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to sync training programs')
      throw err
    } finally {
      setSyncing(false)
    }
  }, [execute, bulkDeleteEvents, setEvents])

  const syncTrainingProgram = useCallback(
    async (programId: string) => {
      setSyncing(true)
      setError(null)
      try {
        const events = await execute(() =>
          TrainingIntegrationService.syncTrainingProgramChanges(programId)
        )

        // Get current events from store to identify program events
        const currentEvents = useCalendarStore.getState().events
        const existingProgramEventIds = Object.values(currentEvents)
          .filter((e: CalendarEvent) => e.trainingProgramId === programId)
          .map((e: CalendarEvent) => e.id)

        // Remove existing events for this program
        if (existingProgramEventIds.length > 0) {
          bulkDeleteEvents(existingProgramEventIds)
        }

        // Add updated events
        events.forEach((event: CalendarEvent) => addEvent(event))
        setLastSyncTime(new Date())

        return events
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to sync training program')
        throw err
      } finally {
        setSyncing(false)
      }
    },
    [execute, bulkDeleteEvents, addEvent]
  )

  const autoSync = useCallback(async () => {
    try {
      await syncTrainingPrograms()
    } catch (error) {
      console.error('Auto sync failed:', error)
    }
  }, [syncTrainingPrograms])

  return {
    syncTrainingPrograms,
    syncTrainingProgram,
    autoSync,
    syncing,
    lastSyncTime,
    error,
  }
}

/**
 * Hook for training-specific filtering
 */
export const useTrainingFilters = () => {
  const [trainingFilters, setTrainingFilters] = useState<TrainingProgramFilter>({})
  const [appliedFilters, setAppliedFilters] = useState<CalendarEventFilter>({})

  const updateTrainingFilters = useCallback((newFilters: Partial<TrainingProgramFilter>) => {
    setTrainingFilters((prev) => ({ ...prev, ...newFilters }))
  }, [])

  const applyTrainingFiltersToCalendar = useCallback(() => {
    const calendarFilters: CalendarEventFilter = {}

    if (trainingFilters.status && trainingFilters.status.length > 0) {
      calendarFilters.type = ['training_session', 'assessment', 'deadline', 'review']
    }

    if (trainingFilters.instructor) {
      calendarFilters.instructor = trainingFilters.instructor
    }

    if (trainingFilters.dateRange) {
      calendarFilters.dateRange = trainingFilters.dateRange
    }

    setAppliedFilters(calendarFilters)
    return calendarFilters
  }, [trainingFilters])

  const clearTrainingFilters = useCallback(() => {
    setTrainingFilters({})
    setAppliedFilters({})
  }, [])

  const getTrainingEventTypes = useCallback((): EventType[] => {
    return ['training_session', 'assessment', 'deadline', 'review']
  }, [])

  const filterTrainingEvents = useCallback(
    (events: CalendarEvent[]) => {
      return events.filter((event) => {
        if (!event.trainingProgramId) return false

        // Filter by status
        if (trainingFilters.status && trainingFilters.status.length > 0) {
          // This would require additional metadata or API support
          // For now, we'll include all training events
        }

        // Filter by instructor
        if (trainingFilters.instructor && event.instructor !== trainingFilters.instructor) {
          return false
        }

        // Filter by date range
        if (trainingFilters.dateRange) {
          const eventDate = new Date(event.start as string)
          if (
            eventDate < trainingFilters.dateRange.start ||
            eventDate > trainingFilters.dateRange.end
          ) {
            return false
          }
        }

        return true
      })
    },
    [trainingFilters]
  )

  return {
    trainingFilters,
    appliedFilters,
    updateTrainingFilters,
    applyTrainingFiltersToCalendar,
    clearTrainingFilters,
    getTrainingEventTypes,
    filterTrainingEvents,
  }
}

/**
 * Hook for training statistics and analytics
 */
export const useTrainingStatistics = () => {
  const [statistics, setStatistics] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { execute } = useAsyncOperation()

  const loadStatistics = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const stats = await execute(() => TrainingIntegrationService.getTrainingStatistics())
      setStatistics(stats)
      return stats
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load training statistics')
      return null
    } finally {
      setLoading(false)
    }
  }, [execute])

  useEffect(() => {
    loadStatistics()
  }, [loadStatistics])

  return {
    statistics,
    loading,
    error,
    loadStatistics,
    refreshStatistics: loadStatistics,
  }
}

/**
 * Combined hook for comprehensive training calendar functionality
 */
export const useTrainingCalendar = () => {
  const calendarState = useCalendarState()
  const trainingPrograms = useTrainingPrograms()
  const trainingEvents = useTrainingEvents()
  const trainingSync = useTrainingSync()
  const trainingFilters = useTrainingFilters()
  const trainingStats = useTrainingStatistics()

  // Initialize training events on mount
  useEffect(() => {
    trainingEvents.loadTrainingEvents()
  }, [trainingEvents.loadTrainingEvents])

  // Auto-sync when training programs change
  useEffect(() => {
    if (trainingPrograms.programs.length > 0) {
      trainingSync.autoSync()
    }
  }, [trainingPrograms.programs.length, trainingSync.autoSync])

  // Filter events based on training filters
  const filteredTrainingEvents = useMemo(() => {
    return trainingFilters.filterTrainingEvents(trainingEvents.trainingEvents)
  }, [trainingEvents.trainingEvents, trainingFilters.filterTrainingEvents])

  return {
    // Calendar state
    ...calendarState,

    // Training programs
    programs: trainingPrograms.programs,
    programsLoading: trainingPrograms.loading,
    programsError: trainingPrograms.error,
    loadPrograms: trainingPrograms.loadPrograms,
    createProgram: trainingPrograms.createProgram,
    updateProgram: trainingPrograms.updateProgram,
    deleteProgram: trainingPrograms.deleteProgram,

    // Training events
    trainingEvents: trainingEvents.trainingEvents,
    trainingEventsLoading: trainingEvents.loading,
    trainingEventsError: trainingEvents.error,
    loadTrainingEvents: trainingEvents.loadTrainingEvents,
    refreshTrainingEvents: trainingEvents.refreshTrainingEvents,

    // Training sync
    syncTrainingPrograms: trainingSync.syncTrainingPrograms,
    syncTrainingProgram: trainingSync.syncTrainingProgram,
    syncing: trainingSync.syncing,
    lastSyncTime: trainingSync.lastSyncTime,
    syncError: trainingSync.error,

    // Training filters
    trainingFilters: trainingFilters.trainingFilters,
    appliedTrainingFilters: trainingFilters.appliedFilters,
    updateTrainingFilters: trainingFilters.updateTrainingFilters,
    applyTrainingFilters: trainingFilters.applyTrainingFiltersToCalendar,
    clearTrainingFilters: trainingFilters.clearTrainingFilters,

    // Training statistics
    trainingStatistics: trainingStats.statistics,
    statisticsLoading: trainingStats.loading,
    statisticsError: trainingStats.error,
    loadStatistics: trainingStats.loadStatistics,

    // Computed
    filteredTrainingEvents,
    hasTrainingPrograms: trainingPrograms.programs.length > 0,
    hasTrainingEvents: trainingEvents.trainingEvents.length > 0,
  }
}

export default useTrainingCalendar
