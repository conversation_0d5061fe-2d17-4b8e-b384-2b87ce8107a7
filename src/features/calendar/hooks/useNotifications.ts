import { useState, useEffect, useCallback } from 'react'
import { CalendarNotificationService } from '../services'
import { useNotifications } from '../../../hooks/useNotifications'
import type {
  CalendarNotification,
  EventReminder,
  NotificationPreferences,
  NotificationHistoryEntry,
  NotificationStats,
  NotificationFilter,
  CalendarNotificationType,
  ReminderType,
  NotificationStatus,
  NotificationPriority,
  CalendarNotificationUserSettings,
} from '../types'

/**
 * Hook for managing calendar notifications
 */
export const useCalendarNotifications = () => {
  const [notifications, setNotifications] = useState<CalendarNotification[]>([])
  const [reminders, setReminders] = useState<EventReminder[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const { sendNotification } = useNotifications()

  /**
   * Schedule an event reminder
   */
  const scheduleReminder = useCallback(
    async (
      eventId: string,
      reminderTime: Date,
      reminderType: ReminderType,
      metadata?: any
    ): Promise<EventReminder | null> => {
      setLoading(true)
      setError(null)

      try {
        // Get current user ID (this would come from auth context)
        const userId = getCurrentUserId()

        const reminder = await CalendarNotificationService.scheduleEventReminder(
          eventId,
          userId,
          reminderTime,
          reminderType,
          metadata
        )

        setReminders((prev) => [...prev, reminder])
        return reminder
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to schedule reminder'
        setError(errorMessage)
        console.error('Failed to schedule reminder:', err)
        return null
      } finally {
        setLoading(false)
      }
    },
    []
  )

  /**
   * Notify about event changes
   */
  const notifyEventChange = useCallback(
    async (
      eventId: string,
      changeType: 'created' | 'updated' | 'deleted' | 'cancelled',
      affectedUsers?: string[],
      changes?: Record<string, { old: unknown; new: unknown }>
    ): Promise<void> => {
      setLoading(true)
      setError(null)

      try {
        // If no affected users provided, get them from the event
        const users = affectedUsers || (await getAffectedUsers(eventId))

        await CalendarNotificationService.notifyEventChange(eventId, changeType, users, changes)
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to notify event change'
        setError(errorMessage)
        console.error('Failed to notify event change:', err)
      } finally {
        setLoading(false)
      }
    },
    []
  )

  /**
   * Send custom notification
   */
  const sendCustomNotification = useCallback(
    async (
      type: CalendarNotificationType,
      eventId: string,
      userIds: string[],
      message?: string,
      metadata?: any
    ): Promise<CalendarNotification[] | null> => {
      setLoading(true)
      setError(null)

      try {
        const newNotifications = await CalendarNotificationService.sendEventNotification(
          type,
          eventId,
          userIds,
          message,
          metadata
        )

        setNotifications((prev) => [...prev, ...newNotifications])

        // Also send through GT-EGA's notification system
        for (const notification of newNotifications) {
          await sendNotification({
            title: notification.title,
            body: notification.message,
            tag: `calendar_${notification.type}`,
            timestamp: notification.scheduledFor.getTime(),
          })
        }

        return newNotifications
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to send notification'
        setError(errorMessage)
        console.error('Failed to send notification:', err)
        return null
      } finally {
        setLoading(false)
      }
    },
    [sendNotification]
  )

  /**
   * Mark notification as read
   */
  const markAsRead = useCallback(async (notificationId: string): Promise<void> => {
    try {
      setNotifications((prev) =>
        prev.map((notification) =>
          notification.id === notificationId
            ? { ...notification, status: 'read' as NotificationStatus, readAt: new Date() }
            : notification
        )
      )
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to mark notification as read'
      setError(errorMessage)
      console.error('Failed to mark notification as read:', err)
    }
  }, [])

  /**
   * Delete notification
   */
  const deleteNotification = useCallback(async (notificationId: string): Promise<void> => {
    try {
      setNotifications((prev) => prev.filter((notification) => notification.id !== notificationId))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete notification'
      setError(errorMessage)
      console.error('Failed to delete notification:', err)
    }
  }, [])

  /**
   * Cancel reminder
   */
  const cancelReminder = useCallback(async (reminderId: string): Promise<void> => {
    try {
      setReminders((prev) => prev.filter((reminder) => reminder.id !== reminderId))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel reminder'
      setError(errorMessage)
      console.error('Failed to cancel reminder:', err)
    }
  }, [])

  /**
   * Refresh notifications
   */
  const refreshNotifications = useCallback(async (): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const userId = getCurrentUserId()
      const userNotifications = await CalendarNotificationService.getNotificationHistory(userId)
      setNotifications(userNotifications)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to refresh notifications'
      setError(errorMessage)
      console.error('Failed to refresh notifications:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  return {
    notifications,
    reminders,
    loading,
    error,
    scheduleReminder,
    notifyEventChange,
    sendCustomNotification,
    markAsRead,
    deleteNotification,
    cancelReminder,
    refreshNotifications,
  }
}

/**
 * Hook for managing event reminders
 */
export const useEventReminders = () => {
  const [reminders, setReminders] = useState<EventReminder[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * Get reminders for an event
   */
  const getEventReminders = useCallback(
    async (eventId: string): Promise<EventReminder[]> => {
      setLoading(true)
      setError(null)

      try {
        const userId = getCurrentUserId()
        const eventReminders = reminders.filter((r) => r.eventId === eventId && r.userId === userId)
        return eventReminders
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to get event reminders'
        setError(errorMessage)
        console.error('Failed to get event reminders:', err)
        return []
      } finally {
        setLoading(false)
      }
    },
    [reminders]
  )

  /**
   * Schedule multiple reminders for an event
   */
  const scheduleMultipleReminders = useCallback(
    async (
      eventId: string,
      reminderTimes: Array<{ time: Date; type: ReminderType }>
    ): Promise<EventReminder[]> => {
      setLoading(true)
      setError(null)

      try {
        const newReminders: EventReminder[] = []

        for (const { time, type } of reminderTimes) {
          const reminder = await CalendarNotificationService.scheduleEventReminder(
            eventId,
            getCurrentUserId(),
            time,
            type
          )

          if (reminder) {
            newReminders.push(reminder)
          }
        }

        setReminders((prev) => [...prev, ...newReminders])
        return newReminders
      } catch (err) {
        const errorMessage =
          err instanceof Error ? err.message : 'Failed to schedule multiple reminders'
        setError(errorMessage)
        console.error('Failed to schedule multiple reminders:', err)
        return []
      } finally {
        setLoading(false)
      }
    },
    []
  )

  /**
   * Cancel all reminders for an event
   */
  const cancelAllReminders = useCallback(async (eventId: string): Promise<void> => {
    try {
      setReminders((prev) => prev.filter((reminder) => reminder.eventId !== eventId))
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel all reminders'
      setError(errorMessage)
      console.error('Failed to cancel all reminders:', err)
    }
  }, [])

  return {
    reminders,
    loading,
    error,
    getEventReminders,
    scheduleMultipleReminders,
    cancelAllReminders,
  }
}

/**
 * Hook for managing notification preferences
 */
export const useNotificationPreferences = () => {
  const [preferences, setPreferences] = useState<NotificationPreferences | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * Load user preferences
   */
  const loadPreferences = useCallback(async (): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const userId = getCurrentUserId()
      const userPreferences =
        await CalendarNotificationService.getUserNotificationPreferences(userId)
      setPreferences(userPreferences)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load preferences'
      setError(errorMessage)
      console.error('Failed to load preferences:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * Update preferences
   */
  const updatePreferences = useCallback(
    async (updates: Partial<NotificationPreferences>): Promise<boolean> => {
      setLoading(true)
      setError(null)

      try {
        const userId = getCurrentUserId()
        const updatedPreferences =
          await CalendarNotificationService.updateUserNotificationPreferences(userId, updates)

        setPreferences(updatedPreferences)
        return true
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to update preferences'
        setError(errorMessage)
        console.error('Failed to update preferences:', err)
        return false
      } finally {
        setLoading(false)
      }
    },
    []
  )

  /**
   * Toggle notification type
   */
  const toggleNotificationType = useCallback(
    async (category: keyof NotificationPreferences, enabled: boolean): Promise<boolean> => {
      if (!preferences) return false

      const updates = {
        ...preferences,
        [category]: {
          ...(preferences[category] as any),
          enabled,
        },
      }

      return updatePreferences(updates)
    },
    [preferences, updatePreferences]
  )

  // Load preferences on mount
  useEffect(() => {
    loadPreferences()
  }, [loadPreferences])

  return {
    preferences,
    loading,
    error,
    loadPreferences,
    updatePreferences,
    toggleNotificationType,
  }
}

/**
 * Hook for managing notification history
 */
export const useNotificationHistory = () => {
  const [history, setHistory] = useState<CalendarNotification[]>([])
  const [stats, setStats] = useState<NotificationStats | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * Load notification history
   */
  const loadHistory = useCallback(async (filter?: NotificationFilter): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const userId = getCurrentUserId()
      const userHistory = await CalendarNotificationService.getNotificationHistory(userId, filter)
      setHistory(userHistory)
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to load notification history'
      setError(errorMessage)
      console.error('Failed to load notification history:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * Load notification statistics
   */
  const loadStats = useCallback(async (): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const userId = getCurrentUserId()
      const userStats = await CalendarNotificationService.getNotificationStats(userId)
      setStats(userStats)
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to load notification statistics'
      setError(errorMessage)
      console.error('Failed to load notification statistics:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * Clear notification history
   */
  const clearHistory = useCallback(async (): Promise<void> => {
    try {
      setHistory([])
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to clear notification history'
      setError(errorMessage)
      console.error('Failed to clear notification history:', err)
    }
  }, [])

  // Load history and stats on mount
  useEffect(() => {
    loadHistory()
    loadStats()
  }, [loadHistory, loadStats])

  return {
    history,
    stats,
    loading,
    error,
    loadHistory,
    loadStats,
    clearHistory,
  }
}

/**
 * Hook for managing user notification settings (simplified)
 */
export const useNotificationSettings = () => {
  const [settings, setSettings] = useState<CalendarNotificationUserSettings | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * Load settings
   */
  const loadSettings = useCallback(async (): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      // This would integrate with user settings API
      // For now, return default settings
      const defaultSettings: CalendarNotificationUserSettings = {
        enabled: true,
        reminders: {
          beforeEvent: 15,
          beforeDeadline: 60,
        },
        emailNotifications: true,
        inAppNotifications: true,
        pushNotifications: true,
        smsNotifications: false,
        quietHours: {
          enabled: false,
          startTime: '22:00',
          endTime: '08:00',
        },
      }

      setSettings(defaultSettings)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load settings'
      setError(errorMessage)
      console.error('Failed to load settings:', err)
    } finally {
      setLoading(false)
    }
  }, [])

  /**
   * Update settings
   */
  const updateSettings = useCallback(
    async (updates: Partial<CalendarNotificationUserSettings>): Promise<boolean> => {
      setLoading(true)
      setError(null)

      try {
        if (!settings) return false

        const updatedSettings = { ...settings, ...updates }
        setSettings(updatedSettings)

        // This would save to the user settings API
        return true
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to update settings'
        setError(errorMessage)
        console.error('Failed to update settings:', err)
        return false
      } finally {
        setLoading(false)
      }
    },
    [settings]
  )

  // Load settings on mount
  useEffect(() => {
    loadSettings()
  }, [loadSettings])

  return {
    settings,
    loading,
    error,
    loadSettings,
    updateSettings,
  }
}

// Helper function to get current user ID
function getCurrentUserId(): string {
  // This would come from authentication context
  // For now, return a mock user ID
  return 'current_user_id'
}

// Helper function to get affected users for an event
async function getAffectedUsers(eventId: string): Promise<string[]> {
  try {
    // This would get users associated with the event
    // For now, return mock data
    return ['user1', 'user2', 'user3']
  } catch (error) {
    console.error('Failed to get affected users:', error)
    return []
  }
}
