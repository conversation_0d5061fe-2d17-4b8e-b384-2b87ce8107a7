import { useState, useEffect, useCallback } from 'react'
import { CalendarPermissionService } from '../services/permissionService'
import { AuthService } from '@/features/auth/services/authService'
import type {
  CalendarPermission,
  CalendarPermissionType,
  PermissionConditions,
  PermissionContext,
  PermissionCheckResult,
  UserPermissionSummary,
  UseCalendarPermissionsReturn,
  UseEventPermissionsReturn,
  UseUserPermissionsReturn,
  UsePermissionChecksReturn,
} from '../types/permissions'
import type { EntityId } from '@/shared/types/common'

/**
 * Hook for managing calendar permissions
 */
export const useCalendarPermissions = (): UseCalendarPermissionsReturn => {
  const [permissions, setPermissions] = useState<CalendarPermission[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getCurrentUserId = useCallback(async (): Promise<EntityId> => {
    try {
      const user = await AuthService.getCurrentUser()
      return user.id
    } catch (err) {
      throw new Error('Failed to get current user')
    }
  }, [])

  const refreshPermissions = useCallback(async (): Promise<void> => {
    try {
      setLoading(true)
      setError(null)

      const userId = await getCurrentUserId()
      const userPermissions = await CalendarPermissionService.getUserPermissions(userId)
      setPermissions(userPermissions)
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to refresh permissions'
      setError(message)
      console.error('Error refreshing permissions:', err)
    } finally {
      setLoading(false)
    }
  }, [getCurrentUserId])

  const canViewCalendar = useCallback(async (): Promise<boolean> => {
    try {
      const userId = await getCurrentUserId()
      const result = await CalendarPermissionService.checkPermission(userId, 'view_calendar')
      return result.granted
    } catch (err) {
      console.error('Error checking view calendar permission:', err)
      return false
    }
  }, [getCurrentUserId])

  const canCreateEvents = useCallback(async (): Promise<boolean> => {
    try {
      const userId = await getCurrentUserId()
      const result = await CalendarPermissionService.checkPermission(userId, 'create_events')
      return result.granted
    } catch (err) {
      console.error('Error checking create events permission:', err)
      return false
    }
  }, [getCurrentUserId])

  const canEditEvent = useCallback(
    async (eventId: string): Promise<boolean> => {
      try {
        const userId = await getCurrentUserId()
        const result = await CalendarPermissionService.checkPermission(
          userId,
          'edit_events',
          eventId
        )
        return result.granted
      } catch (err) {
        console.error('Error checking edit event permission:', err)
        return false
      }
    },
    [getCurrentUserId]
  )

  const canDeleteEvent = useCallback(
    async (eventId: string): Promise<boolean> => {
      try {
        const userId = await getCurrentUserId()
        const result = await CalendarPermissionService.checkPermission(
          userId,
          'delete_events',
          eventId
        )
        return result.granted
      } catch (err) {
        console.error('Error checking delete event permission:', err)
        return false
      }
    },
    [getCurrentUserId]
  )

  const canManagePermissions = useCallback(async (): Promise<boolean> => {
    try {
      const userId = await getCurrentUserId()
      const result = await CalendarPermissionService.checkPermission(userId, 'manage_permissions')
      return result.granted
    } catch (err) {
      console.error('Error checking manage permissions:', err)
      return false
    }
  }, [getCurrentUserId])

  const canViewAllEvents = useCallback(async (): Promise<boolean> => {
    try {
      const userId = await getCurrentUserId()
      const result = await CalendarPermissionService.checkPermission(userId, 'view_all_events')
      return result.granted
    } catch (err) {
      console.error('Error checking view all events permission:', err)
      return false
    }
  }, [getCurrentUserId])

  const canManageResources = useCallback(async (): Promise<boolean> => {
    try {
      const userId = await getCurrentUserId()
      const result = await CalendarPermissionService.checkPermission(userId, 'manage_resources')
      return result.granted
    } catch (err) {
      console.error('Error checking manage resources permission:', err)
      return false
    }
  }, [getCurrentUserId])

  const grantPermission = useCallback(
    async (
      userId: string,
      permission: CalendarPermissionType,
      conditions?: PermissionConditions
    ): Promise<CalendarPermission> => {
      try {
        const currentUserId = await getCurrentUserId()
        const newPermission = await CalendarPermissionService.grantPermission(
          userId,
          permission,
          currentUserId,
          conditions
        )

        // Refresh permissions if granting to current user
        if (userId === currentUserId) {
          await refreshPermissions()
        }

        return newPermission
      } catch (err) {
        const message = err instanceof Error ? err.message : 'Failed to grant permission'
        setError(message)
        throw err
      }
    },
    [getCurrentUserId, refreshPermissions]
  )

  const revokePermission = useCallback(
    async (permissionId: string): Promise<void> => {
      try {
        const currentUserId = await getCurrentUserId()
        await CalendarPermissionService.revokePermission(permissionId, currentUserId)

        // Refresh permissions
        await refreshPermissions()
      } catch (err) {
        const message = err instanceof Error ? err.message : 'Failed to revoke permission'
        setError(message)
        throw err
      }
    },
    [getCurrentUserId, refreshPermissions]
  )

  // Load permissions on mount
  useEffect(() => {
    refreshPermissions()
  }, [refreshPermissions])

  return {
    permissions,
    loading,
    error,
    canViewCalendar,
    canCreateEvents,
    canEditEvent,
    canDeleteEvent,
    canManagePermissions,
    canViewAllEvents,
    canManageResources,
    refreshPermissions,
    grantPermission,
    revokePermission,
  }
}

/**
 * Hook for managing event-specific permissions
 */
export const useEventPermissions = (): UseEventPermissionsReturn => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getCurrentUserId = useCallback(async (): Promise<EntityId> => {
    try {
      const user = await AuthService.getCurrentUser()
      return user.id
    } catch (err) {
      throw new Error('Failed to get current user')
    }
  }, [])

  const canView = useCallback(
    async (eventId: string): Promise<boolean> => {
      try {
        const userId = await getCurrentUserId()
        const result = await CalendarPermissionService.checkPermission(
          userId,
          'view_calendar',
          eventId
        )
        return result.granted
      } catch (err) {
        console.error('Error checking event view permission:', err)
        return false
      }
    },
    [getCurrentUserId]
  )

  const canEdit = useCallback(
    async (eventId: string): Promise<boolean> => {
      try {
        const userId = await getCurrentUserId()
        return await CalendarPermissionService.canModifyEvent(userId, eventId, 'edit')
      } catch (err) {
        console.error('Error checking event edit permission:', err)
        return false
      }
    },
    [getCurrentUserId]
  )

  const canDelete = useCallback(
    async (eventId: string): Promise<boolean> => {
      try {
        const userId = await getCurrentUserId()
        return await CalendarPermissionService.canModifyEvent(userId, eventId, 'delete')
      } catch (err) {
        console.error('Error checking event delete permission:', err)
        return false
      }
    },
    [getCurrentUserId]
  )

  const canManageAttendees = useCallback(
    async (eventId: string): Promise<boolean> => {
      try {
        const userId = await getCurrentUserId()
        const result = await CalendarPermissionService.checkPermission(
          userId,
          'manage_team_events',
          eventId
        )
        return result.granted
      } catch (err) {
        console.error('Error checking manage attendees permission:', err)
        return false
      }
    },
    [getCurrentUserId]
  )

  const canManageResources = useCallback(
    async (eventId: string): Promise<boolean> => {
      try {
        const userId = await getCurrentUserId()
        const result = await CalendarPermissionService.checkPermission(
          userId,
          'manage_resources',
          eventId
        )
        return result.granted
      } catch (err) {
        console.error('Error checking manage resources permission:', err)
        return false
      }
    },
    [getCurrentUserId]
  )

  const isOwner = useCallback(
    async (eventId: string): Promise<boolean> => {
      try {
        const userId = await getCurrentUserId()
        const result = await CalendarPermissionService.checkPermission(
          userId,
          'view_own_events',
          eventId
        )
        return result.reason === 'Event owner'
      } catch (err) {
        console.error('Error checking event ownership:', err)
        return false
      }
    },
    [getCurrentUserId]
  )

  const getEventPermissions = useCallback(
    async (eventId: string): Promise<CalendarPermission[]> => {
      try {
        setLoading(true)
        setError(null)

        const userId = await getCurrentUserId()
        const allPermissions = await CalendarPermissionService.getUserPermissions(userId)
        const eventPermissions = allPermissions.filter((p) => p.eventId === eventId)

        return eventPermissions
      } catch (err) {
        const message = err instanceof Error ? err.message : 'Failed to get event permissions'
        setError(message)
        console.error('Error getting event permissions:', err)
        return []
      } finally {
        setLoading(false)
      }
    },
    [getCurrentUserId]
  )

  return {
    canView,
    canEdit,
    canDelete,
    canManageAttendees,
    canManageResources,
    isOwner,
    getEventPermissions,
  }
}

/**
 * Hook for managing user permissions
 */
export const useUserPermissions = (userId?: EntityId): UseUserPermissionsReturn => {
  const [userPermissions, setUserPermissions] = useState<CalendarPermission[]>([])
  const [permissionSummary, setPermissionSummary] = useState<UserPermissionSummary | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getCurrentUserId = useCallback(async (): Promise<EntityId> => {
    if (userId) return userId

    try {
      const user = await AuthService.getCurrentUser()
      return user.id
    } catch (err) {
      throw new Error('Failed to get current user')
    }
  }, [userId])

  const refreshUserPermissions = useCallback(async (): Promise<void> => {
    try {
      setLoading(true)
      setError(null)

      const targetUserId = await getCurrentUserId()
      const permissions = await CalendarPermissionService.getUserPermissions(targetUserId)
      const summary = await CalendarPermissionService.getUserPermissionSummary(targetUserId)

      setUserPermissions(permissions)
      setPermissionSummary(summary)
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to refresh user permissions'
      setError(message)
      console.error('Error refreshing user permissions:', err)
    } finally {
      setLoading(false)
    }
  }, [getCurrentUserId])

  const hasPermission = useCallback(
    (permission: CalendarPermissionType): boolean => {
      if (!permissionSummary) return false
      return permissionSummary.effectivePermissions.has(permission)
    },
    [permissionSummary]
  )

  const hasAnyPermission = useCallback(
    (permissions: CalendarPermissionType[]): boolean => {
      if (!permissionSummary) return false
      return permissions.some((p) => permissionSummary.effectivePermissions.has(p))
    },
    [permissionSummary]
  )

  const hasAllPermissions = useCallback(
    (permissions: CalendarPermissionType[]): boolean => {
      if (!permissionSummary) return false
      return permissions.every((p) => permissionSummary.effectivePermissions.has(p))
    },
    [permissionSummary]
  )

  // Load permissions on mount and when userId changes
  useEffect(() => {
    refreshUserPermissions()
  }, [refreshUserPermissions])

  return {
    userPermissions,
    effectivePermissions: permissionSummary?.effectivePermissions || new Set(),
    permissionSummary,
    loading,
    error,
    refreshUserPermissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
  }
}

/**
 * Hook for permission checking and validation
 */
export const usePermissionChecks = (): UsePermissionChecksReturn => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const getCurrentUserId = useCallback(async (): Promise<EntityId> => {
    try {
      const user = await AuthService.getCurrentUser()
      return user.id
    } catch (err) {
      throw new Error('Failed to get current user')
    }
  }, [])

  const checkPermission = useCallback(
    async (
      permission: CalendarPermissionType,
      context?: Partial<PermissionContext>
    ): Promise<PermissionCheckResult> => {
      try {
        setLoading(true)
        setError(null)

        const userId = await getCurrentUserId()
        const result = await CalendarPermissionService.checkPermission(
          userId,
          permission,
          context?.eventId,
          context?.calendarId,
          context
        )

        return result
      } catch (err) {
        const message = err instanceof Error ? err.message : 'Failed to check permission'
        setError(message)
        console.error('Error checking permission:', err)
        return { granted: false, reason: message }
      } finally {
        setLoading(false)
      }
    },
    [getCurrentUserId]
  )

  const checkMultiplePermissions = useCallback(
    async (
      permissions: CalendarPermissionType[],
      context?: Partial<PermissionContext>
    ): Promise<Record<CalendarPermissionType, PermissionCheckResult>> => {
      try {
        setLoading(true)
        setError(null)

        const results: Partial<Record<CalendarPermissionType, PermissionCheckResult>> = {}

        for (const permission of permissions) {
          results[permission] = await checkPermission(permission, context)
        }

        return results as Record<CalendarPermissionType, PermissionCheckResult>
      } catch (err) {
        const message = err instanceof Error ? err.message : 'Failed to check permissions'
        setError(message)
        console.error('Error checking multiple permissions:', err)

        // Return failed results for all permissions
        const failedResults: Partial<Record<CalendarPermissionType, PermissionCheckResult>> = {}
        permissions.forEach((p) => {
          failedResults[p] = { granted: false, reason: message }
        })
        return failedResults as Record<CalendarPermissionType, PermissionCheckResult>
      } finally {
        setLoading(false)
      }
    },
    [checkPermission]
  )

  const validatePermissionConditions = useCallback(
    (conditions: PermissionConditions, context: PermissionContext): boolean => {
      return CalendarPermissionService.validatePermissionConditions(conditions, context)
    },
    []
  )

  const getPermissionReason = useCallback(
    (permission: CalendarPermissionType, result: PermissionCheckResult): string => {
      if (result.granted) {
        return result.reason || `Permission '${permission}' granted`
      } else {
        return result.reason || `Permission '${permission}' denied`
      }
    },
    []
  )

  return {
    checkPermission,
    checkMultiplePermissions,
    validatePermissionConditions,
    getPermissionReason,
  }
}

/**
 * Higher-order hook for permission-based component rendering
 */
export const usePermissionGuard = (
  permission: CalendarPermissionType,
  context?: Partial<PermissionContext>
) => {
  const { checkPermission } = usePermissionChecks()
  const [hasPermission, setHasPermission] = useState(false)
  const [checking, setChecking] = useState(true)

  useEffect(() => {
    const check = async () => {
      try {
        setChecking(true)
        const result = await checkPermission(permission, context)
        setHasPermission(result.granted)
      } catch (err) {
        console.error('Error in permission guard:', err)
        setHasPermission(false)
      } finally {
        setChecking(false)
      }
    }

    check()
  }, [permission, context, checkPermission])

  return {
    hasPermission,
    loading: checking,
  }
}

/**
 * Hook for managing permission statistics
 */
export const usePermissionStatistics = (filter?: any) => {
  const [statistics, setStatistics] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const loadStatistics = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      const stats = await CalendarPermissionService.getPermissionStatistics(filter)
      setStatistics(stats)
    } catch (err) {
      const message = err instanceof Error ? err.message : 'Failed to load permission statistics'
      setError(message)
      console.error('Error loading permission statistics:', err)
    } finally {
      setLoading(false)
    }
  }, [filter])

  useEffect(() => {
    loadStatistics()
  }, [loadStatistics])

  return {
    statistics,
    loading,
    error,
    refresh: loadStatistics,
  }
}

export default {
  useCalendarPermissions,
  useEventPermissions,
  useUserPermissions,
  usePermissionChecks,
  usePermissionGuard,
  usePermissionStatistics,
}
