import { useState, useCallback, useEffect } from 'react'
import { CalendarService, CalendarError } from '../services/calendarService'
import { useAsyncOperation } from '../../../hooks/useAsyncOperation'
import type {
  CalendarEvent,
  CreateEventDto,
  UpdateEventDto,
  CalendarEventFilter,
  CalendarStats,
} from '../types'

/**
 * Async state interface for calendar operations
 */
interface AsyncState<T> {
  data: T | null
  loading: boolean
  error: string | null
}

/**
 * Hook for fetching calendar events with filtering and caching
 */
export const useCalendarEvents = (filters?: CalendarEventFilter) => {
  const [state, setState] = useState<AsyncState<CalendarEvent[]>>({
    data: null,
    loading: false,
    error: null,
  })

  const { execute } = useAsyncOperation<CalendarEvent[]>()

  const loadEvents = useCallback(async () => {
    setState((prev) => ({ ...prev, loading: true, error: null }))
    try {
      const events = await execute(() => CalendarService.getEvents(filters))
      setState({ data: events || [], loading: false, error: null })
      return events
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load events'
      setState((prev) => ({ ...prev, loading: false, error: errorMessage }))
      return []
    }
  }, [execute, filters])

  const refetch = useCallback(() => {
    return loadEvents()
  }, [loadEvents])

  // Auto-load events on mount and when filters change
  useEffect(() => {
    loadEvents()
  }, [loadEvents])

  return {
    ...state,
    loadEvents,
    refetch,
    isLoading: state.loading,
    events: state.data || [],
  }
}

/**
 * Hook for calendar event mutations (create, update, delete)
 */
export const useCalendarMutations = () => {
  const [state, setState] = useState<{
    loading: boolean
    error: string | null
    lastOperation: string | null
  }>({
    loading: false,
    error: null,
    lastOperation: null,
  })

  const { execute } = useAsyncOperation()

  const createEvent = useCallback(
    async (eventData: CreateEventDto): Promise<CalendarEvent | null> => {
      setState((prev) => ({ ...prev, loading: true, error: null, lastOperation: 'create' }))
      try {
        const event = await execute(() => CalendarService.createEvent(eventData))
        setState({ loading: false, error: null, lastOperation: 'create-success' })
        return event
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to create event'
        setState((prev) => ({
          ...prev,
          loading: false,
          error: errorMessage,
          lastOperation: 'create-error',
        }))
        return null
      }
    },
    [execute]
  )

  const updateEvent = useCallback(
    async (eventData: UpdateEventDto): Promise<CalendarEvent | null> => {
      setState((prev) => ({ ...prev, loading: true, error: null, lastOperation: 'update' }))
      try {
        const event = await execute(() => CalendarService.updateEvent(eventData))
        setState({ loading: false, error: null, lastOperation: 'update-success' })
        return event
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to update event'
        setState((prev) => ({
          ...prev,
          loading: false,
          error: errorMessage,
          lastOperation: 'update-error',
        }))
        return null
      }
    },
    [execute]
  )

  const deleteEvent = useCallback(
    async (eventId: string): Promise<boolean> => {
      setState((prev) => ({ ...prev, loading: true, error: null, lastOperation: 'delete' }))
      try {
        await execute(() => CalendarService.deleteEvent(eventId))
        setState({ loading: false, error: null, lastOperation: 'delete-success' })
        return true
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to delete event'
        setState((prev) => ({
          ...prev,
          loading: false,
          error: errorMessage,
          lastOperation: 'delete-error',
        }))
        return false
      }
    },
    [execute]
  )

  const reset = useCallback(() => {
    setState({ loading: false, error: null, lastOperation: null })
  }, [])

  return {
    ...state,
    createEvent,
    updateEvent,
    deleteEvent,
    reset,
    isLoading: state.loading,
    isCreating: state.lastOperation?.startsWith('create'),
    isUpdating: state.lastOperation?.startsWith('update'),
    isDeleting: state.lastOperation?.startsWith('delete'),
  }
}

/**
 * Hook for calendar synchronization and real-time updates
 */
export const useCalendarSync = (filters?: CalendarEventFilter) => {
  const [state, setState] = useState<{
    isOnline: boolean
    lastSync: Date | null
    syncInProgress: boolean
    error: string | null
  }>({
    isOnline: navigator.onLine,
    lastSync: null,
    syncInProgress: false,
    error: null,
  })

  const { execute } = useAsyncOperation()

  const syncEvents = useCallback(async (): Promise<CalendarEvent[]> => {
    setState((prev) => ({ ...prev, syncInProgress: true, error: null }))
    try {
      const events = await execute(() => CalendarService.getEvents(filters))
      setState((prev) => ({
        ...prev,
        syncInProgress: false,
        lastSync: new Date(),
        error: null,
      }))
      return events
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sync failed'
      setState((prev) => ({
        ...prev,
        syncInProgress: false,
        error: errorMessage,
      }))
      return []
    }
  }, [execute, filters])

  // Handle online/offline status
  useEffect(() => {
    const handleOnline = () => setState((prev) => ({ ...prev, isOnline: true }))
    const handleOffline = () => setState((prev) => ({ ...prev, isOnline: false }))

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  // Auto-sync when coming back online
  useEffect(() => {
    if (state.isOnline && !state.lastSync) {
      syncEvents()
    }
  }, [state.isOnline, state.lastSync, syncEvents])

  return {
    ...state,
    syncEvents,
    forceSync: syncEvents,
  }
}

/**
 * Hook for calendar statistics and analytics
 */
export const useCalendarStats = (filters?: CalendarEventFilter) => {
  const [state, setState] = useState<AsyncState<CalendarStats>>({
    data: null,
    loading: false,
    error: null,
  })

  const { execute } = useAsyncOperation<CalendarStats>()

  const loadStats = useCallback(async () => {
    setState((prev) => ({ ...prev, loading: true, error: null }))
    try {
      const stats = await execute(() => CalendarService.getCalendarStats(filters))
      setState({ data: stats, loading: false, error: null })
      return stats
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to load statistics'
      setState((prev) => ({ ...prev, loading: false, error: errorMessage }))
      return null
    }
  }, [execute, filters])

  const refetch = useCallback(() => {
    return loadStats()
  }, [loadStats])

  // Auto-load stats on mount and when filters change
  useEffect(() => {
    loadStats()
  }, [loadStats])

  return {
    ...state,
    loadStats,
    refetch,
    isLoading: state.loading,
    stats: state.data,
  }
}

/**
 * Hook for calendar export/import functionality
 */
export const useCalendarImportExport = () => {
  const [state, setState] = useState<{
    loading: boolean
    error: string | null
    lastOperation: string | null
  }>({
    loading: false,
    error: null,
    lastOperation: null,
  })

  const { execute } = useAsyncOperation()

  const exportEvents = useCallback(
    async (format: 'json' | 'ics' | 'csv', filters?: CalendarEventFilter): Promise<Blob | null> => {
      setState((prev) => ({ ...prev, loading: true, error: null, lastOperation: 'export' }))
      try {
        const blob = await execute(() => CalendarService.exportEvents({ format, filter: filters }))
        setState({ loading: false, error: null, lastOperation: 'export-success' })
        return blob
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Export failed'
        setState((prev) => ({
          ...prev,
          loading: false,
          error: errorMessage,
          lastOperation: 'export-error',
        }))
        return null
      }
    },
    [execute]
  )

  const importEvents = useCallback(
    async (file: File): Promise<{ imported: number; errors: string[] } | null> => {
      setState((prev) => ({ ...prev, loading: true, error: null, lastOperation: 'import' }))
      try {
        const result = await execute(() => CalendarService.importEvents(file))
        setState({ loading: false, error: null, lastOperation: 'import-success' })
        return result
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Import failed'
        setState((prev) => ({
          ...prev,
          loading: false,
          error: errorMessage,
          lastOperation: 'import-error',
        }))
        return null
      }
    },
    [execute]
  )

  const reset = useCallback(() => {
    setState({ loading: false, error: null, lastOperation: null })
  }, [])

  return {
    ...state,
    exportEvents,
    importEvents,
    reset,
    isLoading: state.loading,
    isExporting: state.lastOperation?.startsWith('export'),
    isImporting: state.lastOperation?.startsWith('import'),
  }
}

/**
 * Hook for available time slots calculation
 */
export const useAvailableTimeSlots = () => {
  const [state, setState] = useState<AsyncState<Date[]>>({
    data: null,
    loading: false,
    error: null,
  })

  const { execute } = useAsyncOperation<Date[]>()

  const getAvailableSlots = useCallback(
    async (
      startDate: Date,
      endDate: Date,
      duration: number,
      resourceId?: string
    ): Promise<Date[]> => {
      setState((prev) => ({ ...prev, loading: true, error: null }))
      try {
        const slots = await execute(() =>
          CalendarService.getAvailableTimeSlots(startDate, endDate, duration, resourceId)
        )
        setState({ data: slots, loading: false, error: null })
        return slots || []
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Failed to get available slots'
        setState((prev) => ({ ...prev, loading: false, error: errorMessage }))
        return []
      }
    },
    [execute]
  )

  const reset = useCallback(() => {
    setState({ data: null, loading: false, error: null })
  }, [])

  return {
    ...state,
    getAvailableSlots,
    reset,
    isLoading: state.loading,
    availableSlots: state.data || [],
  }
}

/**
 * Combined hook for comprehensive calendar management
 */
export const useCalendar = (filters?: CalendarEventFilter) => {
  const events = useCalendarEvents(filters)
  const mutations = useCalendarMutations()
  const sync = useCalendarSync(filters)
  const stats = useCalendarStats(filters)
  const importExport = useCalendarImportExport()
  const timeSlots = useAvailableTimeSlots()

  // Refetch all data when mutations succeed
  useEffect(() => {
    if (mutations.lastOperation?.endsWith('-success')) {
      events.refetch()
      stats.refetch()
    }
  }, [mutations.lastOperation, events.refetch, stats.refetch])

  return {
    events,
    mutations,
    sync,
    stats,
    importExport,
    timeSlots,
    isLoading: events.isLoading || mutations.isLoading || sync.syncInProgress,
    error: events.error || mutations.error || sync.error || stats.error,
  }
}
