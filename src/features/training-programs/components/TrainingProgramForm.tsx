import { useState } from 'react'
import {
  <PERSON>,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { useTrainingPrograms } from '../hooks/useTrainingPrograms'
import { Save, X } from 'lucide-react'
import type { TrainingProgram } from '@/shared/types'

interface TrainingProgramFormProps {
  program?: TrainingProgram
  onSave: (program: TrainingProgram) => void
  onCancel: () => void
}

const PRESET_DURATIONS = [12, 18, 24, 36]

export function TrainingProgramForm({ program, onSave, onCancel }: TrainingProgramFormProps) {
  const { createTrainingProgram, updateTrainingProgram } = useTrainingPrograms()

  const [formData, setFormData] = useState({
    name: program?.name || '',
    durationMonths: program?.durationMonths || 24,
    description: program?.description || '',
  })

  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Program name is required'
    }

    if (!formData.durationMonths || formData.durationMonths < 1) {
      newErrors.durationMonths = 'Duration must be at least 1 month'
    }

    if (!formData.description.trim()) {
      newErrors.description = 'Description is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      return
    }

    setLoading(true)
    try {
      let savedProgram: TrainingProgram
      if (program) {
        savedProgram = await updateTrainingProgram(program.id, formData)
      } else {
        savedProgram = await createTrainingProgram(formData)
      }
      onSave(savedProgram)
    } catch (error) {
      console.error('Error saving training program:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (field: string, value: string | number) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: '' }))
    }
  }

  const handlePresetDuration = (duration: number) => {
    handleInputChange('durationMonths', duration)
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>{program ? 'Edit Training Program' : 'Create New Training Program'}</CardTitle>
        <CardDescription>
          {program
            ? 'Update training program details and duration'
            : 'Add a new training program to the system'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Program Name</Label>
            <Input
              id="name"
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              placeholder="Enter program name"
              className={errors.name ? 'border-destructive' : ''}
            />
            {errors.name && <p className="text-destructive text-sm">{errors.name}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="durationMonths">Duration (months)</Label>
            <div className="flex items-center space-x-2">
              <Input
                id="durationMonths"
                type="number"
                min="1"
                max="60"
                value={formData.durationMonths}
                onChange={(e) => handleInputChange('durationMonths', parseInt(e.target.value) || 0)}
                placeholder="Enter duration in months"
                className={errors.durationMonths ? 'border-destructive' : ''}
              />
              <span className="text-muted-foreground text-sm">months</span>
            </div>
            {errors.durationMonths && (
              <p className="text-destructive text-sm">{errors.durationMonths}</p>
            )}

            <div className="mt-2">
              <Label className="text-muted-foreground text-sm">Quick presets:</Label>
              <div className="mt-1 flex flex-wrap gap-2">
                {PRESET_DURATIONS.map((duration) => (
                  <Button
                    key={duration}
                    type="button"
                    variant={formData.durationMonths === duration ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handlePresetDuration(duration)}
                  >
                    {duration} months
                  </Button>
                ))}
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              placeholder="Enter program description"
              rows={4}
              className={`border-input ring-offset-background placeholder:text-muted-foreground focus-visible:ring-ring flex min-h-[80px] w-full rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 ${
                errors.description ? 'border-destructive' : ''
              }`}
            />
            {errors.description && <p className="text-destructive text-sm">{errors.description}</p>}
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="mr-2 h-4 w-4" />
              {loading ? 'Saving...' : program ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
