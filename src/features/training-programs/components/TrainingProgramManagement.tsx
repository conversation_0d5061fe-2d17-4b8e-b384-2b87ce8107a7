import { useState, useMemo } from 'react'
import { TrainingProgramList } from './TrainingProgramList'
import { TrainingProgramForm } from './TrainingProgramForm'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Badge } from '@/shared/components/ui/badge'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/shared/components/ui/select'
import { useTrainingPrograms } from '../hooks/useTrainingPrograms'
import { useTrainees } from '../../trainees/hooks/useTrainees'
import {
  BookOpen,
  Plus,
  Search,
  Filter,
  Grid,
  List,
  Users,
  Clock,
  TrendingUp,
  Calendar,
  Award,
} from 'lucide-react'
import type { TrainingProgram } from '@/shared/types'

type ViewMode = 'list' | 'create' | 'edit'

export function TrainingProgramManagement() {
  const [viewMode, setViewMode] = useState<ViewMode>('list')
  const [selectedProgram, setSelectedProgram] = useState<TrainingProgram | undefined>(undefined)
  const [searchQuery, setSearchQuery] = useState('')
  const [durationFilter, setDurationFilter] = useState<number | undefined>(undefined)
  const [sortBy, setSortBy] = useState<'name' | 'duration' | 'trainees'>('name')
  const [viewType, setViewType] = useState<'grid' | 'list'>('grid')

  const { trainingPrograms, loading, error } = useTrainingPrograms()
  const { trainees } = useTrainees()

  // Calculate statistics
  const stats = useMemo(() => {
    if (!trainingPrograms || !Array.isArray(trainingPrograms))
      return {
        totalPrograms: 0,
        totalTrainees: 0,
        averageDuration: 0,
        longestProgram: null,
      }

    const totalPrograms = trainingPrograms.length
    const totalTrainees = trainees?.length || 0
    const averageDuration =
      totalPrograms > 0
        ? Math.round(
            trainingPrograms.reduce((acc, program) => acc + (program.durationMonths || 0), 0) /
              totalPrograms
          )
        : 0
    const longestProgram = trainingPrograms.reduce(
      (longest, program) =>
        program.durationMonths > (longest?.durationMonths || 0) ? program : longest,
      null
    )

    return { totalPrograms, totalTrainees, averageDuration, longestProgram }
  }, [trainingPrograms, trainees])

  // Filter and sort programs
  const filteredAndSortedPrograms = useMemo(() => {
    if (!trainingPrograms || !Array.isArray(trainingPrograms)) return []

    const filtered = trainingPrograms.filter((program) => {
      const matchesSearch =
        !searchQuery ||
        program.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (program.description &&
          program.description.toLowerCase().includes(searchQuery.toLowerCase()))

      const matchesDuration = !durationFilter || program.durationMonths === durationFilter

      return matchesSearch && matchesDuration
    })

    // Sort programs
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name)
        case 'duration':
          return b.durationMonths - a.durationMonths
        case 'trainees':
          const aTrainees = trainees?.filter((t) => t.trainingProgramId === a.id).length || 0
          const bTrainees = trainees?.filter((t) => t.trainingProgramId === b.id).length || 0
          return bTrainees - aTrainees
        default:
          return 0
      }
    })

    return filtered
  }, [trainingPrograms, searchQuery, durationFilter, sortBy, trainees])

  const handleCreate = () => {
    setSelectedProgram(undefined)
    setViewMode('create')
  }

  const handleEdit = (program: TrainingProgram) => {
    setSelectedProgram(program)
    setViewMode('edit')
  }

  const handleSave = () => {
    setSelectedProgram(undefined)
    setViewMode('list')
  }

  const handleCancel = () => {
    setSelectedProgram(undefined)
    setViewMode('list')
  }

  const handleDelete = () => {
    // The delete functionality is handled in the TrainingProgramList component
    // through the useTrainingPrograms hook
  }

  if (viewMode === 'create' || viewMode === 'edit') {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-xl font-semibold">
            {viewMode === 'create' ? 'Create Training Program' : 'Edit Training Program'}
          </h2>
          <Button variant="outline" onClick={handleCancel}>
            Back to List
          </Button>
        </div>
        <TrainingProgramForm
          program={selectedProgram}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Stats Dashboard */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Programs</CardTitle>
            <BookOpen className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPrograms}</div>
            <p className="text-muted-foreground text-xs">Active training programs</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Trainees</CardTitle>
            <Users className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalTrainees}</div>
            <p className="text-muted-foreground text-xs">Enrolled trainees</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Duration</CardTitle>
            <Clock className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.averageDuration}m</div>
            <p className="text-muted-foreground text-xs">Average program length</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Longest Program</CardTitle>
            <TrendingUp className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {stats.longestProgram ? `${stats.longestProgram.durationMonths}m` : 'N/A'}
            </div>
            <p className="text-muted-foreground truncate text-xs">
              {stats.longestProgram?.name || 'No programs'}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Header with Search and Actions */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold">Training Programs</h2>
          <p className="text-muted-foreground">Manage your training programs and curricula</p>
        </div>
        <Button onClick={handleCreate}>
          <Plus className="mr-2 h-4 w-4" />
          Add Program
        </Button>
      </div>

      {/* Filters and Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters & Search</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-4">
            <div className="relative">
              <Search className="text-muted-foreground absolute top-2.5 left-2 h-4 w-4" />
              <Input
                placeholder="Search programs..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-8"
              />
            </div>

            <Select
              value={durationFilter?.toString() || ''}
              onValueChange={(value) => setDurationFilter(value ? parseInt(value) : undefined)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Duration" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All durations</SelectItem>
                <SelectItem value="12">12 months</SelectItem>
                <SelectItem value="18">18 months</SelectItem>
                <SelectItem value="24">24 months</SelectItem>
                <SelectItem value="36">36 months</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={sortBy}
              onValueChange={(value: 'name' | 'duration' | 'trainees') => setSortBy(value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="duration">Duration</SelectItem>
                <SelectItem value="trainees">Trainees</SelectItem>
              </SelectContent>
            </Select>

            <div className="flex gap-2">
              <Button
                variant={viewType === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewType('grid')}
              >
                <Grid className="h-4 w-4" />
              </Button>
              <Button
                variant={viewType === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewType('list')}
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Training Programs Display */}
      <TrainingProgramList
        programs={filteredAndSortedPrograms}
        loading={loading}
        error={error}
        viewType={viewType}
        onCreate={handleCreate}
        onEdit={handleEdit}
        onDelete={handleDelete}
      />
    </div>
  )
}

export default TrainingProgramManagement
