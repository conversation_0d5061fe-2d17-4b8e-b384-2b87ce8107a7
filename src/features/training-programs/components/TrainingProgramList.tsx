import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { But<PERSON> } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'
import {
  BookOpen,
  Plus,
  Edit,
  Trash2,
  Clock,
  Users,
  Calendar,
  Award,
  TrendingUp,
  BarChart3,
} from 'lucide-react'
import type { TrainingProgram } from '@/shared/types'
import type { Trainee } from '@/shared/types/trainee'

interface TrainingProgramListProps {
  programs?: TrainingProgram[]
  loading?: boolean
  error?: string
  viewType?: 'grid' | 'list'
  onEdit?: (program: TrainingProgram) => void
  onDelete?: (program: TrainingProgram) => void
  onCreate?: () => void
  trainees?: Trainee[]
}

export function TrainingProgramList({
  programs = [],
  loading = false,
  error,
  viewType = 'grid',
  onEdit,
  onDelete,
  onCreate,
  trainees = [],
}: TrainingProgramListProps) {
  // Count trainees for each program
  const getTraineeCount = (programId: string) => {
    return trainees.filter((trainee) => trainee.trainingProgramId === programId).length
  }

  // Get trainee completion rate for a program
  const getCompletionRate = (programId: string) => {
    const programTrainees = trainees.filter((trainee) => trainee.trainingProgramId === programId)
    if (programTrainees.length === 0) return 0

    const totalProgress = programTrainees.reduce((acc, trainee) => acc + (trainee.progress || 0), 0)
    return Math.round(totalProgress / programTrainees.length)
  }

  if (loading) {
    return (
      <Card>
        <CardContent className="flex h-64 items-center justify-center">
          <div className="text-center">
            <BookOpen className="text-muted-foreground mx-auto mb-4 h-8 w-8 animate-pulse" />
            <p className="text-muted-foreground">Loading training programs...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex h-64 items-center justify-center">
          <div className="text-center">
            <BookOpen className="text-destructive mx-auto mb-4 h-8 w-8" />
            <p className="text-destructive font-medium">Error loading training programs</p>
            <p className="text-muted-foreground text-sm">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const getDurationBadgeVariant = (duration: number) => {
    if (duration <= 12) return 'default'
    if (duration <= 24) return 'secondary'
    return 'outline'
  }

  const getCompletionColor = (rate: number) => {
    if (rate >= 80) return 'text-green-600'
    if (rate >= 60) return 'text-yellow-600'
    return 'text-red-600'
  }

  if (programs.length === 0) {
    return (
      <Card>
        <CardContent className="py-16">
          <div className="text-center">
            <BookOpen className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
            <h3 className="mb-2 text-lg font-medium">No training programs found</h3>
            <p className="text-muted-foreground mb-6">
              Get started by creating your first training program
            </p>
            {onCreate && (
              <Button onClick={onCreate}>
                <Plus className="mr-2 h-4 w-4" />
                Create Training Program
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  // Grid View
  if (viewType === 'grid') {
    return (
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {programs.map((program) => {
          const traineeCount = getTraineeCount(program.id)
          const completionRate = getCompletionRate(program.id)

          return (
            <Card key={program.id} className="group transition-shadow hover:shadow-lg">
              <CardHeader className="pb-3">
                <div className="flex items-center justify-between">
                  <div className="bg-primary/10 flex h-10 w-10 items-center justify-center rounded-full">
                    <BookOpen className="text-primary h-5 w-5" />
                  </div>
                  <Badge variant={getDurationBadgeVariant(program.durationMonths)}>
                    {program.durationMonths}m
                  </Badge>
                </div>
                <CardTitle className="text-lg">{program.name}</CardTitle>
                {program.description && (
                  <CardDescription className="line-clamp-2">{program.description}</CardDescription>
                )}
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Stats */}
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center">
                    <div className="text-muted-foreground flex items-center justify-center gap-1 text-sm">
                      <Users className="h-3 w-3" />
                      <span>{traineeCount}</span>
                    </div>
                    <p className="text-muted-foreground text-xs">Trainees</p>
                  </div>
                  <div className="text-center">
                    <div
                      className={`flex items-center justify-center gap-1 text-sm font-medium ${getCompletionColor(completionRate)}`}
                    >
                      <TrendingUp className="h-3 w-3" />
                      <span>{completionRate}%</span>
                    </div>
                    <p className="text-muted-foreground text-xs">Completion</p>
                  </div>
                </div>

                {/* Progress Bar */}
                <div className="space-y-2">
                  <div className="text-muted-foreground flex justify-between text-xs">
                    <span>Progress</span>
                    <span>{completionRate}%</span>
                  </div>
                  <div className="bg-secondary h-2 w-full overflow-hidden rounded-full">
                    <div
                      className="bg-primary h-full transition-all duration-300"
                      style={{ width: `${completionRate}%` }}
                    />
                  </div>
                </div>

                {/* Actions */}
                <div className="flex gap-2 pt-2">
                  {onEdit && (
                    <Button
                      variant="outline"
                      size="sm"
                      className="flex-1"
                      onClick={() => onEdit(program)}
                    >
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </Button>
                  )}
                  {onDelete && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onDelete(program)}
                      className="text-destructive hover:text-destructive"
                      disabled={traineeCount > 0}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
          )
        })}
      </div>
    )
  }

  // List View
  return (
    <Card>
      <CardHeader>
        <CardTitle>Training Programs ({programs.length})</CardTitle>
        <CardDescription>Comprehensive overview of all training programs</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {programs.map((program) => {
            const traineeCount = getTraineeCount(program.id)
            const completionRate = getCompletionRate(program.id)

            return (
              <div
                key={program.id}
                className="hover:bg-muted/50 flex items-center justify-between rounded-lg border p-4 transition-colors"
              >
                <div className="flex flex-1 items-center space-x-4">
                  <div className="bg-primary/10 flex h-12 w-12 items-center justify-center rounded-full">
                    <BookOpen className="text-primary h-6 w-6" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="mb-1 flex items-center gap-3">
                      <h3 className="truncate font-medium">{program.name}</h3>
                      <Badge variant={getDurationBadgeVariant(program.durationMonths)}>
                        {program.durationMonths} months
                      </Badge>
                    </div>
                    {program.description && (
                      <p className="text-muted-foreground mb-2 line-clamp-1 text-sm">
                        {program.description}
                      </p>
                    )}
                    <div className="flex items-center space-x-6 text-sm">
                      <div className="text-muted-foreground flex items-center space-x-1">
                        <Users className="h-4 w-4" />
                        <span>{traineeCount} trainees</span>
                      </div>
                      <div
                        className={`flex items-center space-x-1 font-medium ${getCompletionColor(completionRate)}`}
                      >
                        <TrendingUp className="h-4 w-4" />
                        <span>{completionRate}% completion</span>
                      </div>
                      <div className="text-muted-foreground flex items-center space-x-1">
                        <Clock className="h-4 w-4" />
                        <span>{program.durationMonths} months</span>
                      </div>
                    </div>
                    {/* Progress bar for list view */}
                    <div className="mt-2 w-full max-w-md">
                      <div className="bg-secondary h-1.5 w-full overflow-hidden rounded-full">
                        <div
                          className="bg-primary h-full transition-all duration-300"
                          style={{ width: `${completionRate}%` }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div className="ml-4 flex items-center space-x-3">
                  <div className="text-right">
                    <div className={`text-lg font-bold ${getCompletionColor(completionRate)}`}>
                      {completionRate}%
                    </div>
                    <p className="text-muted-foreground text-xs">Avg. Progress</p>
                  </div>
                  <div className="flex space-x-2">
                    {onEdit && (
                      <Button variant="outline" size="sm" onClick={() => onEdit(program)}>
                        <Edit className="h-4 w-4" />
                      </Button>
                    )}
                    {onDelete && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onDelete(program)}
                        className="text-destructive hover:text-destructive"
                        disabled={traineeCount > 0}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
