import { describe, it, expect, vi } from 'vitest'
import { reportService } from '../../services/reportService'
import { TauriAPI } from '../../../services/tauriAPI'

// Mock Tauri API
vi.mock('../../services/tauriAPI', () => ({
  TauriAPI: {
    getReports: vi.fn(),
    generateReport: vi.fn(),
    deleteReport: vi.fn(),
    downloadReport: vi.fn(),
    shareReport: vi.fn(),
  },
}))

describe('ReportService Integration', () => {
  it('should have all the required methods', () => {
    expect(typeof reportService.getReports).toBe('function')
    expect(typeof reportService.getReport).toBe('function')
    expect(typeof reportService.generateReport).toBe('function')
    expect(typeof reportService.deleteReport).toBe('function')
    expect(typeof reportService.downloadReport).toBe('function')
    expect(typeof reportService.shareReport).toBe('function')
    expect(typeof reportService.getReportStatus).toBe('function')
    expect(typeof reportService.cancelReportGeneration).toBe('function')
    expect(typeof reportService.getReportTemplates).toBe('function')
    expect(typeof reportService.generateFromTemplate).toBe('function')
    expect(typeof reportService.getReportAnalytics).toBe('function')
  })
})
