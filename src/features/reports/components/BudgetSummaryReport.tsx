import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Progress } from '@/shared/components/ui/progress'
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Users,
  Calendar,
  Download,
  Share2,
  Filter,
  BarChart3,
  PieChart,
} from 'lucide-react'
import { budgetReportService } from '../services'
import type { BudgetSummaryReportData, BudgetReportParameters } from '../types'

interface BudgetSummaryReportProps {
  parameters: BudgetReportParameters
  onExport?: (format: 'pdf' | 'excel' | 'csv') => void
  onShare?: () => void
}

export function BudgetSummaryReport({ parameters, onExport, onShare }: BudgetSummaryReportProps) {
  const [reportData, setReportData] = useState<BudgetSummaryReportData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchReportData = async () => {
      setIsLoading(true)
      setError(null)
      try {
        const data = await budgetReportService.generateBudgetSummaryReport(parameters)
        setReportData(data)
      } catch (err) {
        setError('Failed to load budget summary report')
        console.error('Error fetching budget summary report:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchReportData()
  }, [parameters])

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`
  }

  const getUtilizationColor = (rate: number) => {
    if (rate >= 90) return 'text-red-600'
    if (rate >= 75) return 'text-yellow-600'
    return 'text-green-600'
  }

  const getTrendIcon = (trend: string) => {
    return trend === 'up' ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : trend === 'down' ? (
      <TrendingDown className="h-4 w-4 text-red-600" />
    ) : (
      <div className="h-4 w-4 rounded-full bg-gray-300" />
    )
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="mb-2 h-4 w-3/4 rounded bg-gray-200"></div>
                <div className="h-8 w-1/2 rounded bg-gray-200"></div>
              </CardContent>
            </Card>
          ))}
        </div>
        <Card className="animate-pulse">
          <CardContent className="p-6">
            <div className="mb-4 h-6 w-1/4 rounded bg-gray-200"></div>
            <div className="space-y-2">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="h-4 rounded bg-gray-200"></div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (error || !reportData) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <div className="mb-2 text-red-600">Error loading report</div>
          <p className="text-gray-600">{error || 'Unknown error occurred'}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Report Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Budget Summary Report</h2>
          <p className="text-gray-600">
            {parameters.dateRange?.start && parameters.dateRange?.end
              ? `${parameters.dateRange.start} - ${parameters.dateRange.end}`
              : 'All time'}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={onShare}>
            <Share2 className="mr-2 h-4 w-4" />
            Share
          </Button>
          <Button variant="outline" size="sm" onClick={() => onExport?.('pdf')}>
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Budgets</p>
                <p className="text-2xl font-bold">{reportData.overview.totalBudgets}</p>
              </div>
              <div className="rounded-lg bg-blue-100 p-2">
                <DollarSign className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Allocated</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(reportData.overview.totalAllocated, reportData.overview.currency)}
                </p>
              </div>
              <div className="rounded-lg bg-green-100 p-2">
                <BarChart3 className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Spent</p>
                <p className="text-2xl font-bold">
                  {formatCurrency(reportData.overview.totalSpent, reportData.overview.currency)}
                </p>
              </div>
              <div className="rounded-lg bg-orange-100 p-2">
                <PieChart className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Utilization Rate</p>
                <p
                  className={`text-2xl font-bold ${getUtilizationColor(reportData.overview.utilizationRate)}`}
                >
                  {formatPercentage(reportData.overview.utilizationRate)}
                </p>
              </div>
              <div className="rounded-lg bg-purple-100 p-2">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <Progress value={reportData.overview.utilizationRate} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Budget Status Breakdown */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Budget Status Breakdown
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportData.byStatus.map((status, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Badge
                      variant={
                        status.status === 'approved'
                          ? 'default'
                          : status.status === 'in_progress'
                            ? 'secondary'
                            : status.status === 'planned'
                              ? 'outline'
                              : 'destructive'
                      }
                    >
                      {status.status}
                    </Badge>
                    <span className="text-sm text-gray-600">{status.count} budgets</span>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold">
                      {formatCurrency(status.totalAmount, reportData.overview.currency)}
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatPercentage(status.percentage)}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Training Type Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {reportData.byTrainingType.map((type, index) => (
                <div key={index} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{type.trainingType}</span>
                    <span className="text-sm text-gray-600">{type.budgetCount} budgets</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <Progress value={type.utilizationRate} className="h-2" />
                    </div>
                    <span
                      className={`ml-3 text-sm font-medium ${getUtilizationColor(type.utilizationRate)}`}
                    >
                      {formatPercentage(type.utilizationRate)}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-xs text-gray-600">
                    <span>
                      {formatCurrency(type.totalAllocated, reportData.overview.currency)} allocated
                    </span>
                    <span>
                      {formatCurrency(type.totalSpent, reportData.overview.currency)} spent
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Top Budgets */}
      <Card>
        <CardHeader>
          <CardTitle>Top Budgets by Allocation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reportData.topBudgets.map((budget, index) => (
              <div
                key={budget.id}
                className="flex items-center justify-between rounded-lg border p-4"
              >
                <div className="flex items-center gap-3">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-sm font-medium text-blue-600">
                    {index + 1}
                  </div>
                  <div>
                    <h4 className="font-medium">{budget.title}</h4>
                    <div className="mt-1 flex items-center gap-2">
                      <Badge variant="outline">{budget.status}</Badge>
                      <span className={`text-sm ${getUtilizationColor(budget.utilizationRate)}`}>
                        {formatPercentage(budget.utilizationRate)}
                      </span>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold">
                    {formatCurrency(budget.allocated, reportData.overview.currency)}
                  </div>
                  <div className="text-sm text-gray-600">
                    {formatCurrency(budget.spent, reportData.overview.currency)} spent
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Monthly Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            Monthly Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reportData.monthlyTrends.map((trend, index) => (
              <div key={index} className="flex items-center justify-between rounded-lg border p-4">
                <div>
                  <h4 className="font-medium">{trend.month}</h4>
                  <div className="mt-2 flex items-center gap-4 text-sm text-gray-600">
                    <span>
                      {formatCurrency(trend.allocated, reportData.overview.currency)} allocated
                    </span>
                    <span>{formatCurrency(trend.spent, reportData.overview.currency)} spent</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className={`font-semibold ${getUtilizationColor(trend.utilizationRate)}`}>
                    {formatPercentage(trend.utilizationRate)}
                  </div>
                  <Progress value={trend.utilizationRate} className="mt-2 w-24" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
