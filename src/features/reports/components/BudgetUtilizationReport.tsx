import React, { useState, useEffect } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Badge } from '@/shared/components/ui/badge'
import { Button } from '@/shared/components/ui/button'
import { Progress } from '@/shared/components/ui/progress'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/shared/components/ui/tabs'
import { Alert, AlertDescription, AlertTitle } from '@/shared/components/ui/alert'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/shared/components/ui/table'
import {
  Download,
  TrendingUp,
  TrendingDown,
  Minus,
  AlertTriangle,
  CheckCircle,
  XCircle,
} from 'lucide-react'
import { budgetReportService } from '../services'
import type { BudgetUtilizationReportData } from '../types'

interface BudgetUtilizationReportProps {
  parameters: {
    dateRange: {
      start: Date
      end: Date
    }
    departments?: string[]
    varianceType?: 'absolute' | 'percentage' | 'both'
  }
  className?: string
}

export const BudgetUtilizationReport: React.FC<BudgetUtilizationReportProps> = ({
  parameters,
  className = '',
}) => {
  const [reportData, setReportData] = useState<BudgetUtilizationReportData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchReportData = async () => {
      try {
        setLoading(true)
        setError(null)
        const data = await budgetReportService.generateBudgetUtilizationReport({
          reportType: 'budget_utilization',
          dateRange: {
            start: parameters.dateRange.start.toISOString(),
            end: parameters.dateRange.end.toISOString(),
          },
          departments: parameters.departments,
          includeRecommendations: true,
          currency: 'USD',
        })
        setReportData(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to generate report')
      } finally {
        setLoading(false)
      }
    }

    fetchReportData()
  }, [parameters])

  const handleExport = async (format: 'pdf' | 'excel' | 'csv') => {
    if (!reportData) return

    try {
      const blob = await budgetReportService.exportBudgetReport(reportData, {
        format,
        includeCharts: true,
        includeRawData: format === 'excel',
        includeRecommendations: true,
      })

      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `budget-utilization.${format === 'csv' ? 'csv' : format === 'excel' ? 'xlsx' : 'pdf'}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      console.error('Export failed:', err)
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down':
        return <TrendingDown className="h-4 w-4 text-red-600" />
      default:
        return <Minus className="h-4 w-4 text-gray-600" />
    }
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'under_utilized':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'over_utilized':
        return <XCircle className="h-4 w-4 text-red-600" />
      default:
        return <CheckCircle className="h-4 w-4 text-green-600" />
    }
  }

  const getUtilizationColor = (rate: number) => {
    if (rate < 50) return 'text-yellow-600'
    if (rate > 100) return 'text-red-600'
    return 'text-green-600'
  }

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse">
          <div className="mb-4 h-8 w-1/3 rounded bg-gray-200"></div>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 rounded bg-gray-200"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!reportData) {
    return (
      <div className="py-8 text-center">
        <p>No data available for the selected parameters.</p>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Budget Utilization Report</h2>
          <p className="text-gray-600">
            {parameters.dateRange.start.toLocaleDateString()} -{' '}
            {parameters.dateRange.end.toLocaleDateString()}
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm" onClick={() => handleExport('pdf')}>
            <Download className="mr-2 h-4 w-4" />
            PDF
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExport('excel')}>
            <Download className="mr-2 h-4 w-4" />
            Excel
          </Button>
          <Button variant="outline" size="sm" onClick={() => handleExport('csv')}>
            <Download className="mr-2 h-4 w-4" />
            CSV
          </Button>
        </div>
      </div>

      {/* Overall Utilization */}
      <Card>
        <CardHeader>
          <CardTitle>Overall Budget Utilization</CardTitle>
          <CardDescription>Summary of budget allocation and spending</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <div className="text-center">
              <p className="text-sm text-gray-600">Total Allocated</p>
              <p className="text-2xl font-bold">
                ${reportData.overallUtilization.totalAllocated.toLocaleString()}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Total Spent</p>
              <p className="text-2xl font-bold">
                ${reportData.overallUtilization.totalSpent.toLocaleString()}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Utilization Rate</p>
              <p
                className={`text-2xl font-bold ${getUtilizationColor(reportData.overallUtilization.utilizationRate)}`}
              >
                {reportData.overallUtilization.utilizationRate.toFixed(1)}%
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Remaining</p>
              <p className="text-2xl font-bold">
                ${reportData.overallUtilization.remaining.toLocaleString()}
              </p>
            </div>
          </div>
          <div className="mt-6">
            <div className="mb-2 flex justify-between text-sm">
              <span>Utilization Progress</span>
              <span>{reportData.overallUtilization.utilizationRate.toFixed(1)}%</span>
            </div>
            <Progress
              value={Math.min(reportData.overallUtilization.utilizationRate, 100)}
              className="w-full"
            />
          </div>
        </CardContent>
      </Card>

      <Tabs defaultValue="byCategory" className="space-y-4">
        <TabsList>
          <TabsTrigger value="byCategory">By Category</TabsTrigger>
          <TabsTrigger value="byBudget">By Budget</TabsTrigger>
          <TabsTrigger value="ranges">Utilization Ranges</TabsTrigger>
          <TabsTrigger value="alerts">Alerts</TabsTrigger>
        </TabsList>

        {/* By Category */}
        <TabsContent value="byCategory">
          <Card>
            <CardHeader>
              <CardTitle>Utilization by Category</CardTitle>
              <CardDescription>Budget utilization breakdown by spending category</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Category</TableHead>
                    <TableHead>Allocated</TableHead>
                    <TableHead>Spent</TableHead>
                    <TableHead>Utilization</TableHead>
                    <TableHead>Variance</TableHead>
                    <TableHead>Trend</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reportData.byCategory.map((category, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{category.category}</TableCell>
                      <TableCell>${category.allocated.toLocaleString()}</TableCell>
                      <TableCell>${category.spent.toLocaleString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className={getUtilizationColor(category.utilizationRate)}>
                            {category.utilizationRate.toFixed(1)}%
                          </span>
                          <Progress
                            value={Math.min(category.utilizationRate, 100)}
                            className="w-16"
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={category.variance < 0 ? 'text-green-600' : 'text-red-600'}>
                          {category.variance > 0 ? '+' : ''}
                          {category.variance.toFixed(1)}%
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          {getTrendIcon(category.trend)}
                          <span className="capitalize">{category.trend}</span>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* By Budget */}
        <TabsContent value="byBudget">
          <Card>
            <CardHeader>
              <CardTitle>Utilization by Budget</CardTitle>
              <CardDescription>Individual budget utilization details</CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Budget Title</TableHead>
                    <TableHead>Allocated</TableHead>
                    <TableHead>Spent</TableHead>
                    <TableHead>Utilization</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Remaining Days</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reportData.byBudget.map((budget, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">{budget.title}</TableCell>
                      <TableCell>${budget.allocated.toLocaleString()}</TableCell>
                      <TableCell>${budget.spent.toLocaleString()}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className={getUtilizationColor(budget.utilizationRate)}>
                            {budget.utilizationRate.toFixed(1)}%
                          </span>
                          <Progress
                            value={Math.min(budget.utilizationRate, 100)}
                            className="w-16"
                          />
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            budget.status === 'approved'
                              ? 'default'
                              : budget.status === 'in_progress'
                                ? 'secondary'
                                : 'outline'
                          }
                        >
                          {budget.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{budget.remainingDays}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Utilization Ranges */}
        <TabsContent value="ranges">
          <Card>
            <CardHeader>
              <CardTitle>Utilization Ranges</CardTitle>
              <CardDescription>Distribution of budgets by utilization percentage</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                {reportData.utilizationRanges.map((range, index) => (
                  <Card key={index} className="border-l-4 border-l-blue-500">
                    <CardContent className="pt-6">
                      <div className="mb-2 flex items-center justify-between">
                        <h3 className="font-semibold">{range.range}</h3>
                        <Badge variant="outline">{range.count} budgets</Badge>
                      </div>
                      <p className="mb-2 text-2xl font-bold">{range.percentage.toFixed(1)}%</p>
                      <p className="text-sm text-gray-600">
                        ${range.totalValue.toLocaleString()} total value
                      </p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Alerts */}
        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle>Utilization Alerts</CardTitle>
              <CardDescription>Budgets requiring attention</CardDescription>
            </CardHeader>
            <CardContent>
              {reportData.alerts.length === 0 ? (
                <div className="py-8 text-center">
                  <CheckCircle className="mx-auto mb-4 h-12 w-12 text-green-600" />
                  <p className="text-gray-600">No utilization alerts at this time.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {reportData.alerts.map((alert, index) => (
                    <Alert
                      key={index}
                      variant={alert.type === 'over_utilized' ? 'destructive' : 'default'}
                    >
                      {getAlertIcon(alert.type)}
                      <AlertTitle className="capitalize">{alert.type.replace('_', ' ')}</AlertTitle>
                      <AlertDescription>
                        <div className="space-y-2">
                          <p>
                            <strong>{alert.budgetTitle}</strong>
                          </p>
                          <p>{alert.message}</p>
                          <p>
                            Current utilization: <strong>{alert.currentValue.toFixed(1)}%</strong>
                            (threshold: {alert.threshold}%)
                          </p>
                        </div>
                      </AlertDescription>
                    </Alert>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
