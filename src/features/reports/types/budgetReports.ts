import { z } from 'zod'
import type { ReportChart } from './index'

// Budget-specific report types
export const BudgetReportTypeSchema = z.enum([
  'budget_summary',
  'budget_utilization',
  'budget_variance',
  'training_roi',
  'budget_approval_status',
  'monthly_budget',
  'quarterly_budget',
  'annual_budget',
  'budget_performance',
  'spending_trends',
  'cost_optimization',
])

export const BudgetReportPeriodSchema = z.enum(['monthly', 'quarterly', 'annual', 'custom'])

export const BudgetVarianceTypeSchema = z.enum(['absolute', 'percentage', 'both'])

// Budget summary report data
export const BudgetSummaryReportDataSchema = z.object({
  overview: z.object({
    totalBudgets: z.number(),
    activeBudgets: z.number(),
    totalAllocated: z.number(),
    totalSpent: z.number(),
    remainingBudget: z.number(),
    utilizationRate: z.number(),
    averageBudgetSize: z.number(),
    currency: z.string(),
  }),
  byStatus: z.array(
    z.object({
      status: z.string(),
      count: z.number(),
      totalAmount: z.number(),
      percentage: z.number(),
    })
  ),
  byTrainingType: z.array(
    z.object({
      trainingType: z.string(),
      budgetCount: z.number(),
      totalAllocated: z.number(),
      totalSpent: z.number(),
      utilizationRate: z.number(),
    })
  ),
  byDepartment: z.array(
    z.object({
      department: z.string(),
      budgetCount: z.number(),
      totalAllocated: z.number(),
      totalSpent: z.number(),
      utilizationRate: z.number(),
    })
  ),
  topBudgets: z.array(
    z.object({
      id: z.string(),
      title: z.string(),
      allocated: z.number(),
      spent: z.number(),
      utilizationRate: z.number(),
      status: z.string(),
    })
  ),
  monthlyTrends: z.array(
    z.object({
      month: z.string(),
      allocated: z.number(),
      spent: z.number(),
      utilizationRate: z.number(),
    })
  ),
})

// Budget utilization report data
export const BudgetUtilizationReportDataSchema = z.object({
  overallUtilization: z.object({
    totalAllocated: z.number(),
    totalSpent: z.number(),
    utilizationRate: z.number(),
    remaining: z.number(),
    currency: z.string(),
  }),
  byCategory: z.array(
    z.object({
      category: z.string(),
      allocated: z.number(),
      spent: z.number(),
      utilizationRate: z.number(),
      variance: z.number(),
      trend: z.enum(['up', 'down', 'stable']),
    })
  ),
  byBudget: z.array(
    z.object({
      id: z.string(),
      title: z.string(),
      allocated: z.number(),
      spent: z.number(),
      utilizationRate: z.number(),
      status: z.string(),
      remainingDays: z.number().optional(),
      projectedSpend: z.number().optional(),
    })
  ),
  utilizationRanges: z.array(
    z.object({
      range: z.string(),
      count: z.number(),
      percentage: z.number(),
      totalValue: z.number(),
    })
  ),
  alerts: z.array(
    z.object({
      type: z.enum(['under_utilized', 'over_utilized', 'at_risk']),
      budgetId: z.string(),
      budgetTitle: z.string(),
      currentValue: z.number(),
      threshold: z.number(),
      message: z.string(),
    })
  ),
})

// Budget variance analysis data
export const BudgetVarianceReportDataSchema = z.object({
  overallVariance: z.object({
    totalVariance: z.number(),
    variancePercentage: z.number(),
    favorableVariance: z.number(),
    unfavorableVariance: z.number(),
    currency: z.string(),
  }),
  byCategory: z.array(
    z.object({
      category: z.string(),
      budgeted: z.number(),
      actual: z.number(),
      variance: z.number(),
      variancePercentage: z.number(),
      trend: z.enum(['improving', 'declining', 'stable']),
    })
  ),
  significantVariances: z.array(
    z.object({
      budgetId: z.string(),
      budgetTitle: z.string(),
      category: z.string(),
      budgeted: z.number(),
      actual: z.number(),
      variance: z.number(),
      variancePercentage: z.number(),
      impact: z.enum(['low', 'medium', 'high']),
      explanation: z.string().optional(),
    })
  ),
  varianceTrends: z.array(
    z.object({
      period: z.string(),
      budgeted: z.number(),
      actual: z.number(),
      variance: z.number(),
      variancePercentage: z.number(),
    })
  ),
  recommendations: z.array(
    z.object({
      type: z.enum(['cost_control', 'reallocation', 'process_improvement']),
      priority: z.enum(['low', 'medium', 'high']),
      description: z.string(),
      potentialSavings: z.number().optional(),
    })
  ),
})

// Training ROI report data
export const TrainingROIReportDataSchema = z.object({
  overallROI: z.object({
    totalInvestment: z.number(),
    totalBenefits: z.number(),
    roiPercentage: z.number(),
    paybackPeriod: z.number(),
    currency: z.string(),
  }),
  byProgram: z.array(
    z.object({
      programId: z.string(),
      programName: z.string(),
      investment: z.number(),
      benefits: z.number(),
      roiPercentage: z.number(),
      participantCount: z.number(),
      completionRate: z.number(),
    })
  ),
  byTrainingType: z.array(
    z.object({
      trainingType: z.string(),
      investment: z.number(),
      benefits: z.number(),
      roiPercentage: z.number(),
      programCount: z.number(),
    })
  ),
  benefitCategories: z.array(
    z.object({
      category: z.string(),
      value: z.number(),
      percentage: z.number(),
      description: z.string(),
    })
  ),
  costBreakdown: z.array(
    z.object({
      category: z.string(),
      amount: z.number(),
      percentage: z.number(),
    })
  ),
  topPerformingPrograms: z.array(
    z.object({
      programId: z.string(),
      programName: z.string(),
      roiPercentage: z.number(),
      investment: z.number(),
      benefits: z.number(),
      keyMetrics: z.record(z.string(), z.number()),
    })
  ),
  trends: z.array(
    z.object({
      period: z.string(),
      investment: z.number(),
      benefits: z.number(),
      roiPercentage: z.number(),
    })
  ),
})

// Budget approval status report data
export const BudgetApprovalStatusReportDataSchema = z.object({
  approvalOverview: z.object({
    totalRequests: z.number(),
    pendingApproval: z.number(),
    approved: z.number(),
    rejected: z.number(),
    averageApprovalTime: z.number(),
    totalValue: z.number(),
    currency: z.string(),
  }),
  byStatus: z.array(
    z.object({
      status: z.string(),
      count: z.number(),
      totalValue: z.number(),
      averageValue: z.number(),
      percentage: z.number(),
    })
  ),
  byLevel: z.array(
    z.object({
      level: z.string(),
      pendingCount: z.number(),
      approvedCount: z.number(),
      rejectedCount: z.number(),
      averageTime: z.number(),
      totalValue: z.number(),
    })
  ),
  byDepartment: z.array(
    z.object({
      department: z.string(),
      requestCount: z.number(),
      totalValue: z.number(),
      approvalRate: z.number(),
      averageApprovalTime: z.number(),
    })
  ),
  agingAnalysis: z.array(
    z.object({
      ageRange: z.string(),
      count: z.number(),
      totalValue: z.number(),
      percentage: z.number(),
    })
  ),
  bottlenecks: z.array(
    z.object({
      level: z.string(),
      averageTime: z.number(),
      pendingCount: z.number(),
      impact: z.enum(['low', 'medium', 'high']),
    })
  ),
  trends: z.array(
    z.object({
      period: z.string(),
      submitted: z.number(),
      approved: z.number(),
      rejected: z.number(),
      averageTime: z.number(),
    })
  ),
})

// Budget performance KPIs
export const BudgetPerformanceKPIsSchema = z.object({
  efficiency: z.object({
    budgetUtilizationRate: z.number(),
    costPerTrainee: z.number(),
    varianceRate: z.number(),
    approvalCycleTime: z.number(),
  }),
  effectiveness: z.object({
    roiPercentage: z.number(),
    completionRate: z.number(),
    satisfactionScore: z.number(),
    skillImprovementScore: z.number(),
  }),
  compliance: z.object({
    policyComplianceRate: z.number(),
    auditFindings: z.number(),
    budgetAdherenceRate: z.number(),
    documentationCompleteness: z.number(),
  }),
  trends: z.array(
    z.object({
      period: z.string(),
      efficiency: z.number(),
      effectiveness: z.number(),
      compliance: z.number(),
    })
  ),
})

// Report generation parameters for budget reports
export const BudgetReportParametersSchema = z.object({
  dateRange: z
    .object({
      start: z.string(),
      end: z.string(),
    })
    .optional(),
  filters: z.record(z.string(), z.any()).optional(),
  groupBy: z.array(z.string()).optional(),
  sortBy: z
    .object({
      field: z.string(),
      direction: z.enum(['asc', 'desc']),
    })
    .optional(),
  includeCharts: z.boolean().optional(),
  format: z.enum(['pdf', 'excel', 'csv']).optional(),
  reportType: BudgetReportTypeSchema,
  period: BudgetReportPeriodSchema.optional(),
  departments: z.array(z.string()).optional(),
  trainingTypes: z.array(z.string()).optional(),
  budgetStatuses: z.array(z.string()).optional(),
  varianceType: BudgetVarianceTypeSchema.optional(),
  includeRecommendations: z.boolean().default(true),
  comparisonPeriod: z
    .object({
      start: z.string(),
      end: z.string(),
    })
    .optional(),
  currency: z.string().default('USD'),
})

// Budget report template
export const BudgetReportTemplateSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  type: BudgetReportTypeSchema,
  category: z.enum(['summary', 'detailed', 'analytical', 'compliance']),
  parameters: z.array(
    z.object({
      name: z.string(),
      type: z.string(),
      required: z.boolean(),
      label: z.string(),
      options: z.array(z.string()).optional(),
      defaultValue: z.any().optional(),
    })
  ),
  charts: z
    .array(
      z.object({
        id: z.string(),
        type: z.string(),
        title: z.string(),
        dataSource: z.string(),
        required: z.boolean().default(true),
      })
    )
    .optional(),
  sections: z
    .array(
      z.object({
        id: z.string(),
        title: z.string(),
        type: z.string(),
        required: z.boolean().default(true),
      })
    )
    .optional(),
  isActive: z.boolean().default(true),
  createdAt: z.string(),
  updatedAt: z.string(),
  createdBy: z.string(),
})

// Export configurations
export const BudgetExportConfigSchema = z.object({
  format: z.enum(['pdf', 'excel', 'csv', 'json', 'powerpoint']),
  includeCharts: z.boolean().default(true),
  includeRawData: z.boolean().default(false),
  includeRecommendations: z.boolean().default(true),
  template: z.string().optional(),
  customBranding: z
    .object({
      logo: z.string().optional(),
      colors: z.array(z.string()).optional(),
      fonts: z.array(z.string()).optional(),
    })
    .optional(),
  watermark: z.string().optional(),
  password: z.string().optional(),
})

// Report scheduling
export const BudgetReportScheduleSchema = z.object({
  id: z.string(),
  name: z.string(),
  templateId: z.string(),
  parameters: BudgetReportParametersSchema,
  frequency: z.enum(['daily', 'weekly', 'monthly', 'quarterly', 'yearly']),
  recipients: z.array(
    z.object({
      email: z.string(),
      name: z.string(),
      role: z.string(),
    })
  ),
  exportConfig: BudgetExportConfigSchema,
  isActive: z.boolean().default(true),
  nextRun: z.string(),
  lastRun: z.string().optional(),
  createdBy: z.string(),
  createdAt: z.string(),
  updatedAt: z.string(),
})

// Type exports
export type BudgetReportType = z.infer<typeof BudgetReportTypeSchema>
export type BudgetReportPeriod = z.infer<typeof BudgetReportPeriodSchema>
export type BudgetVarianceType = z.infer<typeof BudgetVarianceTypeSchema>
export type BudgetSummaryReportData = z.infer<typeof BudgetSummaryReportDataSchema>
export type BudgetUtilizationReportData = z.infer<typeof BudgetUtilizationReportDataSchema>
export type BudgetVarianceReportData = z.infer<typeof BudgetVarianceReportDataSchema>
export type TrainingROIReportData = z.infer<typeof TrainingROIReportDataSchema>
export type BudgetApprovalStatusReportData = z.infer<typeof BudgetApprovalStatusReportDataSchema>
export type BudgetPerformanceKPIs = z.infer<typeof BudgetPerformanceKPIsSchema>
export type BudgetReportParameters = z.infer<typeof BudgetReportParametersSchema>
export type BudgetReportTemplate = z.infer<typeof BudgetReportTemplateSchema>
export type BudgetExportConfig = z.infer<typeof BudgetExportConfigSchema>
export type BudgetReportSchedule = z.infer<typeof BudgetReportScheduleSchema>

// Chart configurations for budget reports
export interface BudgetReportChartConfig {
  id: string
  type: 'bar' | 'line' | 'pie' | 'doughnut' | 'area' | 'scatter' | 'heatmap'
  title: string
  dataSource: string
  xAxis?: string
  yAxis?: string
  groupBy?: string
  aggregation?: 'sum' | 'average' | 'count' | 'max' | 'min'
  filters?: Record<string, any>
  options?: Record<string, any>
}

// Budget report metrics
export interface BudgetReportMetrics {
  totalBudgets: number
  totalAllocated: number
  totalSpent: number
  utilizationRate: number
  varianceRate: number
  roiPercentage: number
  approvalRate: number
  averageApprovalTime: number
  costPerTrainee: number
}

// Budget report insights
export interface BudgetReportInsight {
  type: 'trend' | 'anomaly' | 'opportunity' | 'risk'
  title: string
  description: string
  impact: 'low' | 'medium' | 'high'
  confidence: number
  recommendations: string[]
  data: Record<string, any>
}
