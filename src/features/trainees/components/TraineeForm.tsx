import { useState, useCallback } from 'react'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Label } from '@/shared/components/ui/label'
import { useTrainees } from '../hooks/useTrainees'
import { useTrainingPrograms } from '../../training-programs/hooks/useTrainingPrograms'
import { Save, X } from 'lucide-react'
import type { Trainee, CreateTraineeForm } from '@/shared/types'
import {
  required,
  validateForm,
  createInitialFormState,
  updateFormValues,
  updateFormErrors,
  setFieldTouched,
  getError,
} from '@/lib/formUtils'

interface TraineeFormProps {
  trainee?: Trainee
  onSave: (trainee: Trainee) => void
  onCancel: () => void
}

// Define the form data type
interface TraineeFormData {
  userId: string
  college: string
  enrollmentDate: string
  trainingProgramId: string
  directManagerId: string
  mentorId: string
  managerId: string
  ldOfficerId: string
}

export function TraineeForm({ trainee, onSave, onCancel }: TraineeFormProps) {
  const { createTrainee, updateTrainee } = useTrainees()
  const { trainingPrograms } = useTrainingPrograms()

  // Initialize form state with formUtils
  const initialFormData: TraineeFormData = {
    userId: trainee?.userId || '',
    college: trainee?.college || '',
    enrollmentDate: trainee?.enrollmentDate?.split('T')[0] || '',
    trainingProgramId: trainee?.trainingProgramId || '',
    directManagerId: trainee?.supportTeam?.directManagerId || '',
    mentorId: trainee?.supportTeam?.mentorId || '',
    managerId: trainee?.supportTeam?.managerId || '',
    ldOfficerId: trainee?.supportTeam?.ldOfficerId || '',
  }

  const [formState, setFormState] = useState(() => createInitialFormState(initialFormData))
  const [loading, setLoading] = useState(false)

  // Define validation rules using the new utilities
  const validationRules = {
    userId: [required],
    college: [required],
    enrollmentDate: [required],
    trainingProgramId: [required],
    directManagerId: [required],
    mentorId: [required],
    managerId: [required],
    ldOfficerId: [required],
  }

  // Handle input changes with validation
  const handleInputChange = useCallback((field: keyof TraineeFormData, value: string) => {
    setFormState((prev) => {
      const updatedState = updateFormValues(prev, { [field]: value })
      // Clear error when user starts typing
      if (updatedState.errors[field]) {
        return updateFormErrors(updatedState, { [field]: undefined })
      }
      return updatedState
    })
  }, [])

  // Handle field blur (touched state)
  const handleFieldBlur = useCallback((field: keyof TraineeFormData) => {
    setFormState((prev) => setFieldTouched(prev, field))
  }, [])

  // Validate form using the new utilities
  const validateFormFields = useCallback(() => {
    const result = validateForm(formState.values, validationRules)
    setFormState((prev) => updateFormErrors(prev, result.errors))
    return result.isValid
  }, [formState.values, validationRules])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Set all fields as touched and validate
    const isValid = validateFormFields()
    if (!isValid) {
      return
    }

    setLoading(true)
    try {
      let savedTrainee: Trainee
      if (trainee) {
        // Update existing trainee
        savedTrainee = await updateTrainee(trainee.id, {
          college: formState.values.college,
          enrollmentDate: formState.values.enrollmentDate,
          trainingProgramId: formState.values.trainingProgramId,
        } as any)
      } else {
        // Create new trainee
        const createData: CreateTraineeForm = {
          userId: formState.values.userId,
          college: formState.values.college,
          enrollmentDate: formState.values.enrollmentDate,
          trainingProgramId: formState.values.trainingProgramId,
          directManagerId: formState.values.directManagerId,
          mentorId: formState.values.mentorId,
          managerId: formState.values.managerId,
          ldOfficerId: formState.values.ldOfficerId,
        }
        savedTrainee = await createTrainee(createData)
      }
      onSave(savedTrainee)
    } catch (error) {
      console.error('Error saving trainee:', error)
      // Set form-level error
      setFormState((prev) =>
        updateFormErrors(prev, {
          form: 'An error occurred while saving. Please try again.',
        })
      )
    } finally {
      setLoading(false)
    }
  }

  // Mock user data for dropdowns - in a real app, this would come from an API
  const trainees = [
    { id: 'trainee-1', name: 'John Trainee', email: '<EMAIL>' },
    { id: 'trainee-2', name: 'Jane Trainee', email: '<EMAIL>' },
  ]
  const directManagers = [
    { id: 'dm-1', name: 'Alice Manager', email: '<EMAIL>' },
    { id: 'dm-2', name: 'Bob Manager', email: '<EMAIL>' },
  ]
  const mentors = [
    { id: 'mentor-1', name: 'Carol Mentor', email: '<EMAIL>' },
    { id: 'mentor-2', name: 'Dave Mentor', email: '<EMAIL>' },
  ]
  const managers = [
    { id: 'manager-1', name: 'Eve Senior', email: '<EMAIL>' },
    { id: 'manager-2', name: 'Frank Senior', email: '<EMAIL>' },
  ]
  const ldOfficers = [
    { id: 'ld-1', name: 'Grace LD', email: '<EMAIL>' },
    { id: 'ld-2', name: 'Henry LD', email: '<EMAIL>' },
  ]

  return (
    <Card>
      <CardHeader>
        <CardTitle>{trainee ? 'Edit Trainee' : 'Create New Trainee'}</CardTitle>
        <CardDescription>
          {trainee
            ? 'Update trainee information and program assignment'
            : 'Add a new trainee to the system'}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Form-level error message */}
          {formState.errors.form && (
            <div className="text-destructive bg-destructive/10 rounded p-2 text-sm">
              {formState.errors.form}
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="userId">User</Label>
            <select
              id="userId"
              value={formState.values.userId}
              onChange={(e) => handleInputChange('userId', e.target.value)}
              onBlur={() => handleFieldBlur('userId')}
              className="border-input ring-offset-background focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
              disabled={!!trainee}
            >
              <option value="">Select a user</option>
              {trainees.map((user) => (
                <option key={user.id} value={user.id}>
                  {user.name} ({user.email})
                </option>
              ))}
            </select>
            {getError(formState, 'userId') && (
              <p className="text-destructive text-sm">{getError(formState, 'userId')}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="college">College</Label>
            <Input
              id="college"
              type="text"
              value={formState.values.college}
              onChange={(e) => handleInputChange('college', e.target.value)}
              onBlur={() => handleFieldBlur('college')}
              placeholder="Enter college name"
              className={getError(formState, 'college') ? 'border-destructive' : ''}
            />
            {getError(formState, 'college') && (
              <p className="text-destructive text-sm">{getError(formState, 'college')}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="enrollmentDate">Enrollment Date</Label>
            <Input
              id="enrollmentDate"
              type="date"
              value={formState.values.enrollmentDate}
              onChange={(e) => handleInputChange('enrollmentDate', e.target.value)}
              onBlur={() => handleFieldBlur('enrollmentDate')}
              className={getError(formState, 'enrollmentDate') ? 'border-destructive' : ''}
            />
            {getError(formState, 'enrollmentDate') && (
              <p className="text-destructive text-sm">{getError(formState, 'enrollmentDate')}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="trainingProgramId">Training Program</Label>
            <select
              id="trainingProgramId"
              value={formState.values.trainingProgramId}
              onChange={(e) => handleInputChange('trainingProgramId', e.target.value)}
              onBlur={() => handleFieldBlur('trainingProgramId')}
              className="border-input ring-offset-background focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
            >
              <option value="">Select a training program</option>
              {trainingPrograms.map((program) => (
                <option key={program.id} value={program.id}>
                  {program.name} ({program.durationMonths} months)
                </option>
              ))}
            </select>
            {getError(formState, 'trainingProgramId') && (
              <p className="text-destructive text-sm">{getError(formState, 'trainingProgramId')}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="directManagerId">Direct Manager</Label>
              <select
                id="directManagerId"
                value={formState.values.directManagerId}
                onChange={(e) => handleInputChange('directManagerId', e.target.value)}
                onBlur={() => handleFieldBlur('directManagerId')}
                className="border-input ring-offset-background focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
              >
                <option value="">Select direct manager</option>
                {directManagers.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.name}
                  </option>
                ))}
              </select>
              {getError(formState, 'directManagerId') && (
                <p className="text-destructive text-sm">{getError(formState, 'directManagerId')}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="mentorId">Mentor</Label>
              <select
                id="mentorId"
                value={formState.values.mentorId}
                onChange={(e) => handleInputChange('mentorId', e.target.value)}
                onBlur={() => handleFieldBlur('mentorId')}
                className="border-input ring-offset-background focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
              >
                <option value="">Select mentor</option>
                {mentors.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.name}
                  </option>
                ))}
              </select>
              {getError(formState, 'mentorId') && (
                <p className="text-destructive text-sm">{getError(formState, 'mentorId')}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="managerId">Manager</Label>
              <select
                id="managerId"
                value={formState.values.managerId}
                onChange={(e) => handleInputChange('managerId', e.target.value)}
                onBlur={() => handleFieldBlur('managerId')}
                className="border-input ring-offset-background focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
              >
                <option value="">Select manager</option>
                {managers.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.name}
                  </option>
                ))}
              </select>
              {getError(formState, 'managerId') && (
                <p className="text-destructive text-sm">{getError(formState, 'managerId')}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="ldOfficerId">L&D Officer</Label>
              <select
                id="ldOfficerId"
                value={formState.values.ldOfficerId}
                onChange={(e) => handleInputChange('ldOfficerId', e.target.value)}
                onBlur={() => handleFieldBlur('ldOfficerId')}
                className="border-input ring-offset-background focus-visible:ring-ring flex h-10 w-full rounded-md border px-3 py-2 text-sm focus-visible:ring-2 focus-visible:ring-offset-2 focus-visible:outline-none"
              >
                <option value="">Select L&D Officer</option>
                {ldOfficers.map((user) => (
                  <option key={user.id} value={user.id}>
                    {user.name}
                  </option>
                ))}
              </select>
              {getError(formState, 'ldOfficerId') && (
                <p className="text-destructive text-sm">{getError(formState, 'ldOfficerId')}</p>
              )}
            </div>
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={onCancel}>
              <X className="mr-2 h-4 w-4" />
              Cancel
            </Button>
            <Button type="submit" disabled={loading}>
              <Save className="mr-2 h-4 w-4" />
              {loading ? 'Saving...' : trainee ? 'Update' : 'Create'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
