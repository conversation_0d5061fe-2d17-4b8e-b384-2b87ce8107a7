import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Badge } from '@/shared/components/ui/badge'

import { Avatar } from '@/shared/components/ui/avatar'
import { useTrainees } from '../hooks/useTrainees'
import { Users, Plus, Edit, Trash2, Calendar, Building, TrendingUp, Eye } from 'lucide-react'
import type { Trainee } from '@/shared/types'
import { formatDate } from '@/lib/utils'

interface TraineeListProps {
  onEdit?: (trainee: Trainee) => void
  onDelete?: (trainee: Trainee) => void
  onCreate?: () => void
  supportTeamMemberId?: string
  trainingProgramId?: string
  searchQuery?: string
  selectedStatus?: string
  viewMode?: 'grid' | 'list'
}

export function TraineeList({
  onEdit,
  onDelete,
  onCreate,
  supportTeamMemberId,
  trainingProgramId,
  searchQuery = '',
  selectedStatus = 'all',
  viewMode = 'grid',
}: TraineeListProps) {
  const { trainees, loading, error } = useTrainees()

  // Enhanced filtering logic
  const getFilteredTrainees = () => {
    let filteredTrainees = trainees || []

    // Apply existing filters
    if (supportTeamMemberId) {
      const supportTeam = (trainee: Trainee) => {
        const team = trainee.supportTeam
        return (
          team?.directManagerId === supportTeamMemberId ||
          team?.mentorId === supportTeamMemberId ||
          team?.managerId === supportTeamMemberId ||
          team?.ldOfficerId === supportTeamMemberId
        )
      }
      filteredTrainees = filteredTrainees.filter(supportTeam)
    }

    if (trainingProgramId) {
      filteredTrainees = filteredTrainees.filter((t) => t.trainingProgramId === trainingProgramId)
    }

    // Apply status filter
    if (selectedStatus !== 'all') {
      filteredTrainees = filteredTrainees.filter((t) => t.status === selectedStatus)
    }

    // Apply search filter
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      filteredTrainees = filteredTrainees.filter(
        (t) =>
          t.userName?.toLowerCase().includes(query) ||
          t.userEmail?.toLowerCase().includes(query) ||
          t.college?.toLowerCase().includes(query) ||
          t.trainingProgram?.name?.toLowerCase().includes(query)
      )
    }

    return filteredTrainees
  }

  const displayTrainees = getFilteredTrainees()
  const canManageTrainees = true // Always allow management since we removed auth

  // Trainee Card Component for Grid View
  const TraineeCard = ({ trainee }: { trainee: Trainee }) => (
    <Card className="cursor-pointer transition-shadow hover:shadow-lg">
      <CardHeader>
        <div className="flex items-center gap-4">
          <Avatar
            className="h-12 w-12"
            alt={trainee.userName}
            fallback={trainee.userName
              ?.split(' ')
              .map((n) => n[0])
              .join('')
              .toUpperCase()}
          />
          <div className="flex-1">
            <CardTitle className="text-lg">{trainee.userName}</CardTitle>
            <CardDescription>{trainee.userEmail}</CardDescription>
          </div>
          <Badge variant={trainee.status === 'active' ? 'default' : 'secondary'}>
            {trainee.status}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Status</span>
            <span>{trainee.status || 'Unknown'}</span>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <p className="text-muted-foreground">Program</p>
            <p className="font-medium">{trainee.trainingProgram?.name}</p>
          </div>
          <div>
            <p className="text-muted-foreground">College</p>
            <p className="font-medium">{trainee.college}</p>
          </div>
          <div>
            <p className="text-muted-foreground">Assessments</p>
            <p className="font-medium">0</p>
          </div>
          <div>
            <p className="text-muted-foreground">Status</p>
            <p className="font-medium">{trainee.status || 'Active'}</p>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <Button variant="outline" size="sm">
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </Button>
          <div className="flex gap-2">
            {onEdit && canManageTrainees && (
              <Button variant="outline" size="sm" onClick={() => onEdit(trainee)}>
                <Edit className="h-4 w-4" />
              </Button>
            )}
            {onDelete && canManageTrainees && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => onDelete(trainee)}
                className="text-destructive hover:text-destructive"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )

  // Trainee List Item Component for List View
  const TraineeListItem = ({ trainee }: { trainee: Trainee }) => (
    <div className="hover:bg-muted/50 flex items-center justify-between rounded-lg border p-4 transition-colors">
      <div className="flex items-center space-x-4">
        <Avatar
          className="h-10 w-10"
          alt={trainee.userName}
          fallback={trainee.userName
            ?.split(' ')
            .map((n) => n[0])
            .join('')
            .toUpperCase()}
        />
        <div className="flex-1">
          <div className="flex items-center gap-3">
            <h3 className="font-medium">{trainee.userName}</h3>
            <Badge variant={trainee.status === 'active' ? 'default' : 'secondary'}>
              {trainee.status}
            </Badge>
          </div>
          <p className="text-muted-foreground text-sm">{trainee.userEmail}</p>
          <div className="mt-2 flex items-center gap-6">
            <div className="text-muted-foreground flex items-center space-x-2 text-sm">
              <Building className="h-3 w-3" />
              <span>{trainee.college}</span>
            </div>
            <div className="text-muted-foreground flex items-center space-x-2 text-sm">
              <Calendar className="h-3 w-3" />
              <span>{formatDate(trainee.enrollmentDate)}</span>
            </div>
            <div className="text-muted-foreground flex items-center space-x-2 text-sm">
              <TrendingUp className="h-3 w-3" />
              <span>{trainee.status}</span>
            </div>
          </div>
        </div>
      </div>
      <div className="flex items-center space-x-3">
        <Badge variant="outline">{trainee.trainingProgram?.name}</Badge>
        <div className="flex space-x-2">
          {onEdit && canManageTrainees && (
            <Button variant="outline" size="sm" onClick={() => onEdit(trainee)}>
              <Edit className="h-4 w-4" />
            </Button>
          )}
          {onDelete && canManageTrainees && (
            <Button
              variant="outline"
              size="sm"
              onClick={() => onDelete(trainee)}
              className="text-destructive hover:text-destructive"
            >
              <Trash2 className="h-4 w-4" />
            </Button>
          )}
        </div>
      </div>
    </div>
  )

  if (loading) {
    return (
      <Card>
        <CardContent className="flex h-64 items-center justify-center">
          <div className="text-center">
            <Users className="text-muted-foreground mx-auto mb-4 h-8 w-8 animate-pulse" />
            <p className="text-muted-foreground">Loading trainees...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card>
        <CardContent className="flex h-64 items-center justify-center">
          <div className="text-center">
            <Users className="text-destructive mx-auto mb-4 h-8 w-8" />
            <p className="text-destructive font-medium">Error loading trainees</p>
            <p className="text-muted-foreground text-sm">{error}</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div>
      {displayTrainees.length === 0 ? (
        <div className="py-8 text-center">
          <Users className="text-muted-foreground mx-auto mb-4 h-12 w-12" />
          <p className="text-muted-foreground">No trainees found</p>
          {onCreate && canManageTrainees && (
            <Button className="mt-4" onClick={onCreate}>
              <Plus className="mr-2 h-4 w-4" />
              Create your first trainee
            </Button>
          )}
        </div>
      ) : (
        <>
          {viewMode === 'grid' ? (
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
              {displayTrainees.map((trainee) => (
                <TraineeCard key={trainee.id} trainee={trainee} />
              ))}
            </div>
          ) : (
            <div className="space-y-4">
              {displayTrainees.map((trainee) => (
                <TraineeListItem key={trainee.id} trainee={trainee} />
              ))}
            </div>
          )}
        </>
      )}
    </div>
  )
}
