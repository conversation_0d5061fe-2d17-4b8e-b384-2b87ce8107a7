import { useState } from 'react'
import { TraineeList } from './TraineeList'
import { TraineeForm } from './TraineeForm'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/shared/components/ui/dialog'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/shared/components/ui/card'
import { Button } from '@/shared/components/ui/button'
import { Input } from '@/shared/components/ui/input'
import { Users, Plus, Search, Filter, BookOpen, ClipboardCheck, TrendingUp } from 'lucide-react'
import { useTrainees } from '../hooks/useTrainees'
import type { Trainee } from '@/shared/types'

export function TraineeManagement() {
  const { trainees, deleteTrainee } = useTrainees()
  const [isFormOpen, setIsFormOpen] = useState(false)
  const [editingTrainee, setEditingTrainee] = useState<Trainee | undefined>(undefined)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedStatus, setSelectedStatus] = useState<string>('all')
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')

  const canManageTrainees = true // Always allow management since we removed auth

  // Calculate stats
  const activeTrainees = trainees?.filter((t) => t.status === 'active').length || 0
  const totalTrainees = trainees?.length || 0
  const completedPrograms = 0 // Default to 0 since this property doesn't exist
  const averageProgress = 0 // Default to 0 since this property doesn't exist

  const handleCreateTrainee = () => {
    setEditingTrainee(undefined)
    setIsFormOpen(true)
  }

  const handleEditTrainee = (trainee: Trainee) => {
    setEditingTrainee(trainee)
    setIsFormOpen(true)
  }

  const handleDeleteTrainee = async (trainee: Trainee) => {
    if (confirm(`Are you sure you want to delete ${trainee.userName}?`)) {
      try {
        await deleteTrainee(trainee.id)
      } catch (error) {
        console.error('Error deleting trainee:', error)
      }
    }
  }

  const handleSaveTrainee = () => {
    setIsFormOpen(false)
    setEditingTrainee(undefined)
  }

  const handleCancelForm = () => {
    setIsFormOpen(false)
    setEditingTrainee(undefined)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Trainee Management</h1>
          <p className="text-muted-foreground">
            Manage trainees, track progress, and oversee training programs
          </p>
        </div>
        <div className="flex items-center gap-2">
          {canManageTrainees && (
            <Button onClick={handleCreateTrainee}>
              <Plus className="mr-2 h-4 w-4" />
              Add Trainee
            </Button>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Trainees</CardTitle>
            <Users className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalTrainees}</div>
            <p className="text-muted-foreground text-xs">{activeTrainees} active learners</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg. Progress</CardTitle>
            <TrendingUp className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{averageProgress}%</div>
            <p className="text-muted-foreground text-xs">Across all programs</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Programs Completed</CardTitle>
            <BookOpen className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{completedPrograms}</div>
            <p className="text-muted-foreground text-xs">Successfully finished</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assessments</CardTitle>
            <ClipboardCheck className="text-muted-foreground h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">0</div>
            <p className="text-muted-foreground text-xs">Total assessments taken</p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and View Controls */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Trainees</CardTitle>
              <CardDescription>View and manage trainee progress and enrollment</CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                Grid
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                List
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="mb-6 flex flex-col gap-4 sm:flex-row">
            <div className="flex-1">
              <div className="relative">
                <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
                <Input
                  placeholder="Search trainees..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                Filter
              </Button>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="border-input bg-background rounded-md border px-3 py-2 text-sm"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="completed">Completed</option>
                <option value="suspended">Suspended</option>
              </select>
            </div>
          </div>

          <TraineeList
            onEdit={handleEditTrainee}
            onDelete={handleDeleteTrainee}
            onCreate={handleCreateTrainee}
            searchQuery={searchQuery}
            selectedStatus={selectedStatus}
            viewMode={viewMode}
          />
        </CardContent>
      </Card>

      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          <DialogHeader>
            <DialogTitle>{editingTrainee ? 'Edit Trainee' : 'Create New Trainee'}</DialogTitle>
            <DialogDescription>
              {editingTrainee
                ? 'Edit the trainee information below.'
                : 'Fill in the form to create a new trainee.'}
            </DialogDescription>
          </DialogHeader>
          <TraineeForm
            trainee={editingTrainee}
            onSave={handleSaveTrainee}
            onCancel={handleCancelForm}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}

export default TraineeManagement
