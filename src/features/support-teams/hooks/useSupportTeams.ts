import { useState, useEffect, useCallback } from 'react'
import { SupportTeamService } from '../services'
import type {
  SupportTeam,
  SupportTeamFilters,
  SupportTeamStats,
  CreateSupportTeamRequest,
  UpdateSupportTeamRequest,
  ApiResult,
} from '@/shared/types'

export function useSupportTeams(filters?: SupportTeamFilters) {
  const [supportTeams, setSupportTeams] = useState<SupportTeam[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchSupportTeams = useCallback(async () => {
    setLoading(true)
    setError(null)

    const result = await SupportTeamService.getAll(filters)

    if (result.success) {
      setSupportTeams(result.data)
    } else {
      setError(result.error?.message || 'Failed to fetch support teams')
    }

    setLoading(false)
  }, [filters])

  useEffect(() => {
    fetchSupportTeams()
  }, [fetchSupportTeams])

  return {
    supportTeams,
    loading,
    error,
    refetch: fetchSupportTeams,
  }
}

export function useSupportTeam(id: string) {
  const [supportTeam, setSupportTeam] = useState<SupportTeam | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!id) return

    const fetchSupportTeam = async () => {
      setLoading(true)
      setError(null)

      const result = await SupportTeamService.getById(id)

      if (result.success) {
        setSupportTeam(result.data)
      } else {
        setError(result.error?.message || 'Failed to fetch support team')
      }

      setLoading(false)
    }

    fetchSupportTeam()
  }, [id])

  return {
    supportTeam,
    loading,
    error,
  }
}

export function useSupportTeamStats() {
  const [stats, setStats] = useState<SupportTeamStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchStats = useCallback(async () => {
    setLoading(true)
    setError(null)

    const result = await SupportTeamService.getStats()

    if (result.success) {
      setStats(result.data)
    } else {
      setError(result.error?.message || 'Failed to fetch support team stats')
    }

    setLoading(false)
  }, [])

  useEffect(() => {
    fetchStats()
  }, [fetchStats])

  return {
    stats,
    loading,
    error,
    refetch: fetchStats,
  }
}

export function useSupportTeamActions() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const createSupportTeam = useCallback(
    async (request: CreateSupportTeamRequest): Promise<ApiResult<SupportTeam>> => {
      setLoading(true)
      setError(null)

      const result = await SupportTeamService.create(request)

      if (!result.success) {
        setError(result.error?.message || 'Failed to create support team')
      }

      setLoading(false)
      return result
    },
    []
  )

  const updateSupportTeam = useCallback(
    async (id: string, request: UpdateSupportTeamRequest): Promise<ApiResult<SupportTeam>> => {
      setLoading(true)
      setError(null)

      const result = await SupportTeamService.update(id, request)

      if (!result.success) {
        setError(result.error?.message || 'Failed to update support team')
      }

      setLoading(false)
      return result
    },
    []
  )

  const deleteSupportTeam = useCallback(async (id: string): Promise<ApiResult<void>> => {
    setLoading(true)
    setError(null)

    const result = await SupportTeamService.delete(id)

    if (!result.success) {
      setError(result.error?.message || 'Failed to delete support team')
    }

    setLoading(false)
    return result
  }, [])

  const assignMember = useCallback(
    async (teamId: string, memberId: string): Promise<ApiResult<void>> => {
      setLoading(true)
      setError(null)

      const result = await SupportTeamService.assignMember(teamId, memberId)

      if (!result.success) {
        setError(result.error?.message || 'Failed to assign member')
      }

      setLoading(false)
      return result
    },
    []
  )

  const removeMember = useCallback(
    async (teamId: string, memberId: string): Promise<ApiResult<void>> => {
      setLoading(true)
      setError(null)

      const result = await SupportTeamService.removeMember(teamId, memberId)

      if (!result.success) {
        setError(result.error?.message || 'Failed to remove member')
      }

      setLoading(false)
      return result
    },
    []
  )

  const assignProgram = useCallback(
    async (teamId: string, programId: string): Promise<ApiResult<void>> => {
      setLoading(true)
      setError(null)

      const result = await SupportTeamService.assignProgram(teamId, programId)

      if (!result.success) {
        setError(result.error?.message || 'Failed to assign program')
      }

      setLoading(false)
      return result
    },
    []
  )

  const removeProgram = useCallback(
    async (teamId: string, programId: string): Promise<ApiResult<void>> => {
      setLoading(true)
      setError(null)

      const result = await SupportTeamService.removeProgram(teamId, programId)

      if (!result.success) {
        setError(result.error?.message || 'Failed to remove program')
      }

      setLoading(false)
      return result
    },
    []
  )

  return {
    loading,
    error,
    createSupportTeam,
    updateSupportTeam,
    deleteSupportTeam,
    assignMember,
    removeMember,
    assignProgram,
    removeProgram,
  }
}
