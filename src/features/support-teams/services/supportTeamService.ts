import { invoke } from '@tauri-apps/api/core'
import type {
  SupportTeam,
  CreateSupportTeamRequest,
  UpdateSupportTeamRequest,
  SupportTeamFilters,
  SupportTeamStats,
  ApiResult,
  Paginated,
  Filter,
} from '@/shared/types'

export class SupportTeamService {
  private static readonly ENDPOINT = 'support_teams'

  static async getAll(filters?: SupportTeamFilters): Promise<ApiResult<SupportTeam[]>> {
    try {
      const params = new URLSearchParams()
      if (filters?.search) params.append('search', filters.search)
      if (filters?.hasMembers !== undefined)
        params.append('has_members', filters.hasMembers.toString())
      if (filters?.hasPrograms !== undefined)
        params.append('has_programs', filters.hasPrograms.toString())

      const query = params.toString()
      const url = query ? `${this.ENDPOINT}?${query}` : this.ENDPOINT

      const response = await invoke<SupportTeam[]>('get_support_teams', { url })
      return { success: true, data: response }
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to fetch support teams',
          code: 'FETCH_SUPPORT_TEAMS_ERROR',
        },
      }
    }
  }

  static async getById(id: string): Promise<ApiResult<SupportTeam>> {
    try {
      const response = await invoke<SupportTeam>('get_support_team', { id })
      return { success: true, data: response }
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to fetch support team',
          code: 'FETCH_SUPPORT_TEAM_ERROR',
        },
      }
    }
  }

  static async create(request: CreateSupportTeamRequest): Promise<ApiResult<SupportTeam>> {
    try {
      const response = await invoke<SupportTeam>('create_support_team', { request })
      return { success: true, data: response }
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to create support team',
          code: 'CREATE_SUPPORT_TEAM_ERROR',
        },
      }
    }
  }

  static async update(
    id: string,
    request: UpdateSupportTeamRequest
  ): Promise<ApiResult<SupportTeam>> {
    try {
      const response = await invoke<SupportTeam>('update_support_team', { id, request })
      return { success: true, data: response }
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to update support team',
          code: 'UPDATE_SUPPORT_TEAM_ERROR',
        },
      }
    }
  }

  static async delete(id: string): Promise<ApiResult<void>> {
    try {
      await invoke('delete_support_team', { id })
      return { success: true, data: undefined }
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to delete support team',
          code: 'DELETE_SUPPORT_TEAM_ERROR',
        },
      }
    }
  }

  static async getStats(): Promise<ApiResult<SupportTeamStats>> {
    try {
      const response = await invoke<SupportTeamStats>('get_support_team_stats')
      return { success: true, data: response }
    } catch (error) {
      return {
        success: false,
        error: {
          message: error instanceof Error ? error.message : 'Failed to fetch support team stats',
          code: 'FETCH_SUPPORT_TEAM_STATS_ERROR',
        },
      }
    }
  }

  static async assignMember(teamId: string, memberId: string): Promise<ApiResult<void>> {
    try {
      await invoke('assign_support_team_member', { teamId, memberId })
      return { success: true, data: undefined }
    } catch (error) {
      return {
        success: false,
        error: {
          message:
            error instanceof Error ? error.message : 'Failed to assign member to support team',
          code: 'ASSIGN_MEMBER_ERROR',
        },
      }
    }
  }

  static async removeMember(teamId: string, memberId: string): Promise<ApiResult<void>> {
    try {
      await invoke('remove_support_team_member', { teamId, memberId })
      return { success: true, data: undefined }
    } catch (error) {
      return {
        success: false,
        error: {
          message:
            error instanceof Error ? error.message : 'Failed to remove member from support team',
          code: 'REMOVE_MEMBER_ERROR',
        },
      }
    }
  }

  static async assignProgram(teamId: string, programId: string): Promise<ApiResult<void>> {
    try {
      await invoke('assign_support_team_program', { teamId, programId })
      return { success: true, data: undefined }
    } catch (error) {
      return {
        success: false,
        error: {
          message:
            error instanceof Error ? error.message : 'Failed to assign program to support team',
          code: 'ASSIGN_PROGRAM_ERROR',
        },
      }
    }
  }

  static async removeProgram(teamId: string, programId: string): Promise<ApiResult<void>> {
    try {
      await invoke('remove_support_team_program', { teamId, programId })
      return { success: true, data: undefined }
    } catch (error) {
      return {
        success: false,
        error: {
          message:
            error instanceof Error ? error.message : 'Failed to remove program from support team',
          code: 'REMOVE_PROGRAM_ERROR',
        },
      }
    }
  }
}
