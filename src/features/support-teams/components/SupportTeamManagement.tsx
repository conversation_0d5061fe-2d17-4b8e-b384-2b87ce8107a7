import { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { ArrowLeft, Save, UserPlus, BookOpen } from 'lucide-react'
import { toast } from 'sonner'
import { useSupportTeams, useSupportTeamActions, useSupportTeamStats } from '../hooks'
import { SupportTeamForm } from './SupportTeamForm'
import { SupportTeamList } from './SupportTeamList'
import { SupportTeamStatsCard } from './SupportTeamStats'
import type { SupportTeam } from '@/shared/types'

interface SupportTeamManagementProps {
  onBack?: () => void
}

export function SupportTeamManagement({ onBack }: SupportTeamManagementProps) {
  const [showForm, setShowForm] = useState(false)
  const [editingTeam, setEditingTeam] = useState<SupportTeam | null>(null)
  const [filters, setFilters] = useState({})

  const { supportTeams, loading, error, refetch } = useSupportTeams(filters)
  const { stats, loading: statsLoading } = useSupportTeamStats()
  const {
    createSupportTeam,
    updateSupportTeam,
    deleteSupportTeam,
    loading: actionLoading,
  } = useSupportTeamActions()

  const handleCreateTeam = () => {
    setEditingTeam(null)
    setShowForm(true)
  }

  const handleEditTeam = (team: SupportTeam) => {
    setEditingTeam(team)
    setShowForm(true)
  }

  const handleSubmit = async (data: any) => {
    try {
      if (editingTeam) {
        await updateSupportTeam(editingTeam.id, data)
      } else {
        await createSupportTeam(data)
      }
      setShowForm(false)
      setEditingTeam(null)
      refetch()
    } catch (error) {
      console.error('Failed to save support team:', error)
    }
  }

  const handleDeleteTeam = async (id: string) => {
    try {
      await deleteSupportTeam(id)
      refetch()
    } catch (error) {
      toast.error('Failed to delete support team')
    }
  }

  const handleCancel = () => {
    setShowForm(false)
    setEditingTeam(null)
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <p className="text-destructive mb-4">Error loading support teams: {error}</p>
            <Button onClick={refetch}>Retry</Button>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          {onBack && (
            <Button variant="ghost" size="sm" onClick={onBack}>
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back
            </Button>
          )}
          <div>
            <h1 className="text-2xl font-bold">Support Teams</h1>
            <p className="text-muted-foreground">Manage support teams and their assignments</p>
          </div>
        </div>
      </div>

      <SupportTeamStatsCard stats={stats} loading={statsLoading} />

      <Tabs defaultValue="teams" className="w-full">
        <TabsList>
          <TabsTrigger value="teams">Teams</TabsTrigger>
          <TabsTrigger value="overview">Overview</TabsTrigger>
        </TabsList>

        <TabsContent value="teams" className="space-y-6">
          <SupportTeamList
            supportTeams={supportTeams}
            loading={loading}
            onCreateTeam={handleCreateTeam}
            onEditTeam={handleEditTeam}
            onDeleteTeam={handleDeleteTeam}
            filters={filters}
            onFiltersChange={setFilters}
          />
        </TabsContent>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <Button onClick={handleCreateTeam} className="w-full justify-start">
                  <UserPlus className="mr-2 h-4 w-4" />
                  Create New Team
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <BookOpen className="mr-2 h-4 w-4" />
                  Assign Programs
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Team Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>Total Teams:</span>
                    <span className="font-medium">{stats?.totalTeams || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Active Teams:</span>
                    <span className="font-medium">{stats?.teamsWithMembers || 0}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Total Members:</span>
                    <span className="font-medium">{stats?.totalMembers || 0}</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      <Dialog open={showForm} onOpenChange={setShowForm}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>{editingTeam ? 'Edit Support Team' : 'Create Support Team'}</DialogTitle>
          </DialogHeader>
          <SupportTeamForm
            supportTeam={editingTeam}
            onSubmit={handleSubmit}
            onCancel={handleCancel}
            loading={actionLoading}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
