import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { toast } from 'sonner'
import type { CreateSupportTeamRequest, UpdateSupportTeamRequest } from '@/shared/types'

const supportTeamSchema = z.object({
  name: z.string().min(2, 'Team name must be at least 2 characters'),
  description: z.string().optional(),
})

type SupportTeamFormData = z.infer<typeof supportTeamSchema>

interface SupportTeamFormProps {
  supportTeam?: Partial<UpdateSupportTeamRequest>
  onSubmit: (data: CreateSupportTeamRequest | UpdateSupportTeamRequest) => Promise<void>
  onCancel: () => void
  loading?: boolean
}

export function SupportTeamForm({
  supportTeam,
  onSubmit,
  onCancel,
  loading,
}: SupportTeamFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<SupportTeamFormData>({
    resolver: zodResolver(supportTeamSchema),
    defaultValues: {
      name: supportTeam?.name || '',
      description: supportTeam?.description || '',
    },
  })

  const onFormSubmit = async (data: SupportTeamFormData) => {
    setIsSubmitting(true)
    try {
      await onSubmit(data)
      reset()
      toast.success(
        supportTeam?.name
          ? 'Support team updated successfully'
          : 'Support team created successfully'
      )
    } catch (error) {
      toast.error('Failed to save support team')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="mx-auto w-full max-w-2xl">
      <CardHeader>
        <CardTitle>{supportTeam?.name ? 'Edit Support Team' : 'Create Support Team'}</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">Team Name *</Label>
            <Input
              id="name"
              {...register('name')}
              placeholder="Enter team name"
              disabled={isSubmitting || loading}
            />
            {errors.name && <p className="text-destructive text-sm">{errors.name.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              {...register('description')}
              placeholder="Enter team description (optional)"
              rows={3}
              disabled={isSubmitting || loading}
            />
            {errors.description && (
              <p className="text-destructive text-sm">{errors.description.message}</p>
            )}
          </div>

          <div className="flex justify-end space-x-2">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isSubmitting || loading}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting || loading}>
              {isSubmitting ? 'Saving...' : supportTeam?.name ? 'Update Team' : 'Create Team'}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
