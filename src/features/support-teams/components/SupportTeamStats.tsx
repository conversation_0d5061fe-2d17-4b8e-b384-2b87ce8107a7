import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Users, BookOpen, Target, TrendingUp } from 'lucide-react'
import type { SupportTeamStats } from '@/shared/types'

interface SupportTeamStatsProps {
  stats: SupportTeamStats | null
  loading?: boolean
}

export function SupportTeamStatsCard({ stats, loading }: SupportTeamStatsProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="space-y-2">
              <div className="bg-muted h-4 w-1/2 rounded"></div>
              <div className="bg-muted h-8 w-3/4 rounded"></div>
            </CardHeader>
          </Card>
        ))}
      </div>
    )
  }

  if (!stats) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground text-center">No statistics available</p>
        </CardContent>
      </Card>
    )
  }

  const statCards = [
    {
      title: 'Total Teams',
      value: stats.totalTeams,
      icon: Target,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Teams with Members',
      value: stats.teamsWithMembers,
      icon: Users,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      subtitle:
        stats.totalTeams > 0
          ? `${Math.round((stats.teamsWithMembers / stats.totalTeams) * 100)}% of total`
          : undefined,
    },
    {
      title: 'Teams with Programs',
      value: stats.teamsWithPrograms,
      icon: BookOpen,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      subtitle:
        stats.totalTeams > 0
          ? `${Math.round((stats.teamsWithPrograms / stats.totalTeams) * 100)}% of total`
          : undefined,
    },
    {
      title: 'Total Members',
      value: stats.totalMembers,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
    },
  ]

  return (
    <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((card, index) => {
        const Icon = card.icon
        return (
          <Card key={index}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-muted-foreground text-sm font-medium">
                {card.title}
              </CardTitle>
              <div className={`rounded-md p-2 ${card.bgColor}`}>
                <Icon className={`h-4 w-4 ${card.color}`} />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{card.value}</div>
              {card.subtitle && (
                <p className="text-muted-foreground mt-1 text-xs">{card.subtitle}</p>
              )}
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
