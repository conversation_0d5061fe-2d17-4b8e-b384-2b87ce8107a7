import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { DataTable } from '@/components/ui/DataTable'
import { ConfirmationDialog } from '@/components/ui/ConfirmationDialog'
import { toast } from 'sonner'
import { Search, Plus, Edit, Trash2, Users, BookOpen } from 'lucide-react'
import type { SupportTeam, SupportTeamFilters } from '@/shared/types'

interface SupportTeamListProps {
  supportTeams: SupportTeam[]
  loading?: boolean
  onCreateTeam: () => void
  onEditTeam: (team: SupportTeam) => void
  onDeleteTeam: (id: string) => Promise<void>
  filters?: SupportTeamFilters
  onFiltersChange?: (filters: SupportTeamFilters) => void
}

export function SupportTeamList({
  supportTeams,
  loading,
  onCreateTeam,
  onEditTeam,
  onDeleteTeam,
  filters,
  onFiltersChange,
}: SupportTeamListProps) {
  const [searchTerm, setSearchTerm] = useState(filters?.search || '')
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [teamToDelete, setTeamToDelete] = useState<SupportTeam | null>(null)

  const handleSearch = (value: string) => {
    setSearchTerm(value)
    onFiltersChange?.({ ...filters, search: value })
  }

  const handleDeleteClick = (team: SupportTeam) => {
    setTeamToDelete(team)
    setDeleteDialogOpen(true)
  }

  const handleDeleteConfirm = async () => {
    if (teamToDelete) {
      await onDeleteTeam(teamToDelete.id)
      setDeleteDialogOpen(false)
      setTeamToDelete(null)
      toast.success('Support team deleted successfully')
    }
  }

  const columns = [
    {
      key: 'name',
      label: 'Team Name',
      render: (team: SupportTeam) => <div className="font-medium">{team.name}</div>,
    },
    {
      key: 'description',
      label: 'Description',
      render: (team: SupportTeam) => (
        <div className="text-muted-foreground max-w-xs truncate text-sm">
          {team.description || 'No description'}
        </div>
      ),
    },
    {
      key: 'members',
      label: 'Members',
      render: (team: SupportTeam) => (
        <div className="flex items-center space-x-2">
          <Users className="text-muted-foreground h-4 w-4" />
          <Badge variant="secondary">{team.memberIds?.length || 0}</Badge>
        </div>
      ),
    },
    {
      key: 'programs',
      label: 'Programs',
      render: (team: SupportTeam) => (
        <div className="flex items-center space-x-2">
          <BookOpen className="text-muted-foreground h-4 w-4" />
          <Badge variant="secondary">{team.assignedProgramIds?.length || 0}</Badge>
        </div>
      ),
    },
    {
      key: 'actions',
      label: 'Actions',
      render: (team: SupportTeam) => (
        <div className="flex space-x-2">
          <Button variant="ghost" size="sm" onClick={() => onEditTeam(team)}>
            <Edit className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => handleDeleteClick(team)}
            className="text-destructive hover:text-destructive"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      ),
    },
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <div className="relative">
            <Search className="text-muted-foreground absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform" />
            <Input
              placeholder="Search support teams..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-64 pl-10"
            />
          </div>
        </div>
        <Button onClick={onCreateTeam}>
          <Plus className="mr-2 h-4 w-4" />
          Create Team
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Support Teams</CardTitle>
        </CardHeader>
        <CardContent>
          <DataTable
            data={supportTeams}
            columns={columns}
            loading={loading}
            emptyMessage="No support teams found"
          />
        </CardContent>
      </Card>

      <ConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Support Team"
        description={`Are you sure you want to delete "${teamToDelete?.name}"? This action cannot be undone.`}
        onConfirm={handleDeleteConfirm}
        confirmText="Delete"
        cancelText="Cancel"
        variant="destructive"
      />
    </div>
  )
}
