import type { SupportTeam } from '@/shared/types'

export type { SupportTeam }

export interface CreateSupportTeamRequest {
  name: string
  description?: string
  memberIds?: string[]
  assignedProgramIds?: string[]
}

export interface UpdateSupportTeamRequest {
  name?: string
  description?: string
  memberIds?: string[]
  assignedProgramIds?: string[]
}

export interface SupportTeamFilters {
  search?: string
  hasMembers?: boolean
  hasPrograms?: boolean
}

export interface SupportTeamStats {
  totalTeams: number
  teamsWithMembers: number
  teamsWithPrograms: number
  totalMembers: number
  totalPrograms: number
}
