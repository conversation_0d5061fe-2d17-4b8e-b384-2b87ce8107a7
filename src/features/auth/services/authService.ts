import { <PERSON><PERSON>AP<PERSON> } from '@/services/tauriAPI'
import { SecurityService } from '@/services/securityService'
import type { LoginCredentials, AuthResponse, User } from '@/shared/types'

export class AuthService {
  private static readonly TOKEN_KEY = 'gt-ega-auth-token'
  private static readonly USER_KEY = 'gt-ega-user'
  private static readonly REFRESH_TOKEN_KEY = 'gt-ega-refresh-token'

  /**
   * Enhanced login with security features
   */
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      // Rate limiting check
      const isRateLimited = SecurityService.isRateLimited(credentials.email)
      if (isRateLimited) {
        const rateLimitInfo = SecurityService.getRateLimitInfo(credentials.email)
        const resetTime = new Date(rateLimitInfo.resetTime).toLocaleTimeString()
        throw new Error(`Too many login attempts. Try again after ${resetTime}`)
      }

      // Sanitize inputs
      const sanitizedEmail = SecurityService.sanitizeInput(credentials.email).toLowerCase()
      const sanitizedPassword = SecurityService.sanitizeInput(credentials.password)

      // Validate email format
      if (!this.isValidEmail(sanitizedEmail)) {
        throw new Error('Invalid email format')
      }

      // Call the backend API
      const response = await TauriAPI.login({
        email: sanitizedEmail,
        password: sanitizedPassword,
      })

      // Verify the response contains required fields
      if (!response.token || !response.user) {
        throw new Error('Invalid authentication response')
      }

      // Verify the token
      const tokenPayload = SecurityService.verifyToken(response.token)
      if (!tokenPayload) {
        throw new Error('Invalid authentication token')
      }

      // Store securely
      this.setToken(response.token)
      this.setUser(response.user)

      // Clear rate limit on successful login
      SecurityService.clearRateLimit(credentials.email)

      return response
    } catch (error) {
      // Log failed login attempt (in production, use proper logging service)
      console.warn(`Failed login attempt for email: ${credentials.email}`, {
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Unknown error',
      })

      throw new Error(error instanceof Error ? error.message : 'Login failed')
    }
  }

  /**
   * Enhanced logout with proper cleanup
   */
  static async logout(): Promise<void> {
    const token = this.getToken()
    if (token) {
      try {
        await TauriAPI.logout(token)
      } catch (error) {
        console.error('Logout error:', error)
      }
    }

    this.clearAuth()
  }

  /**
   * Secure token storage
   */
  static setToken(token: string): void {
    try {
      // Verify token before storing
      const payload = SecurityService.verifyToken(token)
      if (!payload) {
        throw new Error('Invalid token')
      }

      localStorage.setItem(this.TOKEN_KEY, token)

      // Set auto-logout timer
      this.setAutoLogout(token)
    } catch (error) {
      console.error('Failed to set token:', error)
      throw error
    }
  }

  static getToken(): string | null {
    const token = localStorage.getItem(this.TOKEN_KEY)

    if (!token) return null

    // Check if token is expired
    if (SecurityService.isTokenExpired(token)) {
      this.clearAuth()
      return null
    }

    return token
  }

  /**
   * Secure user data storage
   */
  static setUser(user: User): void {
    try {
      // Validate user data
      if (!user.id || !user.email || !user.role) {
        throw new Error('Invalid user data')
      }

      // Remove sensitive data before storing
      const sanitizedUser: User = {
        id: user.id,
        name: SecurityService.sanitizeInput(user.name || ''),
        email: SecurityService.sanitizeInput(user.email).toLowerCase(),
        role: user.role,
        status: user.status || 'active',
      }

      localStorage.setItem(this.USER_KEY, JSON.stringify(sanitizedUser))
    } catch (error) {
      console.error('Failed to set user:', error)
    }
  }

  static getUser(): User | null {
    try {
      const userStr = localStorage.getItem(this.USER_KEY)
      if (!userStr) return null

      const user = JSON.parse(userStr) as User

      // Validate user data
      if (!user.id || !user.email || !user.role) {
        this.clearAuth()
        return null
      }

      return user
    } catch (error) {
      console.error('Failed to get user:', error)
      this.clearAuth()
      return null
    }
  }

  /**
   * Clear all authentication data
   */
  static clearAuth(): void {
    localStorage.removeItem(this.TOKEN_KEY)
    localStorage.removeItem(this.USER_KEY)
    localStorage.removeItem(this.REFRESH_TOKEN_KEY)

    // Clear any pending auto-logout timers
    if (this.autoLogoutTimer) {
      clearTimeout(this.autoLogoutTimer)
      this.autoLogoutTimer = null
    }
  }

  /**
   * Enhanced authentication check
   */
  static isAuthenticated(): boolean {
    const token = this.getToken()
    const user = this.getUser()

    if (!token || !user) return false

    // Additional validation
    const payload = SecurityService.verifyToken(token)
    return payload !== null && payload.userId === user.id
  }

  /**
   * Get current user with validation
   */
  static async getCurrentUser(): Promise<User> {
    const cachedUser = this.getUser()
    if (cachedUser) {
      return cachedUser
    }

    try {
      // Add timeout to prevent hanging
      const user = (await Promise.race([
        TauriAPI.getCurrentUser(),
        new Promise<never>((_, reject) =>
          setTimeout(() => reject(new Error('Auth timeout')), 2000)
        ),
      ])) as User

      this.setUser(user)
      return user
    } catch (error) {
      this.clearAuth()
      throw new Error('Failed to get current user')
    }
  }

  /**
   * Check if user has required role
   */
  static hasRole(requiredRole: string): boolean {
    const user = this.getUser()
    if (!user) return false

    // Role hierarchy: admin > ld_officer > manager > direct_manager > mentor > trainee
    const roleHierarchy = {
      admin: 6,
      ld_officer: 5,
      manager: 4,
      direct_manager: 3,
      mentor: 2,
      trainee: 1,
    }

    const userRoleLevel = roleHierarchy[user.role as keyof typeof roleHierarchy] || 0
    const requiredRoleLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0

    return userRoleLevel >= requiredRoleLevel
  }

  /**
   * Email validation helper
   */
  private static isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Auto-logout functionality
   */
  private static autoLogoutTimer: NodeJS.Timeout | null = null

  private static setAutoLogout(token: string): void {
    // Clear existing timer
    if (this.autoLogoutTimer) {
      clearTimeout(this.autoLogoutTimer)
    }

    try {
      const payload = SecurityService.verifyToken(token)
      if (!payload || !payload.exp) return

      const now = Math.floor(Date.now() / 1000)
      const timeUntilExpiry = (payload.exp - now) * 1000

      // Auto-logout 5 minutes before token expires
      const autoLogoutTime = Math.max(timeUntilExpiry - 5 * 60 * 1000, 60000)

      this.autoLogoutTimer = setTimeout(() => {
        console.warn('Session expired - logging out')
        this.logout()
      }, autoLogoutTime)
    } catch (error) {
      console.error('Failed to set auto-logout:', error)
    }
  }

  /**
   * Get rate limit information for login attempts
   */
  static getRateLimitInfo(email: string): {
    remaining: number
    resetTime: Date
    isLimited: boolean
  } {
    const rateLimitInfo = SecurityService.getRateLimitInfo(email)

    return {
      remaining: rateLimitInfo.remaining,
      resetTime: new Date(rateLimitInfo.resetTime),
      isLimited: rateLimitInfo.remaining === 0,
    }
  }

  /**
   * Refresh authentication token
   */
  static async refreshToken(): Promise<boolean> {
    try {
      const currentUser = this.getUser()
      if (!currentUser) return false

      // In a real implementation, you'd have a refresh token flow
      // For now, we'll require re-login
      return false
    } catch (error) {
      console.error('Token refresh failed:', error)
      this.clearAuth()
      return false
    }
  }
}
