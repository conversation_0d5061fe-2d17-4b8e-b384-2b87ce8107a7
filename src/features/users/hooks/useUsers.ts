import { useState, useEffect, useCallback } from 'react'
import { UserService } from '../services/userService'
import type { User, CreateUserData, UpdateUserData, UserRole } from '../types'

export function useUsers() {
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const fetchUsers = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const data = await UserService.getUsers()
      setUsers(data)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch users')
    } finally {
      setLoading(false)
    }
  }, [])

  const createUser = useCallback(async (userData: CreateUserData) => {
    setLoading(true)
    setError(null)
    try {
      const newUser = await UserService.createUser(userData)
      setUsers((prev) => [...prev, newUser])
      return newUser
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to create user'
      setError(errorMsg)
      throw new Error(errorMsg)
    } finally {
      setLoading(false)
    }
  }, [])

  const updateUser = useCallback(async (id: string, userData: UpdateUserData) => {
    setLoading(true)
    setError(null)
    try {
      const updatedUser = await UserService.updateUser(id, userData)
      setUsers((prev) => prev.map((user) => (user.id === id ? updatedUser : user)))
      return updatedUser
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to update user'
      setError(errorMsg)
      throw new Error(errorMsg)
    } finally {
      setLoading(false)
    }
  }, [])

  const deleteUser = useCallback(async (id: string) => {
    setLoading(true)
    setError(null)
    try {
      await UserService.deleteUser(id)
      setUsers((prev) => prev.filter((user) => user.id !== id))
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Failed to delete user'
      setError(errorMsg)
      throw new Error(errorMsg)
    } finally {
      setLoading(false)
    }
  }, [])

  const getUsersByRole = useCallback(async (role: UserRole) => {
    setLoading(true)
    setError(null)
    try {
      const data = await UserService.getUsersByRole(role)
      return data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch users by role')
      return []
    } finally {
      setLoading(false)
    }
  }, [])

  useEffect(() => {
    fetchUsers()
  }, [fetchUsers])

  return {
    users,
    loading,
    error,
    fetchUsers,
    createUser,
    updateUser,
    deleteUser,
    getUsersByRole,
  }
}
