import { describe, it, expect } from 'vitest'

// Test feature imports
import * as reports from '../features/reports'
import * as assessments from '../features/assessments'
import * as dashboard from '../features/dashboard'
import * as trainees from '../features/trainees'
import * as trainingPrograms from '../features/training-programs'

describe('Feature Index Imports', () => {
  it('should import reports feature correctly', () => {
    expect(reports).toBeDefined()
  })

  it('should import assessments feature correctly', () => {
    expect(assessments).toBeDefined()
  })

  it('should import dashboard feature correctly', () => {
    expect(dashboard).toBeDefined()
  })

  it('should import trainees feature correctly', () => {
    expect(trainees).toBeDefined()
  })

  it('should import training programs feature correctly', () => {
    expect(trainingPrograms).toBeDefined()
  })
})
