import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'
import path from 'path'

export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: [
      // Map shared components before the general '@' alias to avoid mis-resolution
      { find: /^@\/components/, replacement: path.resolve(__dirname, './src/shared/components') },
      { find: /^@\//, replacement: path.resolve(__dirname, './src') + '/' },
    ],
  },
  test: {
    environment: 'jsdom',
    setupFiles: ['./src/test/setup.ts'],
    globals: true,
  },
})