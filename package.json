{"name": "gt-ega", "private": true, "version": "0.0.0", "type": "module", "description": "Training Management System with FullCalendar Resource Scheduling", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "preview": "vite preview", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "prepare": "husky", "build:all": "npm run type-check && npm run lint && npm run build"}, "dependencies": {"@fullcalendar/adaptive": "^6.1.19", "@fullcalendar/core": "^6.1.19", "@fullcalendar/daygrid": "^6.1.19", "@fullcalendar/interaction": "^6.1.19", "@fullcalendar/list": "^6.1.19", "@fullcalendar/multimonth": "^6.1.19", "@fullcalendar/react": "^6.1.19", "@fullcalendar/resource": "^6.1.19", "@fullcalendar/resource-timeline": "^6.1.19", "@fullcalendar/timegrid": "^6.1.19", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-dialog": "^1.1.15", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@radix-ui/react-tooltip": "^1.2.8", "@tanstack/react-table": "^8.21.3", "@tauri-apps/plugin-clipboard-manager": "^2.3.0", "@tauri-apps/plugin-dialog": "^2.4.0", "@tauri-apps/plugin-fs": "^2.4.2", "@tauri-apps/plugin-global-shortcut": "^2.3.0", "@tauri-apps/plugin-notification": "^2.3.1", "@tauri-apps/plugin-os": "^2.3.1", "@tauri-apps/plugin-process": "^2.3.0", "@tauri-apps/plugin-shell": "^2.3.1", "@tauri-apps/plugin-updater": "^2.9.0", "@tauri-apps/plugin-window-state": "^2.4.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "bcryptjs": "^3.0.2", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "framer-motion": "^12.23.22", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "lucide-react": "^0.544.0", "nanoid": "^5.1.6", "next-themes": "^0.4.6", "react": "^19.1.1", "react-chartjs-2": "^5.3.0", "react-dom": "^19.1.1", "react-hook-form": "^7.63.0", "react-query": "^3.39.3", "react-router-dom": "^7.9.3", "react-swipeable": "^7.0.2", "recharts": "^3.2.1", "sonner": "^2.0.7", "swr": "^2.3.6", "tailwind-merge": "^3.3.1", "yup": "^1.7.1", "zod": "^4.1.11", "zustand": "^5.0.8"}, "devDependencies": {"@eslint/js": "^9.36.0", "@shadcn/ui": "^0.0.4", "@tailwindcss/postcss": "^4.1.13", "@tailwindcss/vite": "^4.1.13", "@tauri-apps/api": "^2.8.0", "@tauri-apps/cli": "^2.8.4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^24.5.2", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "@vitejs/plugin-react": "^5.0.3", "@vitest/ui": "^3.2.4", "autoprefixer": "^10.4.21", "eslint": "^9.36.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.2.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "husky": "^9.1.7", "lint-staged": "^16.0.2", "postcss": "^8.5.6", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.10", "tailwindcss": "^4.1.13", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.3", "typescript-eslint": "^8.44.0", "vite": "^7.1.7", "vitest": "^3.2.4"}}