[package]
name = "gt-ega"
version = "0.1.0"
description = "Trainee Tracking System"
authors = ["you"]
edition = "2021"
build = "build.rs"

[lib]
name = "gt_ega_lib"
crate-type = ["staticlib", "cdylib", "rlib"]
path = "src/lib.rs"

[[bin]]
name = "gt-ega"
path = "src/main.rs"



[dependencies]
tauri = { version = "2.0", features = ["tray-icon", "image-ico", "image-png"] }
tauri-plugin-notification = "2.0"
tauri-plugin-dialog = "2.0"
tauri-plugin-fs = "2.0"
tauri-plugin-updater = "2.0"
tauri-plugin-window-state = "2.0"
tauri-plugin-clipboard-manager = "2.0"
tauri-plugin-shell = "2.0"
tauri-plugin-global-shortcut = "2.0"
tauri-plugin-os = "2.0"
tauri-plugin-process = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
tokio = { version = "1.0", features = ["full"] }
sqlx = { version = "0.7", features = ["runtime-tokio-rustls", "sqlite", "chrono", "uuid"] }
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
bcrypt = "0.15"
jsonwebtoken = "9.2"

[build-dependencies]
tauri-build = { version = "2", features = [] }
