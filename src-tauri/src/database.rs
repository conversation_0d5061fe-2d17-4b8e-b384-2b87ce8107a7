use serde::{Deserialize, Serialize};
use sqlx::{FromRow, SqlitePool, Row};
use sqlx::sqlite::{SqliteConnectOptions, SqlitePoolOptions};
use std::path::PathBuf;
use chrono::{DateTime, Utc};



#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct User {
    pub id: String,
    pub name: String,
    pub email: String,
    pub password_hash: String,
    pub role: String,
    pub status: String,
    pub last_login: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct TrainingProgram {
    pub id: String,
    pub name: String,
    pub duration_months: i32,
    pub description: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Trainee {
    pub id: String,
    pub user_id: String,
    pub college: String,
    pub enrollment_date: DateTime<Utc>,
    pub training_program_id: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
#[allow(dead_code)]
pub struct SupportTeam {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub area: String,
    pub status: String,
    pub contact_email: Option<String>,
    pub contact_phone: Option<String>,
    pub contact_location: Option<String>,
    pub responsibilities: Option<String>, // JSON array
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub created_by: String,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
#[allow(dead_code)]
pub struct SupportTeamMember {
    pub id: String,
    pub team_id: String,
    pub user_id: String,
    pub role: String, // lead, member, specialist
    pub joined_at: DateTime<Utc>,
    pub permissions: Option<String>, // JSON array
    pub specializations: Option<String>, // JSON array
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
#[allow(dead_code)]
pub struct Report {
    pub id: String,
    pub name: String,
    pub type_: String, // assessment, trainee, program, performance
    pub description: Option<String>,
    pub parameters: String, // JSON object
    pub generated_by: String,
    pub generated_at: DateTime<Utc>,
    pub file_path: Option<String>,
    pub file_size: Option<i64>,
    pub status: String, // generating, completed, failed
    pub expires_at: Option<DateTime<Utc>>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthCredentials {
    pub email: String,
    pub password: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AuthResponse {
    pub token: String,
    pub user: serde_json::Value,
    pub expires_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct Assessment {
    pub id: String,
    pub trainee_id: String,
    pub quarter: i32,
    pub year: i32,
    pub status: String,
    pub overall_score: Option<f64>,
    pub scheduled_date: DateTime<Utc>,
    pub due_date: DateTime<Utc>,
    pub completed_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
#[allow(dead_code)]
pub struct AssessmentReview {
    pub id: String,
    pub assessment_id: String,
    pub reviewer_id: String,
    pub reviewer_role: String,
    pub technical_score: Option<f64>,
    pub soft_skills_score: Option<f64>,
    pub on_job_score: Option<f64>,
    pub comments: Option<String>,
    pub strengths: Option<String>, // JSON string
    pub improvements: Option<String>, // JSON string
    pub submitted_at: Option<DateTime<Utc>>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

// Training Budget Management
#[derive(Debug, Serialize, Deserialize, FromRow)]
pub struct TrainingType {
    pub id: String,
    pub name: String,
    pub description: Option<String>,
    pub requires_budget: bool,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
#[allow(dead_code)]
pub struct TrainingBudget {
    pub id: String,
    pub title: String,
    pub description: Option<String>,
    pub training_type_id: String,
    pub total_budget: f64,
    pub currency: String,
    pub start_date: DateTime<Utc>,
    pub end_date: DateTime<Utc>,
    pub status: String,
    pub location: Option<String>,
    pub provider: Option<String>,
    pub created_by: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
#[allow(dead_code)]
pub struct TrainingBudgetItem {
    pub id: String,
    pub budget_id: String,
    pub category: String,
    pub item_name: String,
    pub estimated_cost: f64,
    pub actual_cost: Option<f64>,
    pub notes: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
#[allow(dead_code)]
pub struct TrainingSchedule {
    pub id: String,
    pub budget_id: String,
    pub title: String,
    pub description: Option<String>,
    pub start_datetime: DateTime<Utc>,
    pub end_datetime: DateTime<Utc>,
    pub location: Option<String>,
    pub instructor: Option<String>,
    pub max_participants: Option<i32>,
    pub status: String,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
#[allow(dead_code)]
pub struct TrainingAttendee {
    pub id: String,
    pub schedule_id: String,
    pub user_id: String,
    pub registration_status: String,
    pub registration_date: DateTime<Utc>,
    pub notes: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Serialize, Deserialize, FromRow)]
#[allow(dead_code)]
pub struct BudgetApproval {
    pub id: String,
    pub budget_id: String,
    pub approver_id: String,
    pub status: String,
    pub approval_date: Option<DateTime<Utc>>,
    pub comments: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

pub struct Database {
    pool: SqlitePool,
}

impl Database {
    pub async fn new(database_path: PathBuf) -> Result<Self, sqlx::Error> {
        // Use ConnectOptions with a concrete filesystem path to avoid URL parsing issues
        let connect_opts = SqliteConnectOptions::new()
            .filename(&database_path)
            .create_if_missing(true);

        let pool = SqlitePoolOptions::new()
            .max_connections(5)
            .connect_with(connect_opts)
            .await?;
        
        // Run migrations
        sqlx::migrate!("./migrations").run(&pool).await?;
        
        Ok(Database { pool })
    }

    pub async fn get_dashboard_stats(&self) -> Result<serde_json::Value, sqlx::Error> {
        let total_trainees: i64 = sqlx::query_scalar("SELECT COUNT(*) FROM trainees")
            .fetch_one(&self.pool)
            .await?;

        // Calculate active trainees (enrolled but not completed)
        let active_trainees: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM trainees WHERE enrollment_date < datetime('now')"
        )
        .fetch_one(&self.pool)
            .await?;

        let completed_trainees: i64 = total_trainees - active_trainees;

        let pending_assessments: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM assessments WHERE status = 'pending'"
        )
        .fetch_one(&self.pool)
            .await?;

        let overdue_assessments: i64 = sqlx::query_scalar(
            "SELECT COUNT(*) FROM assessments WHERE status != 'completed' AND due_date < datetime('now')"
        )
        .fetch_one(&self.pool)
            .await?;

        // Get program distribution
        let program_rows = sqlx::query(
            "SELECT tp.name, COUNT(t.id) as count 
             FROM training_programs tp 
             LEFT JOIN trainees t ON tp.id = t.training_program_id 
             GROUP BY tp.id, tp.name"
        )
        .fetch_all(&self.pool)
        .await?;

        let mut programs_by_type = serde_json::Map::new();
        for row in program_rows {
            let name: String = row.get("name");
            let count: i64 = row.get("count");
            programs_by_type.insert(name, serde_json::Value::Number(count.into()));
        }

        // Get assessments by quarter
        let assessment_rows = sqlx::query(
            "SELECT 'Q' || quarter || ' ' || year as quarter_year, COUNT(*) as count 
             FROM assessments 
             GROUP BY quarter, year 
             ORDER BY year, quarter"
        )
        .fetch_all(&self.pool)
        .await?;

        let mut assessments_by_quarter = serde_json::Map::new();
        for row in assessment_rows {
            let quarter_year: String = row.get("quarter_year");
            let count: i64 = row.get("count");
            assessments_by_quarter.insert(quarter_year, serde_json::Value::Number(count.into()));
        }

        let stats = serde_json::json!({
            "totalTrainees": total_trainees,
            "activeTrainees": active_trainees,
            "completedTrainees": completed_trainees,
            "pendingAssessments": pending_assessments,
            "overdueAssessments": overdue_assessments,
            "averageProgress": if total_trainees > 0 { 
                completed_trainees as f64 / total_trainees as f64 * 100.0 
            } else { 
                0.0 
            },
            "programsByType": programs_by_type,
            "assessmentsByQuarter": assessments_by_quarter,
        });

        Ok(stats)
    }

    pub async fn get_trainees(&self) -> Result<Vec<Trainee>, sqlx::Error> {
        sqlx::query_as("SELECT * FROM trainees")
            .fetch_all(&self.pool)
            .await
    }

    pub async fn save_trainee(&self, trainee: Trainee) -> Result<(), sqlx::Error> {
        sqlx::query(
            "INSERT OR REPLACE INTO trainees (id, user_id, college, enrollment_date, training_program_id, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(&trainee.id)
        .bind(&trainee.user_id)
        .bind(&trainee.college)
        .bind(&trainee.enrollment_date)
        .bind(&trainee.training_program_id)
        .bind(&trainee.created_at)
        .bind(&trainee.updated_at)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    pub async fn get_assessments(&self, trainee_id: Option<String>) -> Result<Vec<Assessment>, sqlx::Error> {
        if let Some(trainee_id) = trainee_id {
            sqlx::query_as("SELECT * FROM assessments WHERE trainee_id = ?")
                .bind(&trainee_id)
                .fetch_all(&self.pool)
                .await
        } else {
            sqlx::query_as("SELECT * FROM assessments")
                .fetch_all(&self.pool)
                .await
        }
    }

    pub async fn save_assessment(&self, assessment: Assessment) -> Result<(), sqlx::Error> {
        sqlx::query(
            "INSERT OR REPLACE INTO assessments (id, trainee_id, quarter, year, status, overall_score, scheduled_date, due_date, completed_at, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
        )
        .bind(&assessment.id)
        .bind(&assessment.trainee_id)
        .bind(&assessment.quarter)
        .bind(&assessment.year)
        .bind(&assessment.status)
        .bind(&assessment.overall_score)
        .bind(&assessment.scheduled_date)
        .bind(&assessment.due_date)
        .bind(&assessment.completed_at)
        .bind(&assessment.created_at)
        .bind(&assessment.updated_at)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    

    // Training Programs
    pub async fn get_training_programs(&self) -> Result<Vec<TrainingProgram>, sqlx::Error> {
        sqlx::query_as("SELECT * FROM training_programs ORDER BY created_at DESC")
            .fetch_all(&self.pool)
            .await
    }

    pub async fn save_training_program(&self, program: &TrainingProgram) -> Result<(), sqlx::Error> {
        sqlx::query(
            "INSERT OR REPLACE INTO training_programs (id, name, duration_months, description, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, ?)"
        )
        .bind(&program.id)
        .bind(&program.name)
        .bind(&program.duration_months)
        .bind(&program.description)
        .bind(&program.created_at)
        .bind(&program.updated_at)
        .execute(&self.pool)
        .await?;

        Ok(())
    }

    

    // Get trainees with related data
    pub async fn get_trainees_with_details(&self) -> Result<Vec<serde_json::Value>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT 
                t.id,
                t.user_name,
                t.user_email,
                t.user_role,
                t.college,
                t.enrollment_date,
                t.training_program_id,
                t.created_at,
                t.updated_at,
                tp.name as program_name,
                tp.duration_months,
                tp.description as program_description
            FROM trainees t
            JOIN training_programs tp ON t.training_program_id = tp.id
            ORDER BY t.created_at DESC
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let trainees: Result<Vec<_>, _> = rows.into_iter().map(|row| {
            Ok(serde_json::json!({
                "id": row.get::<String, _>("id"),
                "userName": row.get::<String, _>("user_name"),
                "userEmail": row.get::<String, _>("user_email"),
                "userRole": row.get::<String, _>("user_role"),
                "college": row.get::<String, _>("college"),
                "enrollmentDate": row.get::<DateTime<Utc>, _>("enrollment_date"),
                "trainingProgramId": row.get::<String, _>("training_program_id"),
                "createdAt": row.get::<DateTime<Utc>, _>("created_at"),
                "updatedAt": row.get::<DateTime<Utc>, _>("updated_at"),
                "trainingProgram": {
                    "id": row.get::<String, _>("training_program_id"),
                    "name": row.get::<String, _>("program_name"),
                    "durationMonths": row.get::<i32, _>("duration_months"),
                    "description": row.get::<Option<String>, _>("program_description")
                }
            }))
        }).collect();

        trainees
    }

    // Get assessments with related data
    pub async fn get_assessments_with_details(&self, trainee_id: Option<String>) -> Result<Vec<serde_json::Value>, sqlx::Error> {
        let (query, _has_params) = if trainee_id.is_some() {
            (r#"
            SELECT 
                a.id,
                a.trainee_id,
                a.quarter,
                a.year,
                a.status,
                a.overall_score,
                a.scheduled_date,
                a.due_date,
                a.completed_at,
                a.created_at,
                a.updated_at,
                t.user_name as trainee_name,
                t.user_email as trainee_email
            FROM assessments a
            JOIN trainees t ON a.trainee_id = t.id
            WHERE a.trainee_id = ?
            ORDER BY a.created_at DESC
            "#, true)
        } else {
            (r#"
            SELECT 
                a.id,
                a.trainee_id,
                a.quarter,
                a.year,
                a.status,
                a.overall_score,
                a.scheduled_date,
                a.due_date,
                a.completed_at,
                a.created_at,
                a.updated_at,
                t.user_name as trainee_name,
                t.user_email as trainee_email
            FROM assessments a
            JOIN trainees t ON a.trainee_id = t.id
            ORDER BY a.created_at DESC
            "#, false)
        };

        let mut query_builder = sqlx::query(query);
        if let Some(ref id) = trainee_id {
            query_builder = query_builder.bind(id);
        }

        let rows = query_builder.fetch_all(&self.pool).await?;

        let assessments: Result<Vec<_>, _> = rows.into_iter().map(|row| {
            Ok(serde_json::json!({
                "id": row.get::<String, _>("id"),
                "traineeId": row.get::<String, _>("trainee_id"),
                "quarter": row.get::<i32, _>("quarter"),
                "year": row.get::<i32, _>("year"),
                "status": row.get::<String, _>("status"),
                "overallScore": row.get::<Option<f64>, _>("overall_score"),
                "scheduledDate": row.get::<DateTime<Utc>, _>("scheduled_date"),
                "dueDate": row.get::<DateTime<Utc>, _>("due_date"),
                "completedAt": row.get::<Option<DateTime<Utc>>, _>("completed_at"),
                "createdAt": row.get::<DateTime<Utc>, _>("created_at"),
                "updatedAt": row.get::<DateTime<Utc>, _>("updated_at"),
                "trainee": {
                    "id": row.get::<String, _>("trainee_id"),
                    "user": {
                        "name": row.get::<String, _>("trainee_name"),
                        "email": row.get::<String, _>("trainee_email")
                    }
                }
            }))
        }).collect();

        assessments
    }
    // Authentication methods
    pub async fn authenticate_user(&self, email: &str, _password: &str) -> Result<Option<serde_json::Value>, sqlx::Error> {
        // First try to find user in users table
        let user = sqlx::query(
            "SELECT id, name, email, role, status FROM users WHERE email = ? AND status = 'active'"
        )
        .bind(email)
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = user {
            return Ok(Some(serde_json::json!({
                "id": row.get::<String, _>("id"),
                "name": row.get::<String, _>("name"),
                "email": row.get::<String, _>("email"),
                "role": row.get::<String, _>("role"),
                "status": row.get::<String, _>("status")
            })));
        }

        // Fallback: check trainees table for backward compatibility
        let trainee_user = sqlx::query(
            "SELECT user_email, user_name, user_role FROM trainees WHERE user_email = ? AND user_role != 'trainee'"
        )
        .bind(email)
        .fetch_optional(&self.pool)
        .await?;

        Ok(trainee_user.map(|row| {
            serde_json::json!({
                "id": row.get::<String, _>("user_email"), // Use email as ID
                "name": row.get::<String, _>("user_name"),
                "email": row.get::<String, _>("user_email"),
                "role": row.get::<String, _>("user_role")
            })
        }))
    }

    // Support Teams methods
    pub async fn get_support_teams(&self) -> Result<Vec<serde_json::Value>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT 
                st.id,
                st.name,
                st.description,
                st.area,
                st.status,
                st.contact_email,
                st.contact_phone,
                st.contact_location,
                st.responsibilities,
                st.created_at,
                st.updated_at,
                st.created_by,
                COUNT(stm.id) as member_count
            FROM support_teams st
            LEFT JOIN support_team_members stm ON st.id = stm.team_id
            GROUP BY st.id
            ORDER BY st.created_at DESC
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let teams: Result<Vec<_>, _> = rows.into_iter().map(|row| {
            let responsibilities: Vec<String> = row.get::<Option<String>, _>("responsibilities")
                .and_then(|s| serde_json::from_str(&s).ok())
                .unwrap_or_default();

            Ok(serde_json::json!({
                "id": row.get::<String, _>("id"),
                "name": row.get::<String, _>("name"),
                "description": row.get::<Option<String>, _>("description"),
                "area": row.get::<String, _>("area"),
                "status": row.get::<String, _>("status"),
                "contactInfo": {
                    "email": row.get::<Option<String>, _>("contact_email"),
                    "phone": row.get::<Option<String>, _>("contact_phone"),
                    "location": row.get::<Option<String>, _>("contact_location")
                },
                "responsibilities": responsibilities,
                "memberCount": row.get::<i64, _>("member_count"),
                "createdAt": row.get::<DateTime<Utc>, _>("created_at"),
                "updatedAt": row.get::<DateTime<Utc>, _>("updated_at"),
                "createdBy": row.get::<String, _>("created_by")
            }))
        }).collect();

        teams
    }

    pub async fn get_support_team(&self, team_id: &str) -> Result<Option<serde_json::Value>, sqlx::Error> {
        let team_row = sqlx::query(
            r#"
            SELECT 
                st.id,
                st.name,
                st.description,
                st.area,
                st.status,
                st.contact_email,
                st.contact_phone,
                st.contact_location,
                st.responsibilities,
                st.created_at,
                st.updated_at,
                st.created_by
            FROM support_teams st
            WHERE st.id = ?
            "#
        )
        .bind(team_id)
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = team_row {
            // Get team members (simplified without user details since users table was removed)
            let member_rows = sqlx::query(
                r#"
                SELECT 
                    stm.id,
                    stm.role,
                    stm.joined_at,
                    stm.permissions,
                    stm.specializations
                FROM support_team_members stm
                WHERE stm.team_id = ?
                ORDER BY stm.joined_at
                "#
            )
            .bind(team_id)
            .fetch_all(&self.pool)
            .await?;

            let members: Vec<serde_json::Value> = member_rows.into_iter().map(|member_row| {
                let permissions: Vec<String> = member_row.get::<Option<String>, _>("permissions")
                    .and_then(|s| serde_json::from_str(&s).ok())
                    .unwrap_or_default();
                
                let specializations: Vec<String> = member_row.get::<Option<String>, _>("specializations")
                    .and_then(|s| serde_json::from_str(&s).ok())
                    .unwrap_or_default();

                serde_json::json!({
                    "id": member_row.get::<String, _>("id"),
                    "role": member_row.get::<String, _>("role"),
                    "joinedAt": member_row.get::<DateTime<Utc>, _>("joined_at"),
                    "permissions": permissions,
                    "specializations": specializations
                })
            }).collect();

            let responsibilities: Vec<String> = row.get::<Option<String>, _>("responsibilities")
                .and_then(|s| serde_json::from_str(&s).ok())
                .unwrap_or_default();

            let team = serde_json::json!({
                "id": row.get::<String, _>("id"),
                "name": row.get::<String, _>("name"),
                "description": row.get::<Option<String>, _>("description"),
                "area": row.get::<String, _>("area"),
                "status": row.get::<String, _>("status"),
                "contactInfo": {
                    "email": row.get::<Option<String>, _>("contact_email"),
                    "phone": row.get::<Option<String>, _>("contact_phone"),
                    "location": row.get::<Option<String>, _>("contact_location")
                },
                "responsibilities": responsibilities,
                "members": members,
                "createdAt": row.get::<DateTime<Utc>, _>("created_at"),
                "updatedAt": row.get::<DateTime<Utc>, _>("updated_at"),
                "createdBy": row.get::<String, _>("created_by")
            });

            Ok(Some(team))
        } else {
            Ok(None)
        }
    }

    pub async fn create_support_team(&self, team_data: serde_json::Value, created_by: &str) -> Result<String, sqlx::Error> {
        let team_id = uuid::Uuid::new_v4().to_string();
        let now = Utc::now();
        
        let responsibilities_json = serde_json::to_string(
            team_data.get("responsibilities").unwrap_or(&serde_json::json!([]))
        ).unwrap_or_else(|_| "[]".to_string());

        let contact_info_default = serde_json::json!({});
        let contact_info = team_data.get("contactInfo").unwrap_or(&contact_info_default);

        sqlx::query(
            r#"
            INSERT INTO support_teams 
            (id, name, description, area, status, contact_email, contact_phone, contact_location, responsibilities, created_at, updated_at, created_by)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&team_id)
        .bind(team_data.get("name").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(team_data.get("description").and_then(|v| v.as_str()))
        .bind(team_data.get("area").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(team_data.get("status").and_then(|v| v.as_str()).unwrap_or("active"))
        .bind(contact_info.get("email").and_then(|v| v.as_str()))
        .bind(contact_info.get("phone").and_then(|v| v.as_str()))
        .bind(contact_info.get("location").and_then(|v| v.as_str()))
        .bind(&responsibilities_json)
        .bind(&now)
        .bind(&now)
        .bind(created_by)
        .execute(&self.pool)
        .await?;

        // Add team members if provided
        if let Some(members) = team_data.get("members").and_then(|v| v.as_array()) {
            for member in members {
                let member_id = uuid::Uuid::new_v4().to_string();
                let permissions_json = serde_json::to_string(
                    member.get("permissions").unwrap_or(&serde_json::json!([]))
                ).unwrap_or_else(|_| "[]".to_string());
                
                let specializations_json = serde_json::to_string(
                    member.get("specializations").unwrap_or(&serde_json::json!([]))
                ).unwrap_or_else(|_| "[]".to_string());

                let query = "INSERT INTO support_team_members (id, team_id, user_id, role, joined_at, permissions, specializations) VALUES (?, ?, ?, ?, ?, ?, ?)";
                
                sqlx::query(query)
                .bind(&member_id)
                .bind(&team_id)
                .bind(member.get("userId").and_then(|v| v.as_str()).unwrap_or(""))
                .bind(member.get("role").and_then(|v| v.as_str()).unwrap_or("member"))
                .bind(&now)
                .bind(&permissions_json)
                .bind(&specializations_json)
                .execute(&self.pool)
                .await?;
            }
        }

        Ok(team_id)
    }

    pub async fn update_support_team(&self, team_id: &str, team_data: serde_json::Value) -> Result<(), sqlx::Error> {
        let now = Utc::now();
        
        let responsibilities_json = serde_json::to_string(
            team_data.get("responsibilities").unwrap_or(&serde_json::json!([]))
        ).unwrap_or_else(|_| "[]".to_string());

        let contact_info_default = serde_json::json!({});
        let contact_info = team_data.get("contactInfo").unwrap_or(&contact_info_default);

        sqlx::query(
            r#"
            UPDATE support_teams
            SET name = ?, description = ?, area = ?, status = ?,
                contact_email = ?, contact_phone = ?, contact_location = ?,
                responsibilities = ?, updated_at = ?
            WHERE id = ?
            "#
        )
        .bind(team_data.get("name").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(team_data.get("description").and_then(|v| v.as_str()))
        .bind(team_data.get("area").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(team_data.get("status").and_then(|v| v.as_str()).unwrap_or("active"))
        .bind(contact_info.get("email").and_then(|v| v.as_str()))
        .bind(contact_info.get("phone").and_then(|v| v.as_str()))
        .bind(contact_info.get("location").and_then(|v| v.as_str()))
        .bind(&responsibilities_json)
        .bind(&now)
        .bind(team_id)
        .execute(&self.pool)
        .await?;

        // Update team members if provided
        if let Some(members) = team_data.get("members").and_then(|v| v.as_array()) {
            // Remove existing members
            sqlx::query("DELETE FROM support_team_members WHERE team_id = ?")
                .bind(team_id)
                .execute(&self.pool)
                .await?;

            // Add new members
            for member in members {
                let member_id = uuid::Uuid::new_v4().to_string();
                let permissions_json = serde_json::to_string(
                    member.get("permissions").unwrap_or(&serde_json::json!([]))
                ).unwrap_or_else(|_| "[]".to_string());
                
                let specializations_json = serde_json::to_string(
                    member.get("specializations").unwrap_or(&serde_json::json!([]))
                ).unwrap_or_else(|_| "[]".to_string());

                sqlx::query("INSERT INTO support_team_members (id, team_id, user_id, role, joined_at, permissions, specializations) VALUES (?, ?, ?, ?, ?, ?, ?)")
                .bind(&member_id)
                .bind(team_id)
                .bind(member.get("userId").and_then(|v| v.as_str()).unwrap_or(""))
                .bind(member.get("role").and_then(|v| v.as_str()).unwrap_or("member"))
                .bind(&now)
                .bind(&permissions_json)
                .bind(&specializations_json)
                .execute(&self.pool)
                .await?;
            }
        }

        Ok(())
    }

    pub async fn delete_support_team(&self, team_id: &str) -> Result<(), sqlx::Error> {
        // Delete team members first (due to foreign key constraint)
        sqlx::query("DELETE FROM support_team_members WHERE team_id = ?")
            .bind(team_id)
            .execute(&self.pool)
            .await?;

        // Delete the team
        sqlx::query("DELETE FROM support_teams WHERE id = ?")
            .bind(team_id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn get_reports(&self) -> Result<Vec<serde_json::Value>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT
                r.id,
                r.name,
                r.type,
                r.description,
                r.parameters,
                r.generated_by,
                r.generated_at,
                r.file_path,
                r.file_size,
                r.status,
                r.expires_at
            FROM reports r
            ORDER BY r.generated_at DESC
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let reports: Result<Vec<_>, _> = rows.into_iter().map(|row| {
            Ok(serde_json::json!({
                "id": row.get::<String, _>("id"),
                "name": row.get::<String, _>("name"),
                "type": row.get::<String, _>("type"),
                "description": row.get::<Option<String>, _>("description"),
                "parameters": row.get::<String, _>("parameters"),
                "generatedBy": row.get::<String, _>("generated_by"),
                "generatedAt": row.get::<DateTime<Utc>, _>("generated_at"),
                "filePath": row.get::<Option<String>, _>("file_path"),
                "fileSize": row.get::<Option<i64>, _>("file_size"),
                "status": row.get::<String, _>("status"),
                "expiresAt": row.get::<Option<DateTime<Utc>>, _>("expires_at")
            }))
        }).collect();

        reports
    }

    pub async fn create_report(&self, report_data: serde_json::Value, generated_by: &str) -> Result<String, sqlx::Error> {
        let report_id = uuid::Uuid::new_v4().to_string();
        let now = Utc::now();
        
        let parameters_json = serde_json::to_string(&report_data).unwrap_or_else(|_| "{}".to_string());

        sqlx::query(
            r#"
            INSERT INTO reports
            (id, name, type, description, parameters, generated_by, generated_at, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&report_id)
        .bind(report_data.get("name").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(report_data.get("type").and_then(|v| v.as_str()).unwrap_or("analytics"))
        .bind(report_data.get("description").and_then(|v| v.as_str()))
        .bind(&parameters_json)
        .bind(generated_by)
        .bind(&now)
        .bind("generating")
        .execute(&self.pool)
        .await?;

        Ok(report_id)
    }

    pub async fn delete_report(&self, report_id: &str) -> Result<(), sqlx::Error> {
        sqlx::query("DELETE FROM reports WHERE id = ?")
            .bind(report_id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn get_trainee_analytics(&self) -> Result<serde_json::Value, sqlx::Error> {
        // Get trainee completion rates by program
        let program_stats = sqlx::query(
            r#"
            SELECT
                tp.name as program_name,
                COUNT(t.id) as total_trainees,
                COUNT(CASE WHEN a.status = 'completed' THEN 1 END) as completed_trainees
            FROM training_programs tp
            LEFT JOIN trainees t ON tp.id = t.training_program_id
            LEFT JOIN assessments a ON t.id = a.trainee_id
            GROUP BY tp.id, tp.name
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let mut programs = serde_json::Map::new();
        for row in program_stats {
            let name: String = row.get("program_name");
            let total: i64 = row.get("total_trainees");
            let completed: i64 = row.get("completed_trainees");
            let completion_rate = if total > 0 { completed as f64 / total as f64 * 100.0 } else { 0.0 };
            
            programs.insert(name, serde_json::json!({
                "total": total,
                "completed": completed,
                "completionRate": completion_rate
            }));
        }

        // Get trainee distribution by college
        let college_stats = sqlx::query(
            "SELECT college, COUNT(*) as count FROM trainees GROUP BY college"
        )
        .fetch_all(&self.pool)
        .await?;

        let mut colleges = serde_json::Map::new();
        for row in college_stats {
            let college: String = row.get("college");
            let count: i64 = row.get("count");
            colleges.insert(college, serde_json::Value::Number(count.into()));
        }

        Ok(serde_json::json!({
            "programs": programs,
            "colleges": colleges
        }))
    }

    pub async fn get_assessment_analytics(&self) -> Result<serde_json::Value, sqlx::Error> {
        // Get assessment completion rates by quarter
        let quarterly_stats = sqlx::query(
            r#"
            SELECT
                quarter,
                year,
                COUNT(*) as total,
                COUNT(CASE WHEN status = 'completed' THEN 1 END) as completed,
                AVG(overall_score) as avg_score
            FROM assessments
            GROUP BY quarter, year
            ORDER BY year, quarter
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let mut quarterly = serde_json::Map::new();
        for row in quarterly_stats {
            let quarter: i32 = row.get("quarter");
            let year: i32 = row.get("year");
            let key = format!("Q{} {}", quarter, year);
            let total: i64 = row.get("total");
            let completed: i64 = row.get("completed");
            let avg_score: Option<f64> = row.get("avg_score");
            let completion_rate = if total > 0 { completed as f64 / total as f64 * 100.0 } else { 0.0 };
            
            quarterly.insert(key, serde_json::json!({
                "total": total,
                "completed": completed,
                "completionRate": completion_rate,
                "averageScore": avg_score.unwrap_or(0.0)
            }));
        }

        // Get score distribution
        let score_distribution = sqlx::query(
            r#"
            SELECT
                CASE
                    WHEN overall_score >= 90 THEN 'Excellent (90-100)'
                    WHEN overall_score >= 80 THEN 'Good (80-89)'
                    WHEN overall_score >= 70 THEN 'Average (70-79)'
                    WHEN overall_score >= 60 THEN 'Below Average (60-69)'
                    ELSE 'Poor (<60)'
                END as score_range,
                COUNT(*) as count
            FROM assessments
            WHERE overall_score IS NOT NULL
            GROUP BY score_range
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let mut scores = serde_json::Map::new();
        for row in score_distribution {
            let range: String = row.get("score_range");
            let count: i64 = row.get("count");
            scores.insert(range, serde_json::Value::Number(count.into()));
        }

        Ok(serde_json::json!({
            "quarterly": quarterly,
            "scoreDistribution": scores
        }))
    }

    // Training Budget Management Methods
    
    pub async fn get_training_types(&self) -> Result<Vec<TrainingType>, sqlx::Error> {
        sqlx::query_as("SELECT * FROM training_types ORDER BY name")
            .fetch_all(&self.pool)
            .await
    }

    pub async fn get_training_budgets(&self) -> Result<Vec<serde_json::Value>, sqlx::Error> {
        let rows = sqlx::query(
            r#"
            SELECT 
                tb.id,
                tb.title,
                tb.description,
                tb.training_type_id,
                tb.total_budget,
                tb.currency,
                tb.start_date,
                tb.end_date,
                tb.status,
                tb.location,
                tb.provider,
                tb.created_by,
                tb.created_at,
                tb.updated_at,
                tt.name as training_type_name
            FROM training_budgets tb
            JOIN training_types tt ON tb.training_type_id = tt.id
            ORDER BY tb.created_at DESC
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let budgets: Vec<serde_json::Value> = rows
            .into_iter()
            .map(|row| {
                serde_json::json!({
                    "id": row.get::<String, _>("id"),
                    "title": row.get::<String, _>("title"),
                    "description": row.get::<Option<String>, _>("description"),
                    "trainingTypeId": row.get::<String, _>("training_type_id"),
                    "totalBudget": row.get::<f64, _>("total_budget"),
                    "currency": row.get::<String, _>("currency"),
                    "startDate": row.get::<DateTime<Utc>, _>("start_date"),
                    "endDate": row.get::<DateTime<Utc>, _>("end_date"),
                    "status": row.get::<String, _>("status"),
                    "location": row.get::<Option<String>, _>("location"),
                    "provider": row.get::<Option<String>, _>("provider"),
                    "createdBy": row.get::<String, _>("created_by"),
                    "createdAt": row.get::<DateTime<Utc>, _>("created_at"),
                    "updatedAt": row.get::<DateTime<Utc>, _>("updated_at"),
                    "trainingType": {
                        "id": row.get::<String, _>("training_type_id"),
                        "name": row.get::<String, _>("training_type_name")
                    }
                })
            })
            .collect();

        Ok(budgets)
    }

    pub async fn get_training_budget(&self, budget_id: &str) -> Result<Option<serde_json::Value>, sqlx::Error> {
        // Base budget row with training type metadata
        let row_opt = sqlx::query(
            r#"
            SELECT 
                tb.id,
                tb.title,
                tb.description,
                tb.training_type_id,
                tb.total_budget,
                tb.currency,
                tb.start_date,
                tb.end_date,
                tb.status,
                tb.location,
                tb.provider,
                tb.created_by,
                tb.created_at,
                tb.updated_at,
                tt.name as training_type_name
            FROM training_budgets tb
            JOIN training_types tt ON tb.training_type_id = tt.id
            WHERE tb.id = ?
            "#
        )
        .bind(budget_id)
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = row_opt {
            // Budget items
            let items_rows = sqlx::query(
                "SELECT * FROM training_budget_items WHERE budget_id = ? ORDER BY created_at"
            )
            .bind(budget_id)
            .fetch_all(&self.pool)
            .await?;

            let budget_items: Vec<serde_json::Value> = items_rows.into_iter().map(|item_row| {
                serde_json::json!({
                    "id": item_row.get::<String, _>("id"),
                    "budgetId": item_row.get::<String, _>("budget_id"),
                    "category": item_row.get::<String, _>("category"),
                    "itemName": item_row.get::<String, _>("item_name"),
                    "estimatedCost": item_row.get::<f64, _>("estimated_cost"),
                    "actualCost": item_row.get::<Option<f64>, _>("actual_cost"),
                    "notes": item_row.get::<Option<String>, _>("notes"),
                    "createdAt": item_row.get::<DateTime<Utc>, _>("created_at"),
                    "updatedAt": item_row.get::<DateTime<Utc>, _>("updated_at")
                })
            }).collect();

            // Schedules with attendees count
            let schedules_rows = sqlx::query(
                r#"
                SELECT 
                    ts.*, 
                    COUNT(ta.id) as attendees_count
                FROM training_schedule ts
                LEFT JOIN training_attendees ta ON ts.id = ta.schedule_id
                WHERE ts.budget_id = ?
                GROUP BY ts.id
                ORDER BY ts.start_datetime
                "#
            )
            .bind(budget_id)
            .fetch_all(&self.pool)
            .await?;

            let schedules: Vec<serde_json::Value> = schedules_rows.into_iter().map(|schedule_row| {
                serde_json::json!({
                    "id": schedule_row.get::<String, _>("id"),
                    "budgetId": schedule_row.get::<String, _>("budget_id"),
                    "title": schedule_row.get::<String, _>("title"),
                    "description": schedule_row.get::<Option<String>, _>("description"),
                    "startDatetime": schedule_row.get::<DateTime<Utc>, _>("start_datetime"),
                    "endDatetime": schedule_row.get::<DateTime<Utc>, _>("end_datetime"),
                    "location": schedule_row.get::<Option<String>, _>("location"),
                    "instructor": schedule_row.get::<Option<String>, _>("instructor"),
                    "maxParticipants": schedule_row.get::<Option<i32>, _>("max_participants"),
                    "status": schedule_row.get::<String, _>("status"),
                    "createdAt": schedule_row.get::<DateTime<Utc>, _>("created_at"),
                    "updatedAt": schedule_row.get::<DateTime<Utc>, _>("updated_at"),
                    "attendeesCount": schedule_row.get::<i64, _>("attendees_count")
                })
            }).collect();

            // Approvals
            let approvals_rows = sqlx::query(
                r#"
                SELECT 
                    ba.*
                FROM budget_approvals ba
                WHERE ba.budget_id = ?
                ORDER BY ba.created_at
                "#
            )
            .bind(budget_id)
            .fetch_all(&self.pool)
            .await?;

            let approvals: Vec<serde_json::Value> = approvals_rows.into_iter().map(|approval_row| {
                serde_json::json!({
                    "id": approval_row.get::<String, _>("id"),
                    "budgetId": approval_row.get::<String, _>("budget_id"),
                    "approverId": approval_row.get::<String, _>("approver_id"),
                    "status": approval_row.get::<String, _>("status"),
                    "approvalDate": approval_row.get::<Option<DateTime<Utc>>, _>("approval_date"),
                    "comments": approval_row.get::<Option<String>, _>("comments"),
                    "createdAt": approval_row.get::<DateTime<Utc>, _>("created_at"),
                    "updatedAt": approval_row.get::<DateTime<Utc>, _>("updated_at")
                })
            }).collect();

            let budget = serde_json::json!({
                "id": row.get::<String, _>("id"),
                "title": row.get::<String, _>("title"),
                "description": row.get::<Option<String>, _>("description"),
                "trainingTypeId": row.get::<String, _>("training_type_id"),
                "totalBudget": row.get::<f64, _>("total_budget"),
                "currency": row.get::<String, _>("currency"),
                "startDate": row.get::<DateTime<Utc>, _>("start_date"),
                "endDate": row.get::<DateTime<Utc>, _>("end_date"),
                "status": row.get::<String, _>("status"),
                "location": row.get::<Option<String>, _>("location"),
                "provider": row.get::<Option<String>, _>("provider"),
                "createdBy": row.get::<String, _>("created_by"),
                "createdAt": row.get::<DateTime<Utc>, _>("created_at"),
                "updatedAt": row.get::<DateTime<Utc>, _>("updated_at"),
                "trainingType": {
                    "id": row.get::<String, _>("training_type_id"),
                    "name": row.get::<String, _>("training_type_name")
                },
                "budgetItems": budget_items,
                "schedules": schedules,
                "approvals": approvals
            });

            Ok(Some(budget))
        } else {
            Ok(None)
        }
    }

    pub async fn create_training_budget(&self, budget_data: serde_json::Value, created_by: &str) -> Result<String, sqlx::Error> {
        let budget_id = uuid::Uuid::new_v4().to_string();
        let now = Utc::now();

        sqlx::query(
            r#"
            INSERT INTO training_budgets 
            (id, title, description, training_type_id, total_budget, currency, start_date, end_date, status, location, provider, created_by, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&budget_id)
        .bind(budget_data.get("title").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(budget_data.get("description").and_then(|v| v.as_str()))
        .bind(budget_data.get("trainingTypeId").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(budget_data.get("totalBudget").and_then(|v| v.as_f64()).unwrap_or(0.0))
        .bind(budget_data.get("currency").and_then(|v| v.as_str()).unwrap_or("USD"))
        .bind(budget_data.get("startDate").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(budget_data.get("endDate").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(budget_data.get("status").and_then(|v| v.as_str()).unwrap_or("planned"))
        .bind(budget_data.get("location").and_then(|v| v.as_str()))
        .bind(budget_data.get("provider").and_then(|v| v.as_str()))
        .bind(created_by)
        .bind(&now)
        .bind(&now)
        .execute(&self.pool)
        .await?;

        // Add budget items if provided
        if let Some(items) = budget_data.get("budgetItems").and_then(|v| v.as_array()) {
            for item in items {
                let item_id = uuid::Uuid::new_v4().to_string();
                sqlx::query(
                    "INSERT INTO training_budget_items (id, budget_id, category, item_name, estimated_cost, notes, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
                )
                .bind(&item_id)
                .bind(&budget_id)
                .bind(item.get("category").and_then(|v| v.as_str()).unwrap_or("other"))
                .bind(item.get("itemName").and_then(|v| v.as_str()).unwrap_or(""))
                .bind(item.get("estimatedCost").and_then(|v| v.as_f64()).unwrap_or(0.0))
                .bind(item.get("notes").and_then(|v| v.as_str()))
                .bind(&now)
                .bind(&now)
                .execute(&self.pool)
                .await?;
            }
        }

        Ok(budget_id)
    }

    pub async fn update_training_budget(&self, budget_id: &str, budget_data: serde_json::Value) -> Result<(), sqlx::Error> {
        let now = Utc::now();

        sqlx::query(
            r#"
            UPDATE training_budgets
            SET title = ?, description = ?, training_type_id = ?, total_budget = ?, currency = ?,
                start_date = ?, end_date = ?, status = ?, location = ?, provider = ?, updated_at = ?
            WHERE id = ?
            "#
        )
        .bind(budget_data.get("title").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(budget_data.get("description").and_then(|v| v.as_str()))
        .bind(budget_data.get("trainingTypeId").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(budget_data.get("totalBudget").and_then(|v| v.as_f64()).unwrap_or(0.0))
        .bind(budget_data.get("currency").and_then(|v| v.as_str()).unwrap_or("USD"))
        .bind(budget_data.get("startDate").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(budget_data.get("endDate").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(budget_data.get("status").and_then(|v| v.as_str()).unwrap_or("planned"))
        .bind(budget_data.get("location").and_then(|v| v.as_str()))
        .bind(budget_data.get("provider").and_then(|v| v.as_str()))
        .bind(&now)
        .bind(budget_id)
        .execute(&self.pool)
        .await?;

        // Update budget items if provided
        if let Some(items) = budget_data.get("budgetItems").and_then(|v| v.as_array()) {
            // Remove existing items
            sqlx::query("DELETE FROM training_budget_items WHERE budget_id = ?")
                .bind(budget_id)
                .execute(&self.pool)
                .await?;

            // Add new items
            for item in items {
                let item_id = uuid::Uuid::new_v4().to_string();
                sqlx::query(
                    "INSERT INTO training_budget_items (id, budget_id, category, item_name, estimated_cost, actual_cost, notes, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)"
                )
                .bind(&item_id)
                .bind(budget_id)
                .bind(item.get("category").and_then(|v| v.as_str()).unwrap_or("other"))
                .bind(item.get("itemName").and_then(|v| v.as_str()).unwrap_or(""))
                .bind(item.get("estimatedCost").and_then(|v| v.as_f64()).unwrap_or(0.0))
                .bind(item.get("actualCost").and_then(|v| v.as_f64()))
                .bind(item.get("notes").and_then(|v| v.as_str()))
                .bind(&now)
                .bind(&now)
                .execute(&self.pool)
                .await?;
            }
        }

        Ok(())
    }

    pub async fn delete_training_budget(&self, budget_id: &str) -> Result<(), sqlx::Error> {
        // Delete related records first (due to foreign key constraints)
        sqlx::query("DELETE FROM budget_approvals WHERE budget_id = ?")
            .bind(budget_id)
            .execute(&self.pool)
            .await?;

        sqlx::query("DELETE FROM training_attendees WHERE schedule_id IN (SELECT id FROM training_schedule WHERE budget_id = ?)")
            .bind(budget_id)
            .execute(&self.pool)
            .await?;

        sqlx::query("DELETE FROM training_schedule WHERE budget_id = ?")
            .bind(budget_id)
            .execute(&self.pool)
            .await?;

        sqlx::query("DELETE FROM training_budget_items WHERE budget_id = ?")
            .bind(budget_id)
            .execute(&self.pool)
            .await?;

        // Delete the budget
        sqlx::query("DELETE FROM training_budgets WHERE id = ?")
            .bind(budget_id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn get_training_schedule(&self, schedule_id: &str) -> Result<Option<serde_json::Value>, sqlx::Error> {
        let schedule_row = sqlx::query(
            r#"
            SELECT 
                ts.*,
                tb.title as budget_title,
                tt.name as training_type_name
            FROM training_schedule ts
            JOIN training_budgets tb ON ts.budget_id = tb.id
            JOIN training_types tt ON tb.training_type_id = tt.id
            WHERE ts.id = ?
            "#
        )
        .bind(schedule_id)
        .fetch_optional(&self.pool)
        .await?;

        if let Some(row) = schedule_row {
            // Get attendees
            let attendees_rows = sqlx::query(
                r#"
                SELECT 
                    ta.*
                FROM training_attendees ta
                WHERE ta.schedule_id = ?
                ORDER BY ta.registration_date
                "#
            )
            .bind(schedule_id)
            .fetch_all(&self.pool)
            .await?;

            let attendees: Vec<serde_json::Value> = attendees_rows.into_iter().map(|attendee_row| {
                serde_json::json!({
                    "id": attendee_row.get::<String, _>("id"),
                    "scheduleId": attendee_row.get::<String, _>("schedule_id"),
                    "userId": attendee_row.get::<String, _>("user_id"),
                    "registrationStatus": attendee_row.get::<String, _>("registration_status"),
                    "registrationDate": attendee_row.get::<DateTime<Utc>, _>("registration_date"),
                    "notes": attendee_row.get::<Option<String>, _>("notes"),
                    "createdAt": attendee_row.get::<DateTime<Utc>, _>("created_at"),
                    "updatedAt": attendee_row.get::<DateTime<Utc>, _>("updated_at"),
                    "userId": attendee_row.get::<String, _>("user_id")
                })
            }).collect();

            let schedule = serde_json::json!({
                "id": row.get::<String, _>("id"),
                "budgetId": row.get::<String, _>("budget_id"),
                "title": row.get::<String, _>("title"),
                "description": row.get::<Option<String>, _>("description"),
                "startDatetime": row.get::<DateTime<Utc>, _>("start_datetime"),
                "endDatetime": row.get::<DateTime<Utc>, _>("end_datetime"),
                "location": row.get::<Option<String>, _>("location"),
                "instructor": row.get::<Option<String>, _>("instructor"),
                "maxParticipants": row.get::<Option<i32>, _>("max_participants"),
                "status": row.get::<String, _>("status"),
                "createdAt": row.get::<DateTime<Utc>, _>("created_at"),
                "updatedAt": row.get::<DateTime<Utc>, _>("updated_at"),
                "budget": {
                    "id": row.get::<String, _>("budget_id"),
                    "title": row.get::<String, _>("budget_title")
                },
                "trainingType": {
                    "name": row.get::<String, _>("training_type_name")
                },
                "attendees": attendees
            });

            Ok(Some(schedule))
        } else {
            Ok(None)
        }
    }

    pub async fn create_training_schedule(&self, schedule_data: serde_json::Value, budget_id: &str) -> Result<String, sqlx::Error> {
        let schedule_id = uuid::Uuid::new_v4().to_string();
        let now = Utc::now();

        sqlx::query(
            r#"
            INSERT INTO training_schedule 
            (id, budget_id, title, description, start_datetime, end_datetime, location, instructor, max_participants, status, created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            "#
        )
        .bind(&schedule_id)
        .bind(budget_id)
        .bind(schedule_data.get("title").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(schedule_data.get("description").and_then(|v| v.as_str()))
        .bind(schedule_data.get("startDatetime").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(schedule_data.get("endDatetime").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(schedule_data.get("location").and_then(|v| v.as_str()))
        .bind(schedule_data.get("instructor").and_then(|v| v.as_str()))
        .bind(schedule_data.get("maxParticipants").and_then(|v| v.as_i64()))
        .bind(schedule_data.get("status").and_then(|v| v.as_str()).unwrap_or("scheduled"))
        .bind(&now)
        .bind(&now)
        .execute(&self.pool)
        .await?;

        // Add attendees if provided
        if let Some(attendees) = schedule_data.get("attendees").and_then(|v| v.as_array()) {
            for attendee in attendees {
                let attendee_id = uuid::Uuid::new_v4().to_string();
                sqlx::query(
                    "INSERT INTO training_attendees (id, schedule_id, user_id, registration_status, registration_date, notes, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
                )
                .bind(&attendee_id)
                .bind(&schedule_id)
                .bind(attendee.get("userId").and_then(|v| v.as_str()).unwrap_or(""))
                .bind(attendee.get("registrationStatus").and_then(|v| v.as_str()).unwrap_or("registered"))
                .bind(&now)
                .bind(attendee.get("notes").and_then(|v| v.as_str()))
                .bind(&now)
                .bind(&now)
                .execute(&self.pool)
                .await?;
            }
        }

        Ok(schedule_id)
    }

    pub async fn update_training_schedule(&self, schedule_id: &str, schedule_data: serde_json::Value) -> Result<(), sqlx::Error> {
        let now = Utc::now();

        sqlx::query(
            r#"
            UPDATE training_schedule
            SET title = ?, description = ?, start_datetime = ?, end_datetime = ?, location = ?, instructor = ?, max_participants = ?, status = ?, updated_at = ?
            WHERE id = ?
            "#
        )
        .bind(schedule_data.get("title").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(schedule_data.get("description").and_then(|v| v.as_str()))
        .bind(schedule_data.get("startDatetime").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(schedule_data.get("endDatetime").and_then(|v| v.as_str()).unwrap_or(""))
        .bind(schedule_data.get("location").and_then(|v| v.as_str()))
        .bind(schedule_data.get("instructor").and_then(|v| v.as_str()))
        .bind(schedule_data.get("maxParticipants").and_then(|v| v.as_i64()))
        .bind(schedule_data.get("status").and_then(|v| v.as_str()).unwrap_or("scheduled"))
        .bind(&now)
        .bind(schedule_id)
        .execute(&self.pool)
        .await?;

        // Update attendees if provided
        if let Some(attendees) = schedule_data.get("attendees").and_then(|v| v.as_array()) {
            // Remove existing attendees
            sqlx::query("DELETE FROM training_attendees WHERE schedule_id = ?")
                .bind(schedule_id)
                .execute(&self.pool)
                .await?;

            // Add new attendees
            for attendee in attendees {
                let attendee_id = uuid::Uuid::new_v4().to_string();
                sqlx::query(
                    "INSERT INTO training_attendees (id, schedule_id, user_id, registration_status, registration_date, notes, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?)"
                )
                .bind(&attendee_id)
                .bind(schedule_id)
                .bind(attendee.get("userId").and_then(|v| v.as_str()).unwrap_or(""))
                .bind(attendee.get("registrationStatus").and_then(|v| v.as_str()).unwrap_or("registered"))
                .bind(&now)
                .bind(attendee.get("notes").and_then(|v| v.as_str()))
                .bind(&now)
                .bind(&now)
                .execute(&self.pool)
                .await?;
            }
        }

        Ok(())
    }

    pub async fn delete_training_schedule(&self, schedule_id: &str) -> Result<(), sqlx::Error> {
        // Delete attendees first
        sqlx::query("DELETE FROM training_attendees WHERE schedule_id = ?")
            .bind(schedule_id)
            .execute(&self.pool)
            .await?;

        // Delete the schedule
        sqlx::query("DELETE FROM training_schedule WHERE id = ?")
            .bind(schedule_id)
            .execute(&self.pool)
            .await?;

        Ok(())
    }

    pub async fn get_users_for_attendee_selection(&self) -> Result<Vec<serde_json::Value>, sqlx::Error> {
        // Query user information from the trainees table since users table was removed
        let users = sqlx::query("SELECT DISTINCT user_name, user_email, user_role FROM trainees ORDER BY user_name ASC")
            .fetch_all(&self.pool)
            .await?;

        let result: Result<Vec<_>, _> = users.into_iter().map(|row| {
            Ok(serde_json::json!({
                "id": row.get::<String, _>("user_email"), // Use email as ID since it's unique
                "name": row.get::<String, _>("user_name"),
                "email": row.get::<String, _>("user_email"),
                "role": row.get::<String, _>("user_role")
            }))
        }).collect();

        result
    }

    pub async fn get_training_budget_analytics(&self) -> Result<serde_json::Value, sqlx::Error> {
        // Get overall totals
        let totals = sqlx::query(
            r#"
            SELECT
                SUM(total_budget) as total_allocated,
                SUM(COALESCE(tbi_total.actual_cost, 0)) as total_spent,
                SUM(COALESCE(tbi_total.estimated_cost, 0)) as total_utilized
            FROM training_budgets tb
            LEFT JOIN (
                SELECT budget_id, SUM(estimated_cost) as estimated_cost, SUM(actual_cost) as actual_cost
                FROM training_budget_items
                GROUP BY budget_id
            ) tbi_total ON tb.id = tbi_total.budget_id
            "#
        )
        .fetch_one(&self.pool)
        .await?;

        let total_allocated = totals.get::<Option<f64>, _>("total_allocated").unwrap_or(0.0);
        let total_spent = totals.get::<Option<f64>, _>("total_spent").unwrap_or(0.0);
        let total_utilized = totals.get::<Option<f64>, _>("total_utilized").unwrap_or(0.0);

        // Get budget summary by training type
        let type_stats = sqlx::query(
            r#"
            SELECT
                tt.name as training_type_name,
                COUNT(tb.id) as budget_count,
                SUM(tb.total_budget) as total_budget_allocated,
                SUM(COALESCE(tbi_total.estimated_cost, 0)) as total_estimated_cost,
                SUM(COALESCE(tbi_total.actual_cost, 0)) as total_actual_cost
            FROM training_budgets tb
            JOIN training_types tt ON tb.training_type_id = tt.id
            LEFT JOIN (
                SELECT budget_id, SUM(estimated_cost) as estimated_cost, SUM(actual_cost) as actual_cost
                FROM training_budget_items
                GROUP BY budget_id
            ) tbi_total ON tb.id = tbi_total.budget_id
            GROUP BY tt.id, tt.name
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let mut by_type = serde_json::Map::new();
        for row in type_stats {
            let type_name: String = row.get("training_type_name");
            by_type.insert(type_name, serde_json::json!({
                "budgetCount": row.get::<i64, _>("budget_count"),
                "totalBudgetAllocated": row.get::<Option<f64>, _>("total_budget_allocated").unwrap_or(0.0),
                "totalEstimatedCost": row.get::<Option<f64>, _>("total_estimated_cost").unwrap_or(0.0),
                "totalActualCost": row.get::<Option<f64>, _>("total_actual_cost").unwrap_or(0.0)
            }));
        }

        // Get budget status distribution
        let status_stats = sqlx::query(
            "SELECT status, COUNT(*) as budget_count, SUM(total_budget) as total_budget FROM training_budgets GROUP BY status"
        )
        .fetch_all(&self.pool)
        .await?;

        let mut by_status = serde_json::Map::new();
        for row in status_stats {
            let status: String = row.get("status");
            by_status.insert(status, serde_json::json!({
                "budgetCount": row.get::<i64, _>("budget_count"),
                "totalBudget": row.get::<Option<f64>, _>("total_budget").unwrap_or(0.0)
            }));
        }

        // Get monthly budget trends
        let monthly_trends = sqlx::query(
            r#"
            SELECT
                strftime('%Y-%m', start_date) as month,
                COUNT(*) as budget_count,
                SUM(total_budget) as total_budget
            FROM training_budgets
            GROUP BY strftime('%Y-%m', start_date)
            ORDER BY month DESC
            LIMIT 12
            "#
        )
        .fetch_all(&self.pool)
        .await?;

        let mut trends = serde_json::Map::new();
        for row in monthly_trends {
            let month: String = row.get("month");
            trends.insert(month, serde_json::json!({
                "budgetCount": row.get::<i64, _>("budget_count"),
                "totalBudget": row.get::<Option<f64>, _>("total_budget").unwrap_or(0.0)
            }));
        }

        Ok(serde_json::json!({
            "totalAllocated": total_allocated,
            "totalSpent": total_spent,
            "totalUtilized": total_utilized,
            "totalEstimatedCost": total_utilized,
            "totalActualCost": total_spent,
            "remainingBudget": total_allocated - total_utilized,
            "byType": by_type,
            "byStatus": by_status,
            "monthlyTrends": trends
        }))
    }

    // ========== User Management ==========

    pub async fn get_users(&self) -> Result<Vec<serde_json::Value>, sqlx::Error> {
        let rows = sqlx::query(
            "SELECT id, name, email, role, status, last_login, created_at, updated_at 
             FROM users 
             ORDER BY created_at DESC"
        )
        .fetch_all(&self.pool)
        .await?;

        Ok(rows
            .iter()
            .map(|row| {
                serde_json::json!({
                    "id": row.get::<String, _>("id"),
                    "name": row.get::<String, _>("name"),
                    "email": row.get::<String, _>("email"),
                    "role": row.get::<String, _>("role"),
                    "status": row.get::<String, _>("status"),
                    "lastLogin": row.get::<Option<String>, _>("last_login"),
                    "createdAt": row.get::<String, _>("created_at"),
                    "updatedAt": row.get::<String, _>("updated_at")
                })
            })
            .collect())
    }

    pub async fn get_user(&self, id: &str) -> Result<Option<serde_json::Value>, sqlx::Error> {
        let row = sqlx::query(
            "SELECT id, name, email, role, status, last_login, created_at, updated_at 
             FROM users 
             WHERE id = ?"
        )
        .bind(id)
        .fetch_optional(&self.pool)
        .await?;

        Ok(row.map(|row| {
            serde_json::json!({
                "id": row.get::<String, _>("id"),
                "name": row.get::<String, _>("name"),
                "email": row.get::<String, _>("email"),
                "role": row.get::<String, _>("role"),
                "status": row.get::<String, _>("status"),
                "lastLogin": row.get::<Option<String>, _>("last_login"),
                "createdAt": row.get::<String, _>("created_at"),
                "updatedAt": row.get::<String, _>("updated_at")
            })
        }))
    }

    pub async fn create_user(&self, user: serde_json::Value) -> Result<serde_json::Value, sqlx::Error> {
        let id = user.get("id").and_then(|v| v.as_str()).unwrap_or("");
        let name = user.get("name").and_then(|v| v.as_str()).unwrap_or("");
        let email = user.get("email").and_then(|v| v.as_str()).unwrap_or("");
        let role = user.get("role").and_then(|v| v.as_str()).unwrap_or("trainee");
        let status = user.get("status").and_then(|v| v.as_str()).unwrap_or("active");

        sqlx::query(
            "INSERT INTO users (id, name, email, role, status, created_at, updated_at)
             VALUES (?, ?, ?, ?, ?, datetime('now'), datetime('now'))"
        )
        .bind(id)
        .bind(name)
        .bind(email)
        .bind(role)
        .bind(status)
        .execute(&self.pool)
        .await?;

        self.get_user(id).await.map(|opt| opt.unwrap_or(serde_json::json!({})))
    }

    pub async fn update_user(&self, id: &str, user: serde_json::Value) -> Result<serde_json::Value, sqlx::Error> {
        let name = user.get("name").and_then(|v| v.as_str()).unwrap_or("");
        let email = user.get("email").and_then(|v| v.as_str()).unwrap_or("");
        let role = user.get("role").and_then(|v| v.as_str()).unwrap_or("trainee");
        let status = user.get("status").and_then(|v| v.as_str()).unwrap_or("active");

        sqlx::query(
            "UPDATE users 
             SET name = ?, email = ?, role = ?, status = ?, updated_at = datetime('now')
             WHERE id = ?"
        )
        .bind(name)
        .bind(email)
        .bind(role)
        .bind(status)
        .bind(id)
        .execute(&self.pool)
        .await?;

        self.get_user(id).await.map(|opt| opt.unwrap_or(serde_json::json!({})))
    }

    pub async fn delete_user(&self, id: &str) -> Result<bool, sqlx::Error> {
        let result = sqlx::query("DELETE FROM users WHERE id = ?")
            .bind(id)
            .execute(&self.pool)
            .await?;

        Ok(result.rows_affected() > 0)
    }

    pub async fn get_users_by_role(&self, role: &str) -> Result<Vec<serde_json::Value>, sqlx::Error> {
        let rows = sqlx::query(
            "SELECT id, name, email, role, status, last_login, created_at, updated_at 
             FROM users 
             WHERE role = ?
             ORDER BY name"
        )
        .bind(role)
        .fetch_all(&self.pool)
        .await?;

        Ok(rows
            .iter()
            .map(|row| {
                serde_json::json!({
                    "id": row.get::<String, _>("id"),
                    "name": row.get::<String, _>("name"),
                    "email": row.get::<String, _>("email"),
                    "role": row.get::<String, _>("role"),
                    "status": row.get::<String, _>("status"),
                    "lastLogin": row.get::<Option<String>, _>("last_login"),
                    "createdAt": row.get::<String, _>("created_at"),
                    "updatedAt": row.get::<String, _>("updated_at")
                })
            })
            .collect())
    }

    // Create user with password hashing
    pub async fn create_user_with_password(&self, user_data: serde_json::Value) -> Result<serde_json::Value, sqlx::Error> {
        let email = user_data.get("email").and_then(|v| v.as_str()).unwrap_or("");
        let password = user_data.get("password").and_then(|v| v.as_str()).unwrap_or("");
        
        // Hash the password
        let password_hash = bcrypt::hash(password, bcrypt::DEFAULT_COST)
            .map_err(|e| sqlx::Error::Io(std::io::Error::new(std::io::ErrorKind::Other, e)))?;

        let mut secure_user_data = user_data.clone();
        secure_user_data.as_object_mut()
            .unwrap()
            .insert("passwordHash".to_string(), serde_json::Value::String(password_hash));

        self.create_user(secure_user_data).await
    }
}