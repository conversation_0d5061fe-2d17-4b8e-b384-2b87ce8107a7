// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

mod database;

use chrono::Utc;
use database::{
    Assessment, AuthCredentials, AuthResponse, Database, Trainee, TrainingProgram, TrainingType,
};
use std::sync::Arc;
use tauri::{Manager, State};
use uuid::Uuid;

pub struct AppState {
    pub db: Arc<Database>,
}

#[tauri::command]
async fn ping() -> Result<String, String> {
    Ok("pong".to_string())
}

#[tauri::command]
async fn initialize_database(_state: State<'_, AppState>) -> Result<String, String> {
    // Database is already initialized in setup, but we can run any additional initialization here
    Ok("Database initialized successfully".to_string())
}

#[tauri::command]
async fn get_dashboard_stats(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    state
        .db
        .get_dashboard_stats()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_trainees(state: State<'_, AppState>) -> Result<Vec<Trainee>, String> {
    state.db.get_trainees().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn save_trainee(trainee: Trainee, state: State<'_, AppState>) -> Result<(), String> {
    state
        .db
        .save_trainee(trainee)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_assessments(
    trainee_id: Option<String>,
    state: State<'_, AppState>,
) -> Result<Vec<Assessment>, String> {
    state
        .db
        .get_assessments(trainee_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn save_assessment(assessment: Assessment, state: State<'_, AppState>) -> Result<(), String> {
    state
        .db
        .save_assessment(assessment)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn export_report(format: String, state: State<'_, AppState>) -> Result<String, String> {
    let stats = state
        .db
        .get_dashboard_stats()
        .await
        .map_err(|e| e.to_string())?;

    // Create export data
    let export_data = serde_json::json!({
        "exportDate": chrono::Utc::now().to_rfc3339(),
        "format": format,
        "data": stats
    });

    match format.as_str() {
        "json" => Ok(export_data.to_string()),
        "csv" => {
            // Simple CSV export
            let mut csv = String::new();
            csv.push_str("Metric,Value\n");
            if let Some(obj) = stats.as_object() {
                for (key, value) in obj {
                    if let Some(num) = value.as_i64() {
                        csv.push_str(&format!("{},{}\n", key, num));
                    }
                }
            }
            Ok(csv)
        }
        _ => Err("Unsupported format".to_string()),
    }
}



// Training Programs
#[tauri::command]
async fn get_training_programs(state: State<'_, AppState>) -> Result<Vec<TrainingProgram>, String> {
    state
        .db
        .get_training_programs()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn save_training_program(
    program: TrainingProgram,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state
        .db
        .save_training_program(&program)
        .await
        .map_err(|e| e.to_string())
}

// Enhanced data endpoints
#[tauri::command]
async fn get_trainees_with_details(
    state: State<'_, AppState>,
) -> Result<Vec<serde_json::Value>, String> {
    state
        .db
        .get_trainees_with_details()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_assessments_with_details(
    trainee_id: Option<String>,
    state: State<'_, AppState>,
) -> Result<Vec<serde_json::Value>, String> {
    state
        .db
        .get_assessments_with_details(trainee_id)
        .await
        .map_err(|e| e.to_string())
}

// User Authentication
#[tauri::command]
async fn login(
    credentials: AuthCredentials,
    state: State<'_, AppState>,
) -> Result<AuthResponse, String> {
    let user = state
        .db
        .authenticate_user(&credentials.email, &credentials.password)
        .await
        .map_err(|e| e.to_string())?;

    match user {
        Some(user) => {
            // In a real app, generate a proper JWT token
            let token = format!("token_{}", Uuid::new_v4());
            let expires_at = Utc::now() + chrono::Duration::hours(24);

            Ok(AuthResponse {
                token,
                user,
                expires_at,
            })
        }
        None => Err("Invalid credentials".to_string()),
    }
}

#[tauri::command]
async fn logout(_token: String, _state: State<'_, AppState>) -> Result<(), String> {
    // In a real app, invalidate the token
    Ok(())
}

#[tauri::command]
async fn get_current_user(_state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    // For now, return a default admin user
    // In a real app, this would get the currently authenticated user from the token
    Ok(serde_json::json!({
        "id": "user-5",
        "name": "Charlie Wilson",
        "email": "<EMAIL>",
        "role": "ld_officer"
    }))
}

// Support Teams endpoints
#[tauri::command]
async fn get_support_teams(state: State<'_, AppState>) -> Result<Vec<serde_json::Value>, String> {
    state
        .db
        .get_support_teams()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_support_team(
    team_id: String,
    state: State<'_, AppState>,
) -> Result<Option<serde_json::Value>, String> {
    state
        .db
        .get_support_team(&team_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn create_support_team(
    team_data: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<String, String> {
    // Get current user ID from team data or use a default admin ID
    let created_by = team_data.get("createdBy")
        .and_then(|v| v.as_str())
        .unwrap_or("admin-1");

    state
        .db
        .create_support_team(team_data.clone(), created_by)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn update_support_team(
    team_id: String,
    team_data: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state
        .db
        .update_support_team(&team_id, team_data)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn delete_support_team(team_id: String, state: State<'_, AppState>) -> Result<(), String> {
    state
        .db
        .delete_support_team(&team_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_support_team_stats(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    // For now, return basic stats. In a full implementation, you'd add this method to the database
    let teams = state
        .db
        .get_support_teams()
        .await
        .map_err(|e| e.to_string())?;

    let total_teams = teams.len() as i64;
    let mut teams_with_members = 0;
    let mut teams_with_programs = 0;
    let mut total_members = 0;

    for team in &teams {
        if let Some(member_count) = team.get("memberCount").and_then(|v| v.as_i64()) {
            if member_count > 0 {
                teams_with_members += 1;
                total_members += member_count;
            }
        }
        // For now, assume all teams have programs if they have any data
        if team.get("name").is_some() {
            teams_with_programs += 1;
        }
    }

    Ok(serde_json::json!({
        "totalTeams": total_teams,
        "teamsWithMembers": teams_with_members,
        "teamsWithPrograms": teams_with_programs,
        "totalMembers": total_members,
        "totalPrograms": total_teams // Simplified
    }))
}

#[tauri::command]
async fn assign_support_team_member(
    _team_id: String,
    _member_id: String,
    _state: State<'_, AppState>,
) -> Result<(), String> {
    // This would need to be implemented in the database layer
    // For now, return success
    Ok(())
}

#[tauri::command]
async fn remove_support_team_member(
    _team_id: String,
    _member_id: String,
    _state: State<'_, AppState>,
) -> Result<(), String> {
    // This would need to be implemented in the database layer
    // For now, return success
    Ok(())
}

#[tauri::command]
async fn assign_support_team_program(
    _team_id: String,
    _program_id: String,
    _state: State<'_, AppState>,
) -> Result<(), String> {
    // This would need to be implemented in the database layer
    // For now, return success
    Ok(())
}

#[tauri::command]
async fn remove_support_team_program(
    _team_id: String,
    _program_id: String,
    _state: State<'_, AppState>,
) -> Result<(), String> {
    // This would need to be implemented in the database layer
    // For now, return success
    Ok(())
}

// Reports endpoints
#[tauri::command]
async fn get_reports(state: State<'_, AppState>) -> Result<Vec<serde_json::Value>, String> {
    state.db.get_reports().await.map_err(|e| e.to_string())
}

#[tauri::command]
async fn generate_report(
    report_data: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<String, String> {
    // Get current user ID from report data or use a default admin ID
    let generated_by = report_data.get("generatedBy")
        .and_then(|v| v.as_str())
        .unwrap_or("admin-1");

    let report_id = state
        .db
        .create_report(report_data.clone(), generated_by)
        .await
        .map_err(|e| e.to_string())?;

    // Simulate report generation (in a real app, this would be done asynchronously)
    let report_id_clone = report_id.clone();
    tokio::spawn(async move {
        // Simulate processing time
        tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

        // In a real implementation, generate the actual report file here
        let _file_path = format!("/reports/{}.pdf", report_id_clone);
        let _file_size = 1024 * 50; // 50KB simulated file size

        // Update report status (you'd need to pass the database reference here)
        // For now, we'll just log the completion
        println!("Report {} generated successfully", report_id_clone);
    });

    Ok(report_id)
}

#[tauri::command]
async fn delete_report(report_id: String, state: State<'_, AppState>) -> Result<(), String> {
    state
        .db
        .delete_report(&report_id)
        .await
        .map_err(|e| e.to_string())
}

// Analytics endpoints
#[tauri::command]
async fn get_trainee_analytics(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    state
        .db
        .get_trainee_analytics()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_assessment_analytics(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    state
        .db
        .get_assessment_analytics()
        .await
        .map_err(|e| e.to_string())
}

// Training Budget Management endpoints
#[tauri::command]
async fn get_training_types(state: State<'_, AppState>) -> Result<Vec<TrainingType>, String> {
    state
        .db
        .get_training_types()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_training_budgets(state: State<'_, AppState>) -> Result<Vec<serde_json::Value>, String> {
    state
        .db
        .get_training_budgets()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_training_budget(
    budget_id: String,
    state: State<'_, AppState>,
) -> Result<Option<serde_json::Value>, String> {
    state
        .db
        .get_training_budget(&budget_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn create_training_budget(
    budget_data: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<String, String> {
    // Get current user ID from budget data or use a default admin ID
    let created_by = budget_data.get("createdBy")
        .and_then(|v| v.as_str())
        .unwrap_or("admin-1");

    state
        .db
        .create_training_budget(budget_data.clone(), created_by)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn update_training_budget(
    budget_id: String,
    budget_data: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state
        .db
        .update_training_budget(&budget_id, budget_data)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn delete_training_budget(
    budget_id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state
        .db
        .delete_training_budget(&budget_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_training_schedule(
    schedule_id: String,
    state: State<'_, AppState>,
) -> Result<Option<serde_json::Value>, String> {
    state
        .db
        .get_training_schedule(&schedule_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn create_training_schedule(
    schedule_data: serde_json::Value,
    budget_id: String,
    state: State<'_, AppState>,
) -> Result<String, String> {
    state
        .db
        .create_training_schedule(schedule_data, &budget_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn update_training_schedule(
    schedule_id: String,
    schedule_data: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state
        .db
        .update_training_schedule(&schedule_id, schedule_data)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn delete_training_schedule(
    schedule_id: String,
    state: State<'_, AppState>,
) -> Result<(), String> {
    state
        .db
        .delete_training_schedule(&schedule_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_users_for_attendee_selection(state: State<'_, AppState>) -> Result<Vec<serde_json::Value>, String> {
    state
        .db
        .get_users_for_attendee_selection()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_training_budget_analytics(state: State<'_, AppState>) -> Result<serde_json::Value, String> {
    state
        .db
        .get_training_budget_analytics()
        .await
        .map_err(|e| e.to_string())
}

// User Management endpoints
#[tauri::command]
async fn get_users(state: State<'_, AppState>) -> Result<Vec<serde_json::Value>, String> {
    state
        .db
        .get_users()
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_user(
    user_id: String,
    state: State<'_, AppState>,
) -> Result<Option<serde_json::Value>, String> {
    state
        .db
        .get_user(&user_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn create_user(
    user_data: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<serde_json::Value, String> {
    state
        .db
        .create_user(user_data)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn create_user_with_password(
    user_data: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<serde_json::Value, String> {
    state
        .db
        .create_user_with_password(user_data)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn update_user(
    user_id: String,
    user_data: serde_json::Value,
    state: State<'_, AppState>,
) -> Result<serde_json::Value, String> {
    state
        .db
        .update_user(&user_id, user_data)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn delete_user(
    user_id: String,
    state: State<'_, AppState>,
) -> Result<bool, String> {
    state
        .db
        .delete_user(&user_id)
        .await
        .map_err(|e| e.to_string())
}

#[tauri::command]
async fn get_users_by_role(
    role: String,
    state: State<'_, AppState>,
) -> Result<Vec<serde_json::Value>, String> {
    state
        .db
        .get_users_by_role(&role)
        .await
        .map_err(|e| e.to_string())
}

#[tokio::main]
async fn main() {
    tauri::Builder::default()
        .plugin(tauri_plugin_notification::init())
        .plugin(tauri_plugin_dialog::init())
        .plugin(tauri_plugin_fs::init())
        .plugin(tauri_plugin_window_state::Builder::new().build())
        .plugin(tauri_plugin_clipboard_manager::init())
        .plugin(tauri_plugin_shell::init())
        .plugin(tauri_plugin_global_shortcut::Builder::new().build())
        .plugin(tauri_plugin_os::init())
        .plugin(tauri_plugin_process::init())
        .setup(|app| {
            // Initialize database
            let app_data_dir = app
                .path()
                .app_data_dir()
                .expect("Failed to get app data dir");
            std::fs::create_dir_all(&app_data_dir).expect("Failed to create app data directory");

            let db_path = app_data_dir.join("gt_ega.db");

            let db = Arc::new(
                tokio::task::block_in_place(|| {
                    tokio::runtime::Handle::current().block_on(Database::new(db_path))
                })
                .expect("Failed to initialize database"),
            );

            app.manage(AppState { db });

            Ok(())
        })
        .invoke_handler(tauri::generate_handler![
            // System
            ping,
            initialize_database,
            // Dashboard
            get_dashboard_stats,
            // Authentication
            login,
            logout,
            get_current_user,
            // User Management
            get_users,
            get_user,
            create_user,
            create_user_with_password,
            update_user,
            delete_user,
            get_users_by_role,
            // Trainees
            get_trainees,
            save_trainee,
            get_trainees_with_details,
            // Assessments
            get_assessments,
            save_assessment,
            get_assessments_with_details,
            
            // Training Programs
            get_training_programs,
            save_training_program,
            // Support Teams
            get_support_teams,
            get_support_team,
            create_support_team,
            update_support_team,
            delete_support_team,
            get_support_team_stats,
            assign_support_team_member,
            remove_support_team_member,
            assign_support_team_program,
            remove_support_team_program,
            // Reports
            get_reports,
            generate_report,
            delete_report,
            export_report,
            // Training Budget Management
            get_training_types,
            get_training_budgets,
            get_training_budget,
            create_training_budget,
            update_training_budget,
            delete_training_budget,
            get_training_schedule,
            create_training_schedule,
            update_training_schedule,
            delete_training_schedule,
            get_users_for_attendee_selection,
            get_training_budget_analytics,
            // Analytics
            get_trainee_analytics,
            get_assessment_analytics
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
