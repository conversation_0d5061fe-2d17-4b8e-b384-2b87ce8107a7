{"rustc": 9783247635632824192, "features": "[\"common-controls-v6\", \"compression\", \"default\", \"dynamic-acl\", \"image\", \"image-ico\", \"image-png\", \"tauri-runtime-wry\", \"tray-icon\", \"webkit2gtk\", \"webview2-com\", \"wry\", \"x11\"]", "declared_features": "[\"common-controls-v6\", \"compression\", \"config-json5\", \"config-toml\", \"custom-protocol\", \"data-url\", \"default\", \"devtools\", \"dynamic-acl\", \"http-range\", \"image\", \"image-ico\", \"image-png\", \"isolation\", \"linux-libxdo\", \"macos-private-api\", \"macos-proxy\", \"native-tls\", \"native-tls-vendored\", \"objc-exception\", \"process-relaunch-dangerous-allow-symlink-macos\", \"protocol-asset\", \"rustls-tls\", \"specta\", \"tauri-runtime-wry\", \"test\", \"tracing\", \"tray-icon\", \"unstable\", \"uuid\", \"webkit2gtk\", \"webview-data-url\", \"webview2-com\", \"wry\", \"x11\"]", "target": 12223948975794516716, "profile": 8276155916380437441, "path": 237352587324949828, "deps": [[358260473257383719, "plist", false, 7538058883619081310], [1200537532907108615, "url<PERSON><PERSON>n", false, 5065912095014682876], [1260461579271933187, "serialize_to_javascript", false, 7176914702523119901], [1420036582921034023, "image", false, 13659385676148546469], [1605511829699343563, "objc2", false, 744096222267939731], [1852463361802237065, "anyhow", false, 16517032112576851435], [1967864351173319501, "muda", false, 8601122354882041448], [3331586631144870129, "getrandom", false, 2586547575641964922], [4143744114649553716, "raw_window_handle", false, 15256674939953401903], [4336745513838352383, "thiserror", false, 3311293667150545164], [5404511084185685755, "url", false, 4589935766087210441], [6537120525306722933, "tauri_macros", false, 1525175459314981834], [6803352382179706244, "percent_encoding", false, 9750790380584505443], [8427153362654230442, "build_script_build", false, 4078160040589342091], [8589231650440095114, "embed_plist", false, 14890864745413537922], [9010263965687315507, "http", false, 16066920122783708211], [9293239362693504808, "glob", false, 8113599901605468963], [9628989939628929789, "objc2_web_kit", false, 638823834629182395], [9859211262912517217, "objc2_foundation", false, 4727062294452664245], [9952368442187680820, "tauri_runtime_wry", false, 1538731654575428789], [10229185211513642314, "mime", false, 11892782129224539320], [10575598148575346675, "objc2_app_kit", false, 8423360922633152610], [11989259058781683633, "dunce", false, 4995169038050421145], [12565293087094287914, "window_vibrancy", false, 7684782289404453759], [12832915883349295919, "serde_json", false, 13273047758056896886], [12986574360607194341, "serde_repr", false, 7093778461026560066], [13066042571740262168, "log", false, 9401519235968876370], [13077543566650298139, "heck", false, 13656739892825970954], [13548984313718623784, "serde", false, 11532597960971313909], [16727543399706004146, "cookie", false, 12653387793131356403], [16928111194414003569, "dirs", false, 11926194694332411210], [17233053221795943287, "tauri_utils", false, 7505252598482244554], [17531218394775549125, "tokio", false, 6085819188307862357], [18010483002580779355, "tauri_runtime", false, 13461307212006038841], [18035788301859549979, "tray_icon", false, 12643225622224371339]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-72f7831306cf1264/dep-lib-tauri", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}