{"rustc": 9783247635632824192, "features": "[]", "declared_features": "[]", "target": 11842472800422696103, "profile": 8276155916380437441, "path": 12057939383722008934, "deps": [[806484002035136204, "gethostname", false, 1878722843588336211], [1260461579271933187, "serialize_to_javascript", false, 7176914702523119901], [4336745513838352383, "thiserror", false, 3311293667150545164], [5024769281214949041, "os_info", false, 5067960792500448448], [6160766086878524380, "build_script_build", false, 14101484136148245946], [8427153362654230442, "tauri", false, 1352465124042977984], [12832915883349295919, "serde_json", false, 13273047758056896886], [13066042571740262168, "log", false, 9401519235968876370], [13548984313718623784, "serde", false, 11532597960971313909], [14618885535728128396, "sys_locale", false, 16693546077626630119]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-os-b743622030a8d42a/dep-lib-tauri_plugin_os", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}