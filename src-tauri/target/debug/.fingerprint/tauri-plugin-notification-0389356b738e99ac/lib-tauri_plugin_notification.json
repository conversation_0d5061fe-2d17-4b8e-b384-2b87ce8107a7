{"rustc": 9783247635632824192, "features": "[]", "declared_features": "[\"win7-notifications\", \"windows-version\", \"windows7-compat\"]", "target": 11906320761866078153, "profile": 8276155916380437441, "path": 11215599585975678593, "deps": [[947818755262499932, "notify_rust", false, 1692364712085597907], [4336745513838352383, "thiserror", false, 3311293667150545164], [5404511084185685755, "url", false, 4589935766087210441], [8427153362654230442, "tauri", false, 1352465124042977984], [11916940916964035392, "rand", false, 8814820600473035641], [12832915883349295919, "serde_json", false, 13273047758056896886], [12986574360607194341, "serde_repr", false, 7093778461026560066], [13066042571740262168, "log", false, 9401519235968876370], [13548984313718623784, "serde", false, 11532597960971313909], [17653728621794763101, "build_script_build", false, 14602458184917747745], [18360501799614255111, "time", false, 8058612894377932912]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-notification-0389356b738e99ac/dep-lib-tauri_plugin_notification", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}