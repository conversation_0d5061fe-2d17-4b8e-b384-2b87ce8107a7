{"rustc": 9783247635632824192, "features": "[]", "declared_features": "[]", "target": 2977321560937920362, "profile": 8276155916380437441, "path": 10350449442202747418, "deps": [[2001188854233969420, "build_script_build", false, 4330860756040365346], [4336745513838352383, "thiserror", false, 3311293667150545164], [8427153362654230442, "tauri", false, 1352465124042977984], [11337703028400419576, "os_pipe", false, 16383743958966752092], [12832915883349295919, "serde_json", false, 13273047758056896886], [13066042571740262168, "log", false, 9401519235968876370], [13548984313718623784, "serde", false, 11532597960971313909], [13626897008533545915, "regex", false, 10593131971045519619], [14564311161534545801, "encoding_rs", false, 13203334814566468994], [15722096100444777195, "shared_child", false, 5266148011580057311], [16192041687293812804, "open", false, 8977129701033564647], [17531218394775549125, "tokio", false, 6085819188307862357]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-shell-e66b16714365116c/dep-lib-tauri_plugin_shell", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}