{"rustc": 9783247635632824192, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"sqlx-sqlite\", \"tokio\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"async-std\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"time\", \"tokio\", \"uuid\"]", "target": 961973412475639632, "profile": 3033921117576893, "path": 12628579342776682555, "deps": [[373107762698212489, "proc_macro2", false, 4169187560799585552], [530211389790465181, "hex", false, 17584963853385135338], [996810380461694889, "sqlx_core", false, 9969404580735296586], [2713742371683562785, "syn", false, 11042634058065162160], [3405707034081185165, "dotenvy", false, 6371866748695781605], [3722963349756955755, "once_cell", false, 1006271504547235677], [4352659168317596042, "tempfile", false, 14903588324999991075], [5404511084185685755, "url", false, 9558323642765243910], [8045585743974080694, "heck", false, 2838339553681422620], [9857275760291862238, "sha2", false, 6673023750161820564], [11082282709338087849, "quote", false, 797130458734673429], [11838249260056359578, "sqlx_sqlite", false, 10315079959700586249], [12170264697963848012, "either", false, 7550327027051306887], [12832915883349295919, "serde_json", false, 9060392238108009148], [13548984313718623784, "serde", false, 7361760517279585112], [17531218394775549125, "tokio", false, 15698161016720073900]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-core-51e69fa126b666e7/dep-lib-sqlx_macros_core", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}