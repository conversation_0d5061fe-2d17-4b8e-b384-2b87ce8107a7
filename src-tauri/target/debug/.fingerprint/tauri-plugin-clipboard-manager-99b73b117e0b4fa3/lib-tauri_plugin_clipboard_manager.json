{"rustc": 9783247635632824192, "features": "[]", "declared_features": "[]", "target": 16794383176018920266, "profile": 8276155916380437441, "path": 4819790985710778852, "deps": [[86246135597337767, "arboard", false, 12612146516034128681], [1797035611096599003, "build_script_build", false, 7011546398929048544], [4336745513838352383, "thiserror", false, 3311293667150545164], [8427153362654230442, "tauri", false, 1352465124042977984], [12832915883349295919, "serde_json", false, 13273047758056896886], [13066042571740262168, "log", false, 9401519235968876370], [13548984313718623784, "serde", false, 11532597960971313909]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-clipboard-manager-99b73b117e0b4fa3/dep-lib-tauri_plugin_clipboard_manager", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}