{"rustc": 9783247635632824192, "features": "[]", "declared_features": "[]", "target": 17935818335149578806, "profile": 8276155916380437441, "path": 3776479883476449576, "deps": [[4336745513838352383, "thiserror", false, 3311293667150545164], [4707735785701411121, "build_script_build", false, 18076976409204303924], [8427153362654230442, "tauri", false, 1352465124042977984], [12832915883349295919, "serde_json", false, 13273047758056896886], [12848154260885479101, "bitflags", false, 13526488534898140950], [13066042571740262168, "log", false, 9401519235968876370], [13548984313718623784, "serde", false, 11532597960971313909]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-plugin-window-state-47323dbf18c6d403/dep-lib-tauri_plugin_window_state", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}