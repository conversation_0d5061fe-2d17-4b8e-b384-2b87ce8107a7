{"rustc": 9783247635632824192, "features": "[\"_rt-tokio\", \"_tls-rustls\", \"chrono\", \"default\", \"json\", \"migrate\", \"sqlite\", \"uuid\"]", "declared_features": "[\"_rt-async-std\", \"_rt-tokio\", \"_tls-native-tls\", \"_tls-rustls\", \"bigdecimal\", \"bit-vec\", \"chrono\", \"default\", \"ipnetwork\", \"json\", \"mac_address\", \"migrate\", \"mysql\", \"postgres\", \"rust_decimal\", \"sqlite\", \"time\", \"uuid\"]", "target": 13494433325021527976, "profile": 3033921117576893, "path": 6290415407612000834, "deps": [[373107762698212489, "proc_macro2", false, 4169187560799585552], [996810380461694889, "sqlx_core", false, 9969404580735296586], [2713742371683562785, "syn", false, 11042634058065162160], [11082282709338087849, "quote", false, 797130458734673429], [15733334431800349573, "sqlx_macros_core", false, 10219348628788178104]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/sqlx-macros-8fd04224fe43b6a8/dep-lib-sqlx_macros", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}