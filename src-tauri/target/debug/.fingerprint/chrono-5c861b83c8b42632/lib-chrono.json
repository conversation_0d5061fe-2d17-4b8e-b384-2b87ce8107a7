{"rustc": 9783247635632824192, "features": "[\"alloc\", \"clock\", \"default\", \"iana-time-zone\", \"js-sys\", \"now\", \"oldtime\", \"serde\", \"std\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "declared_features": "[\"__internal_bench\", \"alloc\", \"arbitrary\", \"clock\", \"core-error\", \"default\", \"iana-time-zone\", \"js-sys\", \"libc\", \"now\", \"oldtime\", \"pure-rust-locales\", \"rkyv\", \"rkyv-16\", \"rkyv-32\", \"rkyv-64\", \"rkyv-validation\", \"serde\", \"std\", \"unstable-locales\", \"wasm-bindgen\", \"wasmbind\", \"winapi\", \"windows-link\"]", "target": 15315924755136109342, "profile": 8276155916380437441, "path": 4559734463277664533, "deps": [[5157631553186200874, "num_traits", false, 3870028567633807117], [12317487911761266689, "iana_time_zone", false, 17717246100150293685], [13548984313718623784, "serde", false, 11532597960971313909]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/chrono-5c861b83c8b42632/dep-lib-chrono", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}