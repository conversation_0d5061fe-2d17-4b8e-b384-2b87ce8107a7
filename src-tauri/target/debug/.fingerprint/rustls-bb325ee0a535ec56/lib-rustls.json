{"rustc": 9783247635632824192, "features": "[\"ring\", \"std\", \"tls12\"]", "declared_features": "[\"aws-lc-rs\", \"aws_lc_rs\", \"brotli\", \"custom-provider\", \"default\", \"fips\", \"hashbrown\", \"log\", \"logging\", \"prefer-post-quantum\", \"read_buf\", \"ring\", \"rustversion\", \"std\", \"tls12\", \"zlib\"]", "target": 4618819951246003698, "profile": 15030315195695952907, "path": 11878578119774949429, "deps": [[2078890231223437303, "<PERSON><PERSON><PERSON>", false, 9332756302209320308], [2883436298747778685, "pki_types", false, 2696535723871484470], [3722963349756955755, "once_cell", false, 6260706625711267324], [5491919304041016563, "ring", false, 18430393264337702552], [10931517880218440090, "build_script_build", false, 15552974331420442538], [12865141776541797048, "zeroize", false, 14835349611901552672], [17003143334332120809, "subtle", false, 9526187062215842510]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rustls-bb325ee0a535ec56/dep-lib-rustls", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}